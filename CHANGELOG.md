# Changelog

All notable changes to Portal Sukan Intra Kor Kesihatan DiRaja 2025 will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **Pagination System for Player Management**
  - Added pagination controls with 5 players per page
  - Smart page navigation with previous/next buttons
  - Page number buttons (max 5 visible) with intelligent positioning
  - Results counter showing "X to Y of Z results"
  - Auto-reset to page 1 when filters change
  - Backend pagination support in `getAllPlayers` API endpoint
  - Responsive pagination controls for mobile devices

### Enhanced
- **Player List Table Improvements**
  - Fixed Select component value prop issues (empty string to undefined)
  - Updated filter options to use "all" instead of empty string
  - Improved API response handling for backward compatibility
  - Enhanced error handling and loading states

### Fixed
- **Import Statement Cleanup**
  - Removed duplicate import statements in players.tsx
  - Fixed React component import errors
  - Cleaned up unused imports

### Technical
- **API Enhancements**
  - Updated `/api/admin/players/all` endpoint to support pagination
  - Added pagination metadata in API responses
  - Maintained backward compatibility with existing frontend code
  - Enhanced filter parameter handling

## [1.0.0] - 2025-01-15

### Added
- **Initial Release**
  - Complete Portal Sukan management system
  - Zone-based player registration
  - Multi-role authentication (Superadmin, Admin, Zone)
  - Dynamic CMS for content management
  - Match scheduling and results tracking
  - Responsive design with Tailwind CSS v4
  - Laravel 12 backend with React 19 frontend
  - Sanctum token authentication
  - Image upload and compression
  - Excel export functionality
  - Real-time notifications with react-hot-toast
  - Form validation with Zod
  - State management with TanStack Query

### Features
- **Player Management**
  - Zone-specific player registration
  - Multi-sport participation
  - Team leader assignment
  - Player status tracking
  - Advanced filtering and search

- **Zone Management**
  - Zone leader assignment
  - Custom zone branding
  - Zone-specific statistics
  - Profile management

- **Content Management**
  - Dynamic homepage content
  - Tournament results management
  - Gallery with image compression
  - News and announcements
  - Rules and regulations
  - Social media integration

- **Admin Panel**
  - Role-based access control
  - Comprehensive reporting
  - System settings management
  - User management
  - Sports management

### Security
- Laravel Sanctum token authentication
- Role-based access control
- CSRF protection
- Input validation and sanitization
- Secure file upload handling

### Performance
- Optimized database queries
- Image compression and optimization
- Lazy loading for components
- Efficient state management
- Responsive design for all devices
