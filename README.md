# Portal Sukan Intra Kor Kesihatan DiRaja 2025

<div align="center">

![Portal Sukan Logo](public/logo.svg)

**Sistem Pengurusan Tournament Sukan Komprehensif untuk Anggota Tentera**

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-12-red.svg)](https://laravel.com)
[![React](https://img.shields.io/badge/React-19-blue.svg)](https://reactjs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.7-blue.svg)](https://www.typescriptlang.org)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.1-38B2AC.svg)](https://tailwindcss.com)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-orange.svg)](https://mysql.com)

</div>

---

## 🎯 Tentang Projek

Portal Sukan Intra Kor Kesihatan DiRaja 2025 adalah sistem pengurusan tournament sukan yang direka khusus untuk anggota tentera. Sistem ini menyediakan platform lengkap untuk pendaftaran peserta, pengurusan perlawanan, dan pengurusan content secara dinamik.

### ✨ Ciri-ciri Utama

- **🏃‍♂️ Pengurusan Peserta Berasaskan Zon** - Pendaftaran dan pengurusan peserta mengikut zon dengan pagination
- **🏆 Sistem Tournament Lengkap** - Jadual perlawanan, keputusan, dan statistik real-time
- **📝 CMS Dinamik** - Pengurusan content website secara dinamik tanpa coding
- **👥 Pengurusan Multi-Peranan** - Superadmin, Admin, dan Zone dengan akses berbeza
- **📱 Responsive Design** - Optimized untuk desktop, tablet, dan mobile
- **🔒 Keselamatan Tinggi** - Sanctum token authentication dengan role-based access
- **📊 Laporan Komprehensif** - Statistik dan laporan prestasi real-time
- **🖼️ Pengurusan Media** - Upload dan compression gambar automatik
- **🔍 Carian dan Penapisan Lanjutan** - Filter peserta mengikut zon, sukan dengan pagination

### 🛠️ Teknologi

- **Frontend:** React 19 + TypeScript + Tailwind CSS v4 + Shadcn UI
- **Backend:** Laravel 12 + MySQL 8.0
- **Authentication:** Laravel Sanctum (Token-based)
- **State Management:** TanStack Query + React Context
- **Routing:** React Router DOM
- **Validation:** Zod
- **Notifications:** React Hot Toast

---

## 📚 Dokumentasi Lengkap

Dokumentasi lengkap tersedia di folder **[`docs/`](docs/README.md)**:

### 🏆 Dokumentasi Sukan
- **[🎯 Skema Pemarkahan](docs/SKEMA-PEMARKAHAN.md)** - Sistem pemarkahan lengkap untuk semua sukan
- **[📋 Peraturan Sukan](docs/SPORT-RULES.md)** - Peraturan lengkap semua sukan
- **[📅 Jadual Pertandingan](docs/JADUAL-PERTANDINGAN.md)** - Jadual lengkap kejohanan

### 📖 Dokumentasi Sistem
- **[📋 Dokumentasi Lengkap](docs/DOCUMENTATION.md)** - Panduan komprehensif sistem
- **[👤 Manual Pengguna](docs/USER-MANUAL.md)** - Panduan penggunaan untuk end-users
- **[👨‍💻 Developer Guide](docs/DEVELOPER-GUIDE.md)** - Panduan untuk developers
- **[📡 API Reference](docs/API-REFERENCE.md)** - Dokumentasi API lengkap
- **[⚙️ System Requirements](docs/SYSTEM-REQUIREMENTS.md)** - Keperluan sistem dan spesifikasi

### 🎨 Dokumentasi Design
- **[🎨 CMS Design Specification](docs/cms-design-specification.md)** - Spesifikasi design CMS

---

## 🆕 Recent Updates

### ✅ Latest Features (January 2025)
- **📄 Pagination System** - Added pagination for player management with 5 items per page
- **🔍 Enhanced Filtering** - Improved filter system with better Select component handling
- **🚀 Performance Optimization** - Optimized API responses and database queries
- **🐛 Bug Fixes** - Fixed import statement issues and component errors
- **📚 Documentation** - Updated comprehensive documentation and changelog

### 🔄 What's Next
- Advanced reporting dashboard
- Real-time match updates
- Mobile app development
- Enhanced analytics

---

## 🚀 Quick Start

### Prasyarat
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- NPM/Yarn

### Installation

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd intra-kor
   ```

2. **Install Dependencies**
   ```bash
   # Backend dependencies
   composer install

   # Frontend dependencies
   npm install
   ```

3. **Environment Setup**
   ```bash
   # Copy environment file
   cp .env.example .env

   # Generate application key
   php artisan key:generate

   # Configure database in .env file
   # DB_DATABASE=intra_kor
   # DB_USERNAME=root
   # DB_PASSWORD=
   ```

4. **Database Setup**
   ```bash
   # Run migrations
   php artisan migrate

   # Seed database with sample data
   php artisan db:seed

   # Create storage symlink
   php artisan storage:link
   ```

5. **Start Development Servers**
   ```bash
   # Start Laravel server
   php artisan serve

   # Start Vite dev server (in another terminal)
   npm run dev
   ```

6. **Access Application**
   - **Frontend:** http://localhost:3000
   - **Backend API:** http://localhost:8000/api

### Default Login Credentials

| Username | Password | Role | Description |
|----------|----------|------|-------------|
| superadmin | Abcd@1234 | superadmin | Full system access |
| admin | Abcd@1234 | admin | General admin access |
| zona | Abcd@1234 | zone | Zone A management |
| zonb | Abcd@1234 | zone | Zone B management |
| zonc | Abcd@1234 | zone | Zone C management |

**⚠️ Nota:** Semua pengguna mesti tukar kata laluan pada log masuk pertama.

---

## 🏗️ Struktur Projek

```
Portal Sukan/
├── 📁 app/                    # Laravel application
│   ├── Http/Controllers/Api/  # API controllers
│   └── Models/               # Eloquent models
├── 📁 database/
│   ├── migrations/           # Database migrations
│   └── seeders/             # Database seeders
├── 📁 resources/js/         # React frontend
│   ├── components/          # React components
│   ├── pages/              # Page components
│   ├── contexts/           # React contexts
│   └── types/              # TypeScript types
├── 📁 routes/
│   ├── api.php             # API routes
│   └── web.php             # Web routes
├── 📁 public/              # Public assets
├── 📁 storage/             # File storage
├── 📁 docs/                # Documentation
└── 📁 deployment/          # Deployment files
```

---

## 🎮 Penggunaan Asas

### 1. Log Masuk
1. Pergi ke halaman login
2. Masukkan username dan kata laluan
3. Tukar kata laluan pada log masuk pertama

### 2. Pengurusan Peserta
1. Pergi ke "Pengurusan Peserta"
2. Klik "Tambah Peserta"
3. Isi maklumat peserta dan pilih sukan
4. Simpan pendaftaran

### 3. Pengurusan Perlawanan
1. Pergi ke "Pengurusan Perlawanan"
2. Klik "Tambah Perlawanan"
3. Set jadual dan venue
4. Kemaskini keputusan setelah perlawanan

### 4. CMS Content
1. Pergi ke "CMS" > "Page Content"
2. Pilih halaman yang ingin diedit
3. Edit content dan upload gambar
4. Simpan perubahan

---

## 🚀 Deployment

### Production Deployment (cPanel)

1. **Upload Files**
   ```bash
   # Upload laravel-app/ to cPanel root
   # Upload public_html/ contents to public_html/
   ```

2. **Database Setup**
   ```bash
   # Create database in cPanel
   # Update .env with database credentials
   ```

3. **Run Migrations**
   ```bash
   cd laravel-app
   php artisan migrate --force
   php artisan db:seed --force
   ```

4. **Set Permissions**
   ```bash
   chmod -R 755 laravel-app/
   chmod -R 775 laravel-app/storage/
   chmod -R 775 laravel-app/bootstrap/cache/
   ```

Lihat [📋 Dokumentasi Lengkap](docs/DOCUMENTATION.md#deployment-guide) untuk panduan deployment yang terperinci.

---

## 🧪 Testing

```bash
# Run PHP tests
php artisan test

# Run frontend tests
npm run test

# Run linting
npm run lint

# Run type checking
npm run types
```

---

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

Lihat [👨‍💻 Developer Guide](docs/DEVELOPER-GUIDE.md) untuk panduan development yang lengkap.

---

## 📞 Support

### Dokumentasi
- **[📋 Dokumentasi Lengkap](docs/DOCUMENTATION.md)** - Panduan komprehensif
- **[👤 Manual Pengguna](docs/USER-MANUAL.md)** - Panduan penggunaan
- **[❓ Troubleshooting](docs/DOCUMENTATION.md#troubleshooting)** - Penyelesaian masalah

### Hubungi Support
- **Email:** <EMAIL>
- **Telefon:** 03-12345678
- **Waktu:** 8:00 AM - 5:00 PM (Isnin - Jumaat)

---

## 📄 License

Projek ini adalah hak milik Kor Kesihatan DiRaja dan digunakan untuk tujuan rasmi sahaja.

---

## 🙏 Acknowledgments

- **Kor Kesihatan DiRaja** - Client dan stakeholder utama
- **Laravel Community** - Framework backend yang powerful
- **React Community** - Library frontend yang modern
- **Tailwind CSS** - Framework CSS yang efficient
- **Shadcn UI** - Component library yang beautiful

---

<div align="center">

**Portal Sukan Intra Kor Kesihatan DiRaja 2025**
*Sistem Pengurusan Tournament Sukan Komprehensif*

Dikemaskini: Julai 2025 | Versi: 1.0.0

</div>
