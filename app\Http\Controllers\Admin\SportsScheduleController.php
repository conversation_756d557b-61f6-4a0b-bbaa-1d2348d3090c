<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SportsSchedule;
use App\Models\Sport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SportsScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = SportsSchedule::with(['sport', 'creator'])
            ->active()
            ->ordered();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('end_date', '<=', $request->end_date);
        }

        // Search by name if provided
        if ($request->filled('search')) {
            $query->where('schedule_name', 'like', '%' . $request->search . '%');
        }

        $schedules = $query->paginate($request->get('per_page', 10));

        return response()->json($schedules);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'schedule_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'main_venue' => 'nullable|string|max:255',
            'display_order' => 'nullable|integer|min:0',
        ]);

        $validated['created_by'] = Auth::id();
        $validated['display_order'] = $validated['display_order'] ?? 0;

        $schedule = SportsSchedule::create($validated);
        $schedule->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Sports schedule created successfully',
            'data' => $schedule
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SportsSchedule $sportsSchedule): JsonResponse
    {
        $sportsSchedule->load(['sport', 'creator', 'matches.sport', 'tournamentBrackets']);

        return response()->json([
            'data' => $sportsSchedule
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SportsSchedule $sportsSchedule): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'schedule_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'main_venue' => 'nullable|string|max:255',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $sportsSchedule->update($validated);
        $sportsSchedule->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Sports schedule updated successfully',
            'data' => $sportsSchedule
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SportsSchedule $sportsSchedule): JsonResponse
    {
        // Check if schedule has matches
        if ($sportsSchedule->matches()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete schedule with existing matches'
            ], 422);
        }

        $sportsSchedule->delete();

        return response()->json([
            'message' => 'Sports schedule deleted successfully'
        ]);
    }

    /**
     * Get sports for dropdown
     */
    public function getSports(): JsonResponse
    {
        $sports = Sport::active()->ordered()->get(['id', 'name']);

        return response()->json([
            'data' => $sports
        ]);
    }
}
