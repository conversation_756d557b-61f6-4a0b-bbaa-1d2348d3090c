<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TournamentBracket;
use App\Models\Sport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TournamentBracketController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = TournamentBracket::with(['sport', 'creator'])
            ->active();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by tournament type if provided
        if ($request->filled('tournament_type')) {
            $query->byTournamentType($request->tournament_type);
        }

        // Search by bracket name if provided
        if ($request->filled('search')) {
            $query->where('bracket_name', 'like', '%' . $request->search . '%');
        }

        $brackets = $query->paginate($request->get('per_page', 10));

        return response()->json($brackets);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'bracket_name' => 'required|string|max:255',
            'bracket_structure' => 'required|array',
            'tournament_type' => 'required|string|in:knockout,round_robin,swiss',
        ]);

        $validated['created_by'] = Auth::id();

        $bracket = TournamentBracket::create($validated);
        $bracket->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Tournament bracket created successfully',
            'data' => $bracket
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(TournamentBracket $tournamentBracket): JsonResponse
    {
        $tournamentBracket->load(['sport', 'creator', 'matches']);

        return response()->json([
            'data' => $tournamentBracket
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TournamentBracket $tournamentBracket): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'bracket_name' => 'required|string|max:255',
            'bracket_structure' => 'required|array',
            'tournament_type' => 'required|string|in:knockout,round_robin,swiss',
            'is_active' => 'boolean',
        ]);

        $tournamentBracket->update($validated);
        $tournamentBracket->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Tournament bracket updated successfully',
            'data' => $tournamentBracket
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TournamentBracket $tournamentBracket): JsonResponse
    {
        $tournamentBracket->delete();

        return response()->json([
            'message' => 'Tournament bracket deleted successfully'
        ]);
    }

    /**
     * Get sports for dropdown - only sports that have bracket matches
     */
    public function getSports(): JsonResponse
    {
        $sports = Sport::active()
            ->whereHas('matches', function($query) {
                $query->whereNotNull('bracket_round');
            })
            ->ordered()
            ->get(['id', 'name']);

        return response()->json([
            'sports' => $sports
        ]);
    }

    /**
     * Generate next round matches for tournament progression
     */
    public function generateNextRound(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
        ]);

        $sportId = $validated['sport_id'];

        // Get completed matches from current round
        $completedMatches = SportMatch::where('sport_id', $sportId)
            ->where('status', 'completed')
            ->whereNotNull('bracket_round')
            ->whereNotNull('winner_zone')
            ->where('winner_zone', '!=', 'draw')
            ->get();

        if ($completedMatches->isEmpty()) {
            return response()->json([
                'message' => 'No completed matches found to generate next round'
            ], 400);
        }

        // Group by bracket round
        $roundGroups = $completedMatches->groupBy('bracket_round');

        $roundMapping = [
            'First Round' => 'Quarter Final',
            'Quarter Final' => 'Semi Final',
            'Semi Final' => 'Final',
        ];

        $generatedMatches = [];

        foreach ($roundGroups as $currentRound => $matches) {
            $nextRound = $roundMapping[$currentRound] ?? null;

            if (!$nextRound) continue;

            // Group matches by pairs for next round
            $matchPairs = $matches->chunk(2);

            foreach ($matchPairs as $index => $pair) {
                if ($pair->count() < 2) continue;

                $match1 = $pair->first();
                $match2 = $pair->last();

                // Check if both matches are completed
                if (!$match1->winner_zone || !$match2->winner_zone) continue;

                $nextPosition = $index + 1;

                // Check if next round match already exists
                $existingMatch = SportMatch::where('sport_id', $sportId)
                    ->where('bracket_round', $nextRound)
                    ->where('bracket_position', $nextPosition)
                    ->first();

                if (!$existingMatch) {
                    // Create new match for next round
                    $newMatch = SportMatch::create([
                        'sport_id' => $sportId,
                        'sports_schedule_id' => $match1->sports_schedule_id,
                        'title' => $nextRound . ' - Match ' . $nextPosition,
                        'bracket_round' => $nextRound,
                        'bracket_position' => $nextPosition,
                        'zone_a' => $match1->winner_zone,
                        'zone_b' => $match2->winner_zone,
                        'match_date' => now()->addDays(1)->format('Y-m-d'),
                        'match_time' => '10:00:00',
                        'venue' => $match1->venue,
                        'status' => 'scheduled',
                        'created_by' => auth()->id(),
                    ]);

                    $generatedMatches[] = $newMatch;
                }
            }
        }

        return response()->json([
            'message' => count($generatedMatches) . ' matches generated for next round',
            'data' => $generatedMatches
        ]);
    }
}
