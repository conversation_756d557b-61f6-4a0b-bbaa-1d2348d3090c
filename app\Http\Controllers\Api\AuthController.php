<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        // Log login attempts only in development
        if (app()->environment('local', 'development')) {
            Log::info('=== API LOGIN ATTEMPT START ===');
            Log::info('Request data: ' . json_encode($request->only(['username', 'remember'])));
        }

        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
            'remember' => 'boolean',
        ]);

        // Find user by username
        $user = User::where('username', $request->username)->first();

        if (!$user) {
            if (app()->environment('local', 'development')) {
                Log::info('User not found');
            }
            throw ValidationException::withMessages([
                'username' => ['Username atau kata laluan tidak betul.'],
            ]);
        }

        if (app()->environment('local', 'development')) {
            Log::info('User found: ' . $user->username);
        }

        // Check password
        if (!Hash::check($request->password, $user->password)) {
            if (app()->environment('local', 'development')) {
                Log::info('Password check failed');
            }
            throw ValidationException::withMessages([
                'username' => ['Username atau kata laluan tidak betul.'],
            ]);
        }

        if (app()->environment('local', 'development')) {
            Log::info('Password check passed');
        }

        // Check if user is active
        if (!$user->is_active) {
            if (app()->environment('local', 'development')) {
                Log::info('User is not active');
            }
            throw ValidationException::withMessages([
                'username' => ['Akaun anda tidak aktif. Sila hubungi pentadbir.'],
            ]);
        }

        // Create Sanctum token
        $token = $user->createToken('auth_token')->plainTextToken;

        // Update last login
        $user->update(['last_login_at' => now()]);

        if (app()->environment('local', 'development')) {
            Log::info('Login successful for user: ' . $user->username);
            Log::info('Token created: ' . substr($token, 0, 20) . '...');
        }

        return response()->json([
            'message' => 'Login berjaya',
            'user' => [
                'id' => $user->id,
                'username' => $user->username,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'zone' => $user->zone,
                'must_change_password' => (bool) $user->must_change_password,
            ],
            'token' => $token,
            'must_change_password' => (bool) $user->must_change_password,
        ]);
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        try {
            $user = $request->user();

            if ($user && $user->currentAccessToken()) {
                $user->currentAccessToken()->delete();
                Log::info('Logout successful for user: ' . $user->username);
            }

            return response()->json([
                'message' => 'Logout berjaya'
            ]);
        } catch (\Exception $e) {
            Log::error('Logout error: ' . $e->getMessage());

            // Return success even if token deletion fails
            return response()->json([
                'message' => 'Logout berjaya'
            ]);
        }
    }

    /**
     * Get current user
     */
    public function user(Request $request)
    {
        $user = $request->user();

        return response()->json([
            'id' => $user->id,
            'username' => $user->username,
            'name' => $user->name,
            'email' => $user->email,
            'role' => $user->role,
            'zone' => $user->zone,
            'must_change_password' => (bool) $user->must_change_password,
        ]);
    }

    /**
     * Change password
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = $request->user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['Kata laluan semasa tidak betul.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
            'must_change_password' => false,
        ]);

        Log::info('Password changed successfully for user: ' . $user->username);

        return response()->json([
            'message' => 'Kata laluan berjaya dikemaskini',
            'user' => [
                'id' => $user->id,
                'username' => $user->username,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'zone' => $user->zone,
                'must_change_password' => false,
            ],
        ]);
    }
}
