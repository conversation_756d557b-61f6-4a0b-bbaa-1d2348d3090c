<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageUploadController extends Controller
{
    /**
     * Upload and process image for CMS
     */
    public function uploadCmsImage(Request $request)
    {
        // Check if GD extension is loaded
        if (!extension_loaded('gd')) {
            return response()->json([
                'success' => false,
                'message' => 'GD extension is not loaded'
            ], 500);
        }

        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp', // Remove size limit
            'section' => 'required|string',
            'width' => 'nullable|integer|min:100|max:4000',
            'height' => 'nullable|integer|min:100|max:4000',
            'quality' => 'nullable|integer|min:10|max:100'
        ]);

        try {
            $file = $request->file('image');
            $section = $request->input('section', 'general');
            $width = $request->input('width', 1920);
            $height = $request->input('height', 1080);
            $quality = $request->input('quality', 80);

            // Get original file extension
            $originalExtension = strtolower($file->getClientOriginalExtension());

            // Generate unique filename with appropriate extension (preserve PNG, convert others to JPG)
            $outputExtension = ($originalExtension === 'png') ? 'png' : 'jpg';
            $filename = $section . '_' . time() . '_' . uniqid() . '.' . $outputExtension;
            
            // Create directory if not exists
            $uploadPath = storage_path('app/public/cms-images');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Use native GD library for image processing
            $tempPath = $file->getPathname();
            $originalSize = getimagesize($tempPath);

            if (!$originalSize) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid image file'
                ], 400);
            }

            $originalWidth = $originalSize[0];
            $originalHeight = $originalSize[1];

            // Create image resource based on type with error handling
            $source = false;
            switch ($originalSize['mime']) {
                case 'image/jpeg':
                    $source = @imagecreatefromjpeg($tempPath);
                    break;
                case 'image/png':
                    // Suppress warnings for PNG with sRGB profile issues
                    error_reporting(E_ERROR | E_PARSE);
                    $source = @imagecreatefrompng($tempPath);
                    error_reporting(E_ALL);
                    break;
                case 'image/gif':
                    $source = @imagecreatefromgif($tempPath);
                    break;
                case 'image/webp':
                    $source = @imagecreatefromwebp($tempPath);
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Unsupported image format'
                    ], 400);
            }

            if (!$source) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create image resource'
                ], 400);
            }

            // Create new image with target dimensions
            $target = imagecreatetruecolor($width, $height);

            // Handle transparency for PNG images
            if ($originalSize['mime'] === 'image/png') {
                // Enable alpha blending and save alpha channel
                imagealphablending($target, false);
                imagesavealpha($target, true);

                // Create transparent background
                $transparent = imagecolorallocatealpha($target, 0, 0, 0, 127);
                imagefill($target, 0, 0, $transparent);

                // Enable alpha blending for copying
                imagealphablending($target, true);
            }

            // Calculate crop dimensions to maintain aspect ratio
            $aspectRatio = $originalWidth / $originalHeight;
            $targetAspectRatio = $width / $height;

            if ($aspectRatio > $targetAspectRatio) {
                // Image is wider - crop width
                $newHeight = $originalHeight;
                $newWidth = $originalHeight * $targetAspectRatio;
                $srcX = ($originalWidth - $newWidth) / 2;
                $srcY = 0;
            } else {
                // Image is taller - crop height
                $newWidth = $originalWidth;
                $newHeight = $originalWidth / $targetAspectRatio;
                $srcX = 0;
                $srcY = ($originalHeight - $newHeight) / 2;
            }

            // Resize and crop
            imagecopyresampled($target, $source, 0, 0, $srcX, $srcY, $width, $height, $newWidth, $newHeight);

            // Save based on original format to preserve transparency
            if ($originalSize['mime'] === 'image/png') {
                // Disable alpha blending and save alpha channel for PNG output
                imagealphablending($target, false);
                imagesavealpha($target, true);
                imagepng($target, $uploadPath . '/' . $filename, 9); // 9 is max compression for PNG
            } else {
                // Save as JPEG for other formats
                imagejpeg($target, $uploadPath . '/' . $filename, $quality);
            }

            // Clean up memory
            imagedestroy($source);
            imagedestroy($target);
            
            // Generate URL
            $url = '/storage/cms-images/' . $filename;
            
            // Get file size after compression
            $fileSize = filesize($uploadPath . '/' . $filename);
            $fileSizeKB = round($fileSize / 1024, 2);
            
            return response()->json([
                'success' => true,
                'message' => 'Image uploaded and processed successfully',
                'data' => [
                    'url' => $url,
                    'filename' => $filename,
                    'original_size' => [
                        'width' => $originalWidth,
                        'height' => $originalHeight
                    ],
                    'processed_size' => [
                        'width' => $width,
                        'height' => $height
                    ],
                    'file_size_kb' => $fileSizeKB,
                    'quality' => $quality,
                    'uploaded_at' => now()->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Image upload failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get image info and generate different sizes
     */
    public function getImageInfo(Request $request)
    {
        $request->validate([
            'url' => 'required|url'
        ]);

        try {
            $url = $request->input('url');
            
            // Try to get image info
            $imageInfo = getimagesize($url);
            
            if (!$imageInfo) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unable to get image information'
                ], 400);
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'type' => $imageInfo['mime'],
                    'aspect_ratio' => round($imageInfo[0] / $imageInfo[1], 2),
                    'recommended_sizes' => [
                        'hero' => ['width' => 1920, 'height' => 1080],
                        'card' => ['width' => 800, 'height' => 600],
                        'thumbnail' => ['width' => 400, 'height' => 300]
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get image info: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process existing image URL
     */
    public function processImageUrl(Request $request)
    {
        // Check if GD extension is loaded
        if (!extension_loaded('gd')) {
            return response()->json([
                'success' => false,
                'message' => 'GD extension is not loaded'
            ], 500);
        }

        $request->validate([
            'url' => 'required|url',
            'section' => 'required|string',
            'width' => 'nullable|integer|min:100|max:4000',
            'height' => 'nullable|integer|min:100|max:4000',
            'quality' => 'nullable|integer|min:10|max:100'
        ]);

        try {
            $url = $request->input('url');
            $section = $request->input('section', 'general');
            $width = $request->input('width', 1920);
            $height = $request->input('height', 1080);
            $quality = $request->input('quality', 80);

            // Validate URL accessibility
            $headers = @get_headers($url);
            if (!$headers || strpos($headers[0], '200') === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unable to access the image URL'
                ], 400);
            }

            // Generate unique filename
            $filename = $section . '_' . time() . '_' . uniqid() . '.jpg';

            // Create directory if not exists
            $uploadPath = storage_path('app/public/cms-images');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Download and process image
            $manager = new ImageManager(new Driver());

            // Try to read the image with better error handling
            try {
                $image = $manager->read($url);
            } catch (\Exception $e) {
                \Log::error('Image decode error: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Unable to decode image: ' . $e->getMessage()
                ], 400);
            }

            // Get original dimensions
            $originalWidth = $image->width();
            $originalHeight = $image->height();

            // Calculate aspect ratio and resize
            $aspectRatio = $originalWidth / $originalHeight;
            $targetAspectRatio = $width / $height;

            if ($aspectRatio > $targetAspectRatio) {
                $image->scale(height: $height);
                $image->crop($width, $height);
            } else {
                $image->scale(width: $width);
                $image->crop($width, $height);
            }

            // Apply compression and save
            $image->toJpeg($quality)->save($uploadPath . '/' . $filename);
            
            // Generate URL
            $processedUrl = '/storage/cms-images/' . $filename;
            
            // Get file size after compression
            $fileSize = filesize($uploadPath . '/' . $filename);
            $fileSizeKB = round($fileSize / 1024, 2);
            
            return response()->json([
                'success' => true,
                'message' => 'Image processed successfully',
                'data' => [
                    'url' => $processedUrl,
                    'filename' => $filename,
                    'original_url' => $url,
                    'original_size' => [
                        'width' => $originalWidth,
                        'height' => $originalHeight
                    ],
                    'processed_size' => [
                        'width' => $width,
                        'height' => $height
                    ],
                    'file_size_kb' => $fileSizeKB,
                    'quality' => $quality,
                    'processed_at' => now()->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Image processing failed', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to process image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete uploaded image
     */
    public function deleteImage(Request $request)
    {
        $request->validate([
            'filename' => 'required|string'
        ]);

        try {
            $filename = $request->input('filename');
            $filePath = 'public/cms-images/' . $filename;
            
            if (Storage::exists($filePath)) {
                Storage::delete($filePath);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found'
                ], 404);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test system requirements for image processing
     */
    public function testSystem()
    {
        $tests = [];

        // Check GD extension
        $tests['gd_extension'] = extension_loaded('gd');

        // Check storage directory
        $uploadPath = storage_path('app/public/cms-images');
        $tests['storage_writable'] = is_writable(dirname($uploadPath)) || is_writable($uploadPath);

        // Check if we can create directory
        if (!file_exists($uploadPath)) {
            try {
                mkdir($uploadPath, 0755, true);
                $tests['can_create_directory'] = true;
            } catch (\Exception $e) {
                $tests['can_create_directory'] = false;
                $tests['directory_error'] = $e->getMessage();
            }
        } else {
            $tests['can_create_directory'] = true;
        }

        // Test Intervention Image
        try {
            $manager = new ImageManager(new Driver());
            $tests['intervention_image'] = true;
        } catch (\Exception $e) {
            $tests['intervention_image'] = false;
            $tests['intervention_error'] = $e->getMessage();
        }

        return response()->json([
            'success' => true,
            'tests' => $tests
        ]);
    }
}
