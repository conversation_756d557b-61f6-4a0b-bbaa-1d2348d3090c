<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GameMatch;
use App\Models\Sport;
use App\Models\Zone;
use Illuminate\Http\Request;

class MatchesController extends Controller
{
    /**
     * Display a listing of matches
     */
    public function index(Request $request)
    {
        $query = GameMatch::with(['sport']);

        // Filter by sport if specified
        if ($request->has('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by status if specified
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('date_from')) {
            $query->whereDate('match_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('match_date', '<=', $request->date_to);
        }

        // Filter by zone (if user is zone admin or specific zone requested)
        if ($request->has('zone')) {
            $zone = $request->zone;
            $query->where(function ($q) use ($zone) {
                $q->where('zone_a', $zone)
                  ->orWhere('zone_b', $zone);
            });
        }

        $matches = $query->orderBy('match_date')->orderBy('match_time')->get();

        // Transform the data
        $matches = $matches->map(function ($match) {
            // Get zone names
            $zoneA = Zone::where('code', $match->zone_a)->first();
            $zoneB = Zone::where('code', $match->zone_b)->first();
            $winnerZone = $match->winner_zone ? Zone::where('code', $match->winner_zone)->first() : null;

            return [
                'id' => $match->id,
                'title' => $match->title,
                'sport' => $match->sport ? $match->sport->name : null,
                'sport_id' => $match->sport_id,
                'date' => $match->match_date,
                'time' => $match->match_time,
                'venue' => $match->venue,
                'team_a' => $zoneA ? $zoneA->name : $match->zone_a,
                'team_b' => $zoneB ? $zoneB->name : $match->zone_b,
                'zone_a' => $match->zone_a,
                'zone_b' => $match->zone_b,
                'status' => $match->status,
                'score_a' => $match->score_zone_a,
                'score_b' => $match->score_zone_b,
                'winner' => $winnerZone ? $winnerZone->name : $match->winner_zone,
                'winner_zone' => $match->winner_zone,
                'notes' => $match->notes,
            ];
        });

        return response()->json($matches);
    }

    /**
     * Store a newly created match
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'title' => 'nullable|string|max:255',
            'zone_a' => 'required|string|max:20',
            'zone_b' => 'required|string|max:20',
            'match_date' => 'required|date',
            'match_time' => 'required|string',
            'venue' => 'required|string|max:255',
            'status' => 'required|in:scheduled,ongoing,completed,cancelled',
        ]);

        // Only auto-generate title if not provided
        if (empty($validated['title'])) {
            $validated['title'] = $validated['zone_a'] . ' vs ' . $validated['zone_b'];
        }
        $validated['created_by'] = auth()->id();

        $match = GameMatch::create($validated);
        $match->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Perlawanan berjaya dijadualkan',
            'match' => $match
        ], 201);
    }

    /**
     * Display the specified match
     */
    public function show($id)
    {
        $match = GameMatch::with(['sport', 'creator'])->findOrFail($id);

        return response()->json($match);
    }

    /**
     * Update the specified match
     */
    public function update(Request $request, $id)
    {
        $match = GameMatch::findOrFail($id);

        $validated = $request->validate([
            'sport_id' => 'required|exists:sports,id',
            'title' => 'nullable|string|max:255',
            'zone_a' => 'required|string|max:20',
            'zone_b' => 'required|string|max:20',
            'match_date' => 'required|date',
            'match_time' => 'required|string',
            'venue' => 'required|string|max:255',
            'status' => 'required|in:scheduled,ongoing,completed,cancelled',
        ]);

        // Only auto-generate title if not provided
        if (empty($validated['title'])) {
            $validated['title'] = $validated['zone_a'] . ' vs ' . $validated['zone_b'];
        }

        $match->update($validated);
        $match->load(['sport', 'creator']);

        return response()->json([
            'message' => 'Maklumat perlawanan berjaya dikemaskini',
            'match' => $match
        ]);
    }

    /**
     * Remove the specified match
     */
    public function destroy($id)
    {
        $match = GameMatch::findOrFail($id);
        $match->delete();

        return response()->json([
            'message' => 'Perlawanan berjaya dipadamkan'
        ]);
    }

    /**
     * Get match statistics
     */
    public function getStats(Request $request)
    {
        // Mock stats - replace with actual database queries
        $stats = [
            'total' => 45,
            'scheduled' => 8,
            'ongoing' => 2,
            'completed' => 32,
            'cancelled' => 3,
            'by_sport' => [
                'Badminton' => 12,
                'Football' => 10,
                'Netball' => 8,
                'Basketball' => 7,
                'Volleyball' => 8,
            ],
            'by_venue' => [
                'Dewan Serbaguna A' => 15,
                'Padang Bola' => 10,
                'Gelanggang Netball' => 8,
                'Gelanggang Basketball' => 7,
                'Dewan Serbaguna B' => 5,
            ]
        ];

        return response()->json($stats);
    }
}
