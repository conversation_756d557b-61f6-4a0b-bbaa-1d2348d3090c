<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PageContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PageContentController extends Controller
{
    /**
     * Get all page content
     */
    public function index(Request $request)
    {
        $content = PageContent::orderBy('page')
            ->orderBy('sort_order')
            ->orderBy('section')
            ->orderBy('content_key')
            ->get();

        return response()->json($content);
    }

    /**
     * Store new page content
     */
    public function store(Request $request)
    {
        $request->validate([
            'page' => 'required|string',
            'section' => 'nullable|string',
            'content_key' => 'required|string',
            'content_value' => 'required|string',
            'content_type' => 'required|in:text,textarea,image,url',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer',
        ]);

        try {
            $user = auth()->user();
            $userId = $user ? $user->getAttribute('id') : null;

            \Log::info('PageContent store - User debug', [
                'user_exists' => $user ? true : false,
                'user_id' => $userId,
                'user_username' => $user ? $user->username : null
            ]);

            // Use updateOrCreate to handle duplicates
            $pageContent = PageContent::updateOrCreate(
                [
                    'page' => $request->page,
                    'section' => $request->section,
                    'content_key' => $request->content_key,
                ],
                [
                    'content_value' => $request->content_value,
                    'content_type' => $request->content_type,
                    'description' => $request->description,
                    'is_active' => $request->get('is_active', true),
                    'sort_order' => $request->get('sort_order', 1),
                    'updated_by' => $userId,
                ]
            );

            return response()->json([
                'message' => 'Kandungan berjaya disimpan',
                'content' => $pageContent
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal menyimpan kandungan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display specific page content
     */
    public function show($id)
    {
        $content = PageContent::findOrFail($id);
        return response()->json($content);
    }

    /**
     * Update page content
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'content_value' => 'required|string|max:65535',
            'description' => 'nullable|string|max:1000',
        ]);

        try {
            $pageContent = PageContent::findOrFail($id);

            // Use DB::table to avoid model events and authentication issues
            $updateData = [
                'content_value' => $request->content_value,
                'updated_at' => now()
            ];

            if ($request->has('description')) {
                $updateData['description'] = $request->description;
            }

            // Update only the required fields without touching updated_by
            \DB::table('page_content')
                ->where('id', $id)
                ->update($updateData);

            // Get fresh data after update
            $updatedContent = PageContent::find($id);

            return response()->json([
                'message' => 'Kandungan berjaya dikemaskini',
                'content' => $updatedContent
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'message' => 'Kandungan tidak dijumpai'
            ], 404);
        } catch (\Illuminate\Database\QueryException $e) {
            \Log::error('Database error in PageContent update', [
                'id' => $id,
                'error' => $e->getMessage(),
                'sql' => $e->getSql() ?? 'N/A',
                'bindings' => $e->getBindings() ?? []
            ]);

            return response()->json([
                'message' => 'Ralat pangkalan data: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            \Log::error('PageContent update error', [
                'id' => $id,
                'request' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Gagal mengemaskini kandungan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete page content
     */
    public function destroy($id)
    {
        // Mock response - replace with actual database deletion
        return response()->json([
            'message' => 'Kandungan berjaya dipadamkan'
        ]);
    }

    /**
     * Get content for a specific page
     */
    public function getPageContent($page)
    {
        $content = PageContent::where('page', $page)
            ->orderBy('sort_order')
            ->orderBy('section')
            ->orderBy('content_key')
            ->get()
            ->groupBy('section')
            ->map(function ($sectionContent) {
                return $sectionContent->pluck('content_value', 'content_key');
            });

        return response()->json($content);
    }

    /**
     * Get specific content by page, section, and key
     */
    public function getContent($page, $section = null, $key = null)
    {
        $query = PageContent::where('page', $page);

        if ($section) {
            $query->where('section', $section);
        }

        if ($key) {
            $query->where('content_key', $key);
            $content = $query->first();
            return response()->json(['content' => $content ? $content->content_value : null]);
        }

        $content = $query->get();

        if ($section) {
            // Return as key-value pairs for specific section
            $content = $content->pluck('content_value', 'content_key');
        } else {
            // Group by section for entire page
            $content = $content->groupBy('section')->map(function ($sectionContent) {
                return $sectionContent->pluck('content_value', 'content_key');
            });
        }

        return response()->json(['content' => $content]);
    }

    /**
     * Update content for entire page
     */
    public function updatePageContent(Request $request, $page)
    {
        $request->validate([
            'content' => 'required|array',
        ]);

        // Mock response - replace with actual database update
        return response()->json([
            'message' => 'Kandungan halaman berjaya dikemaskini',
            'page' => $page,
            'content' => $request->content,
            'updated_at' => now()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get all homepage content for public access
     */
    public function getHomepageContent()
    {
        $content = PageContent::where('page', 'homepage')
            ->where('is_active', true)
            ->orderBy('section')
            ->orderBy('sort_order')
            ->get();

        // Group by section
        $grouped = $content->groupBy('section');

        // Transform to key-value pairs for each section
        $result = [];
        foreach ($grouped as $section => $items) {
            $result[$section] = [];
            foreach ($items as $item) {
                $result[$section][$item->content_key] = $item->content_value;
            }
        }

        return response()->json($result);
    }
}
