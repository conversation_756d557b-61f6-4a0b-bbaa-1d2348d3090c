<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Player;
use App\Models\Zone;
use App\Models\Sport;
use App\Models\RankCategory;
use Illuminate\Http\Request;

class PlayersController extends Controller
{
    /**
     * Check if zone has leader and sport team has leader (for zone admins)
     */
    public function checkZoneLeader(Request $request)
    {
        $user = $request->user();
        $sportTeamId = $request->get('sport_team_id');

        if ($user->role !== 'zone') {
            return response()->json(['hasLeader' => true]); // Admin can always add
        }

        $zone = \App\Models\Zone::where('code', $user->zone)->first();
        $hasZoneLeader = $zone && $zone->leader_id;

        if (!$hasZoneLeader) {
            return response()->json([
                'hasLeader' => false,
                'zoneName' => $zone ? $zone->name : null,
                'message' => 'Zon anda belum mempunyai ketua. Sila lantik ketua zon terlebih dahulu.'
            ]);
        }

        // If sport team ID provided, check if sport team has leader
        if ($sportTeamId) {
            $sportTeam = \App\Models\SportTeam::find($sportTeamId);
            if (!$sportTeam || !$sportTeam->team_leader_name) {
                return response()->json([
                    'hasLeader' => false,
                    'zoneName' => $zone->name,
                    'message' => 'Sukan ini belum mempunyai ketua pasukan. Sila lantik ketua pasukan terlebih dahulu.'
                ]);
            }
        }

        return response()->json([
            'hasLeader' => true,
            'zoneName' => $zone->name,
            'message' => null
        ]);
    }

    /**
     * Display a listing of players
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = Player::query();

        // Filter by zone if user is zone admin
        if ($user && $user->role === 'zone') {
            $query->where('zone', $user->zone);
        }

        // Filter by zone if specified in request
        if ($request->has('zone')) {
            $query->where('zone', $request->zone);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by sport
        if ($request->has('sport')) {
            $query->whereHas('sports', function ($q) use ($request) {
                $q->where('sports.id', $request->sport);
            });
        }

        // Filter by sport_id for rank-based queries
        if ($request->has('sport_id')) {
            $query->whereHas('sports', function ($q) use ($request) {
                $q->where('sports.id', $request->sport_id);
            });
        }

        // Include rank category for rank-based sports
        if ($request->has('rank_based') && $request->boolean('rank_based')) {
            $query->with('rankCategory');
        }

        // Search by name, phone, or service number
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('service_number', 'like', "%{$search}%");
            });
        }

        // Add pagination with optimized query
        $perPage = $request->get('per_page', 5);
        $players = $query->with(['sports:id,name', 'rankCategory:id,name,code'])
                        ->select(['id', 'name', 'phone', 'zone', 'status', 'created_at', 'rank_category_id', 'player_type'])
                        ->orderBy('name')
                        ->paginate($perPage);

        // Transform the data with optimized structure
        $players->getCollection()->transform(function ($player) {
            return [
                'id' => $player->id,
                'name' => $player->name,
                'phone' => $player->phone,
                'zone' => $player->zone,
                'player_type' => $player->player_type,
                'sports' => $player->sports->map(function ($sport) {
                    return [
                        'id' => $sport->id,
                        'name' => $sport->name
                    ];
                }),
                'rank_category' => $player->rankCategory ? [
                    'id' => $player->rankCategory->id,
                    'name' => $player->rankCategory->name,
                    'code' => $player->rankCategory->code,
                ] : null,
                'status' => $player->status,
                'created_at' => $player->created_at->toISOString(),
            ];
        });

        return response()->json($players);
    }

    /**
     * Get all players list for superadmin/admin with filters
     */
    public function getAllPlayers(Request $request)
    {
        $user = $request->user();

        // Only superadmin and admin can access this endpoint
        if (!in_array($user->role, ['superadmin', 'admin'])) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        try {
            $query = Player::with(['sportTeam.sport', 'sportTeam.zone', 'rankCategory']);

            // Filter by zone if provided
            if ($request->has('zone') && $request->zone) {
                $query->whereHas('sportTeam', function ($q) use ($request) {
                    $q->where('zone_code', $request->zone);
                });
            }

            // Filter by sport if provided
            if ($request->has('sport') && $request->sport) {
                $query->whereHas('sportTeam.sport', function ($q) use ($request) {
                    $q->where('id', $request->sport);
                });
            }

            // Search by name if provided
            if ($request->has('search') && $request->search) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }

            // Add pagination
            $perPage = $request->get('per_page', 5);
            $players = $query->orderBy('name')->paginate($perPage);

            $formattedPlayers = $players->map(function ($player) {
                $sportTeam = $player->sportTeam;
                return [
                    'id' => $player->id,
                    'name' => $player->name,
                    'phone' => $player->phone,
                    'player_type' => $player->player_type,
                    'status' => $player->status,
                    'zone' => $sportTeam ? $sportTeam->zone_code : null,
                    'zone_name' => $sportTeam && $sportTeam->zone ? $sportTeam->zone->name : null,
                    'sport' => $sportTeam && $sportTeam->sport ? $sportTeam->sport->name : null,
                    'sport_id' => $sportTeam && $sportTeam->sport ? $sportTeam->sport->id : null,
                    'team_leader_name' => $sportTeam ? $sportTeam->team_leader_name : null,
                    'team_leader_phone' => $sportTeam ? $sportTeam->team_leader_phone : null,
                    'rank_category_id' => $player->rank_category_id,
                    'rank_category' => $player->rankCategory ? [
                        'id' => $player->rankCategory->id,
                        'name' => $player->rankCategory->name,
                        'code' => $player->rankCategory->code,
                    ] : null,
                    'created_at' => $player->created_at->format('d/m/Y'),
                ];
            });

            return response()->json([
                'data' => $formattedPlayers,
                'current_page' => $players->currentPage(),
                'last_page' => $players->lastPage(),
                'per_page' => $players->perPage(),
                'total' => $players->total(),
                'from' => $players->firstItem(),
                'to' => $players->lastItem(),
                // Keep backward compatibility
                'players' => $formattedPlayers,
            ]);
        } catch (\Exception $e) {
            \Log::error('PlayersController@getAllPlayers exception', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return response()->json([
                'message' => 'Gagal memuat data peserta',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get filter options for players list (zones and sports)
     */
    public function getFilterOptions(Request $request)
    {
        $user = $request->user();

        // Only superadmin and admin can access this endpoint
        if (!in_array($user->role, ['superadmin', 'admin'])) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        try {
            // Get all zones
            $zones = \App\Models\Zone::select('code', 'name')->orderBy('name')->get();

            // Get all sports
            $sports = \App\Models\Sport::select('id', 'name')->where('is_active', true)->orderBy('name')->get();

            return response()->json([
                'zones' => $zones,
                'sports' => $sports,
            ]);
        } catch (\Exception $e) {
            \Log::error('PlayersController@getFilterOptions exception', [
                'message' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Gagal memuat pilihan filter',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created player
     */
    public function store(Request $request)
    {
        $user = $request->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'sport_team_id' => 'required|integer|exists:sport_teams,id',
            'player_type' => 'required|in:main,substitute',
            'rank_category_id' => 'required|integer|exists:rank_categories,id',
            'address' => 'nullable|string',
        ]);

        // Get sport team and validate permissions
        $sportTeam = \App\Models\SportTeam::findOrFail($request->sport_team_id);

        // Zone admin can only add players to their own zone
        if ($user->role === 'zone' && $sportTeam->zone_code !== $user->zone) {
            return response()->json([
                'message' => 'Anda hanya boleh menambah peserta ke zon anda sendiri.'
            ], 403);
        }

        // Check zone leader requirement for zone admin
        if ($user->role === 'zone') {
            $zone = \App\Models\Zone::where('code', $user->zone)->first();
            if (!$zone || !$zone->leader_id) {
                return response()->json([
                    'message' => 'Zon anda belum mempunyai ketua. Sila lantik ketua zon terlebih dahulu.'
                ], 403);
            }
        }

        // Check if sport team has leader
        if (!$sportTeam->team_leader_name) {
            return response()->json([
                'message' => 'Sukan ini belum mempunyai ketua pasukan. Sila lantik ketua pasukan terlebih dahulu.'
            ], 403);
        }

        try {
            $player = Player::create([
                'name' => $request->name,
                'zone' => $sportTeam->zone_code,
                'status' => 'active',
                'is_active' => true,
                'player_type' => $request->player_type,
                'sport_team_id' => $request->sport_team_id,
                'rank_category_id' => $request->rank_category_id,
                'address' => $request->address,
                'registered_by' => $user->getAttribute('id'),
            ]);

            // Attach sport to player via pivot table
            $player->sports()->attach($sportTeam->sport_id, [
                'sport_team_id' => $sportTeam->id
            ]);

            // Load sport team and sport info
            $player->load(['sportTeam.sport']);

            return response()->json([
                'message' => 'Peserta berjaya didaftarkan',
                'player' => [
                    'id' => $player->id,
                    'name' => $player->name,
                    'phone' => $player->phone,
                    'zone' => $player->zone,
                    'status' => $player->status,
                    'player_type' => $player->player_type,
                    'sport' => [
                        'id' => $player->sportTeam->sport->id,
                        'name' => $player->sportTeam->sport->name
                    ],
                    'created_at' => $player->created_at->toISOString(),
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mendaftarkan peserta',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified player
     */
    public function show($id)
    {
        $player = Player::findOrFail($id);

        return response()->json([
            'id' => $player->id,
            'name' => $player->name,
            'service_number' => $player->service_number,
            'phone' => $player->phone,
            'zone' => $player->zone,
            'rank' => $player->rank,
            'rank_category_id' => $player->rank_category_id,
            'unit' => $player->unit,
            'status' => $player->is_active ? 'active' : 'inactive',
            'registered_at' => $player->created_at->format('Y-m-d'),
            'emergency_contact' => $player->emergency_contact,
            'emergency_phone' => $player->emergency_phone,
            'medical_conditions' => $player->medical_conditions,
        ]);
    }

    /**
     * Update the specified player
     */
    public function update(Request $request, $id)
    {
        \Log::info('Update Player Request', [
            'player_id' => $id,
            'request_data' => $request->all(),
            'user_id' => $request->user()->id,
            'user_zone' => $request->user()->zone
        ]);

        $user = $request->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'player_type' => 'required|in:main,substitute',
            'rank_category_id' => 'required|integer|exists:rank_categories,id',
        ]);

        $player = \App\Models\Player::findOrFail($id);

        // Zone admin can only update players in their own zone
        if ($user->role === 'zone') {
            // Check player zone directly first (for old players without sport_team_id)
            if ($player->zone !== $user->zone) {
                return response()->json([
                    'message' => 'Anda hanya boleh mengemaskini peserta dalam zon anda sendiri.'
                ], 403);
            }

            // If player has sport_team_id, also check sport team zone for consistency
            if ($player->sport_team_id) {
                $sportTeam = $player->sportTeam;
                if ($sportTeam && $sportTeam->zone_code !== $user->zone) {
                    return response()->json([
                        'message' => 'Anda hanya boleh mengemaskini peserta dalam zon anda sendiri.'
                    ], 403);
                }
            }
        }

        $player->update([
            'name' => $request->name,
            'player_type' => $request->player_type,
            'rank_category_id' => $request->rank_category_id,
        ]);

        \Log::info('Player Updated Successfully', [
            'player_id' => $player->id,
            'updated_data' => $player->fresh()->toArray()
        ]);

        return response()->json([
            'message' => 'Maklumat pemain berjaya dikemaskini',
            'player' => [
                'id' => $player->id,
                'name' => $player->name,
                'phone' => $player->phone,
                'player_type' => $player->player_type,
                'status' => $player->status,
                'updated_at' => $player->updated_at->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Remove the specified player
     */
    public function destroy(Request $request, $id)
    {
        \Log::info('Delete Player Request', [
            'player_id' => $id,
            'user_id' => $request->user()->id,
            'user_zone' => $request->user()->zone
        ]);

        $user = $request->user();
        $player = \App\Models\Player::findOrFail($id);

        // Zone admin can only delete players in their own zone
        if ($user->role === 'zone') {
            // Check player zone directly first (for old players without sport_team_id)
            if ($player->zone !== $user->zone) {
                return response()->json([
                    'message' => 'Anda hanya boleh memadamkan peserta dalam zon anda sendiri.'
                ], 403);
            }

            // If player has sport_team_id, also check sport team zone for consistency
            if ($player->sport_team_id) {
                $sportTeam = $player->sportTeam;
                if ($sportTeam && $sportTeam->zone_code !== $user->zone) {
                    return response()->json([
                        'message' => 'Anda hanya boleh memadamkan peserta dalam zon anda sendiri.'
                    ], 403);
                }
            }
        }

        $player->delete();

        \Log::info('Player Deleted Successfully', [
            'player_id' => $id,
            'player_name' => $player->name
        ]);

        return response()->json([
            'message' => 'Pemain berjaya dipadamkan'
        ]);
    }

    /**
     * Get player statistics
     */
    public function getStats(Request $request)
    {
        $user = $request->user();

        // Mock stats - replace with actual database queries
        $stats = [
            'total' => 150,
            'active' => 145,
            'inactive' => 5,
            'by_zone' => [
                'Zone A' => 50,
                'Zone B' => 55,
                'Zone C' => 45,
            ],
            'by_sport' => [
                'Badminton' => 45,
                'Football' => 40,
                'Netball' => 35,
                'Basketball' => 30,
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Store a rank-based player (for sports like Larian)
     */
    public function storeRankBased(Request $request)
    {
        $user = $request->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'sport_id' => 'required|integer|exists:sports,id',
            'rank_category_id' => 'required|integer|exists:rank_categories,id',
        ]);

        // Get sport and validate it's rank-based
        $sport = \App\Models\Sport::findOrFail($request->sport_id);
        if ($sport->sport_type !== 'rank_based') {
            return response()->json([
                'message' => 'Sukan ini tidak menggunakan sistem kategori pangkat.'
            ], 400);
        }

        try {
            $player = Player::create([
                'name' => $request->name,
                'rank_category_id' => $request->rank_category_id,
                'zone' => $user->zone, // Add zone from user
                'status' => 'active',
                'is_active' => true,
                'registered_by' => $user->getAttribute('id'),
            ]);

            // Attach sport to player via pivot table
            $player->sports()->attach($request->sport_id);

            return response()->json([
                'message' => 'Peserta berjaya ditambah',
                'player' => $player->load('rankCategory')
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal menambah peserta',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
