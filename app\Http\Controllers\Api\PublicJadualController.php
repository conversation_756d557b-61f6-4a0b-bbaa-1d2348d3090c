<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SportMatch;
use App\Models\SportsSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PublicJadualController extends Controller
{
    /**
     * Get all sports schedules for public display
     */
    public function getSportsSchedules(Request $request): JsonResponse
    {
        $query = SportsSchedule::with(['sport', 'matches' => function($query) {
                $query->with('sport')->orderBy('match_date')->orderBy('match_time');
            }])
            ->active()
            ->ordered();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('end_date', '<=', $request->end_date);
        }

        $schedules = $query->get();

        return response()->json([
            'data' => $schedules
        ]);
    }

    /**
     * Get all matches for public display
     */
    public function getMatches(Request $request): JsonResponse
    {
        $query = SportMatch::with(['sport', 'sportsSchedule'])
            ->orderedByDateTime();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by sports schedule if provided
        if ($request->filled('sports_schedule_id')) {
            $query->where('sports_schedule_id', $request->sports_schedule_id);
        }

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->where('match_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('match_date', '<=', $request->end_date);
        }

        // Search by title if provided
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // Only show non-cancelled matches for public
        $query->where('status', '!=', 'cancelled');

        $matches = $query->get();

        return response()->json([
            'data' => $matches
        ]);
    }

    /**
     * Get matches by schedule for public display
     */
    public function getMatchesBySchedule(Request $request, $scheduleId): JsonResponse
    {
        $schedule = SportsSchedule::with(['sport'])->findOrFail($scheduleId);

        $matches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('sports_schedule_id', $scheduleId)
            ->where('status', '!=', 'cancelled')
            ->orderedByBil()
            ->orderedByDateTime()
            ->get();

        return response()->json([
            'schedule' => $schedule,
            'matches' => $matches
        ]);
    }

    /**
     * Get tournament bracket matches for public display
     */
    public function getTournamentBrackets(Request $request): JsonResponse
    {
        $query = SportMatch::with(['sport', 'sportsSchedule'])
            ->whereNotNull('bracket_round')
            ->orderedByDateTime();

        // Filter by sport if provided
        if ($request->filled('sport_id')) {
            $query->where('sport_id', $request->sport_id);
        }

        // Filter by sports schedule if provided
        if ($request->filled('sports_schedule_id')) {
            $query->where('sports_schedule_id', $request->sports_schedule_id);
        }

        // Only show non-cancelled matches for public
        $query->where('status', '!=', 'cancelled');

        $matches = $query->get();

        // Group by sports schedule
        $groupedBrackets = $matches->groupBy('sports_schedule.schedule_name');

        return response()->json([
            'data' => $groupedBrackets
        ]);
    }

    /**
     * Get live/ongoing matches
     */
    public function getLiveMatches(): JsonResponse
    {
        $liveMatches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('status', 'ongoing')
            ->orderedByDateTime()
            ->get();

        return response()->json([
            'data' => $liveMatches
        ]);
    }

    /**
     * Get upcoming matches (next 7 days)
     */
    public function getUpcomingMatches(): JsonResponse
    {
        $upcomingMatches = SportMatch::with(['sport', 'sportsSchedule'])
            ->where('status', 'scheduled')
            ->where('match_date', '>=', now()->toDateString())
            ->where('match_date', '<=', now()->addDays(7)->toDateString())
            ->orderedByDateTime()
            ->limit(10)
            ->get();

        return response()->json([
            'data' => $upcomingMatches
        ]);
    }

    /**
     * Get match statistics for public display
     */
    public function getMatchStatistics(): JsonResponse
    {
        $totalMatches = SportMatch::count();
        $completedMatches = SportMatch::where('status', 'completed')->count();
        $upcomingMatches = SportMatch::where('status', 'scheduled')->count();
        $ongoingMatches = SportMatch::where('status', 'ongoing')->count();

        $totalSchedules = SportsSchedule::active()->count();
        $activeSports = SportMatch::distinct('sport_id')->count();

        return response()->json([
            'total_matches' => $totalMatches,
            'completed_matches' => $completedMatches,
            'upcoming_matches' => $upcomingMatches,
            'ongoing_matches' => $ongoingMatches,
            'total_schedules' => $totalSchedules,
            'active_sports' => $activeSports,
        ]);
    }

    /**
     * Get tournament results for public display
     */
    public function getTournamentResults(): JsonResponse
    {
        $sportsSchedules = SportsSchedule::with(['sport', 'matches' => function($query) {
            $query->with('sport')->orderBy('match_date')->orderBy('match_time');
        }])
        ->active()
        ->ordered()
        ->get();

        $tournamentResults = $sportsSchedules->map(function ($schedule) {
            $matches = $schedule->matches;
            $completedMatches = $matches->where('status', 'completed');
            $ongoingMatches = $matches->where('status', 'ongoing');

            // Determine overall status
            $status = 'upcoming';
            if ($completedMatches->count() === $matches->count() && $matches->count() > 0) {
                $status = 'completed';
            } elseif ($ongoingMatches->count() > 0 || $completedMatches->count() > 0) {
                $status = 'ongoing';
            }

            // Get winners from completed matches
            $winners = $completedMatches->where('winner_zone', '!=', null)->pluck('winner_zone')->unique();
            $topWinner = $winners->first();

            // Calculate participants (estimate based on zones involved)
            $zones = $matches->pluck('zone_a')->merge($matches->pluck('zone_b'))->filter()->unique();
            $participants = $zones->count() * 10; // Estimate 10 players per zone per sport

            return [
                'id' => $schedule->id,
                'sport' => $schedule->sport->name,
                'category' => $schedule->sport->participants ?? 'Mixed',
                'zone' => 'All Zones',
                'date' => $schedule->end_date,
                'status' => $status,
                'winner' => $status === 'completed' ? $topWinner : null,
                'runnerUp' => $status === 'completed' ? $winners->skip(1)->first() : null,
                'thirdPlace' => $status === 'completed' ? $winners->skip(2)->first() : null,
                'participants' => $participants,
                'matches' => $matches->count(),
                'finalScore' => $status === 'completed' ? 'Final completed' : null,
                'sport_logo' => $schedule->sport->logo_url,
                'venue' => $schedule->main_venue,
                'description' => $schedule->description
            ];
        });

        return response()->json([
            'data' => $tournamentResults
        ]);
    }

    /**
     * Get overall standings for public display
     */
    public function getOverallStandings(): JsonResponse
    {
        // Get all completed matches with points
        $completedMatches = SportMatch::where('status', 'completed')
            ->whereNotNull('winner_zone')
            ->with('sport')
            ->get();

        // Calculate points for each zone
        $zonePoints = [];
        $zoneMedals = [];

        foreach ($completedMatches as $match) {
            // Award points based on match results
            if ($match->winner_zone) {
                $winnerZone = $match->winner_zone;
                $zonePoints[$winnerZone] = ($zonePoints[$winnerZone] ?? 0) + 3; // 3 points for win

                // Count as gold medal for final matches
                if (str_contains(strtolower($match->title), 'final')) {
                    $zoneMedals[$winnerZone]['gold'] = ($zoneMedals[$winnerZone]['gold'] ?? 0) + 1;
                }
            }

            // Award points for participation
            if ($match->zone_a && $match->zone_a !== $match->winner_zone) {
                $zonePoints[$match->zone_a] = ($zonePoints[$match->zone_a] ?? 0) + 1; // 1 point for participation

                if (str_contains(strtolower($match->title), 'final')) {
                    $zoneMedals[$match->zone_a]['silver'] = ($zoneMedals[$match->zone_a]['silver'] ?? 0) + 1;
                }
            }

            if ($match->zone_b && $match->zone_b !== $match->winner_zone) {
                $zonePoints[$match->zone_b] = ($zonePoints[$match->zone_b] ?? 0) + 1; // 1 point for participation

                if (str_contains(strtolower($match->title), 'final')) {
                    $zoneMedals[$match->zone_b]['bronze'] = ($zoneMedals[$match->zone_b]['bronze'] ?? 0) + 1;
                }
            }
        }

        // Create standings array
        $standings = [];
        foreach ($zonePoints as $zone => $points) {
            $standings[] = [
                'position' => 0, // Will be set after sorting
                'zone' => $zone,
                'points' => $points,
                'gold' => $zoneMedals[$zone]['gold'] ?? 0,
                'silver' => $zoneMedals[$zone]['silver'] ?? 0,
                'bronze' => $zoneMedals[$zone]['bronze'] ?? 0,
            ];
        }

        // Sort by points (descending) and assign positions
        usort($standings, function($a, $b) {
            if ($a['points'] === $b['points']) {
                // If points are equal, sort by gold medals
                if ($a['gold'] === $b['gold']) {
                    // If gold medals are equal, sort by silver medals
                    return $b['silver'] - $a['silver'];
                }
                return $b['gold'] - $a['gold'];
            }
            return $b['points'] - $a['points'];
        });

        // Assign positions
        foreach ($standings as $index => &$standing) {
            $standing['position'] = $index + 1;
        }

        return response()->json([
            'data' => $standings
        ]);
    }
}
