<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RankCategory;
use Illuminate\Http\Request;

class RankCategoriesController extends Controller
{
    /**
     * Display a listing of rank categories
     */
    public function index(Request $request)
    {
        $query = RankCategory::query();

        // Filter by active status
        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        } else {
            // Default to active categories only
            $query->where('is_active', true);
        }

        $categories = $query->ordered()->get();

        return response()->json($categories);
    }

    /**
     * Display the specified rank category
     */
    public function show($id)
    {
        $category = RankCategory::findOrFail($id);
        return response()->json($category);
    }
}
