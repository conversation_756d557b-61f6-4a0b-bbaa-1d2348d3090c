<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RankCategoryTeam;
use App\Models\Sport;
use App\Models\RankCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RankCategoryTeamsController extends Controller
{
    /**
     * Create or update rank category team leader
     */
    public function store(Request $request)
    {
        $request->validate([
            'sport_id' => 'required|integer|exists:sports,id',
            'rank_category_id' => 'required|integer|exists:rank_categories,id',
            'team_leader_name' => 'required|string|max:255',
            'team_leader_phone' => 'required|string|max:20',
            'team_leader_contact' => 'nullable|string|max:255',
        ]);

        try {
            $rankCategoryTeam = RankCategoryTeam::updateOrCreate(
                [
                    'sport_id' => $request->sport_id,
                    'rank_category_id' => $request->rank_category_id,
                ],
                [
                    'team_leader_name' => $request->team_leader_name,
                    'team_leader_phone' => $request->team_leader_phone,
                    'team_leader_contact' => $request->team_leader_contact,
                    'is_active' => true,
                ]
            );

            return response()->json([
                'message' => 'Ketua pasukan berjaya disimpan',
                'rank_category_team' => $rankCategoryTeam
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal menyimpan ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update rank category team leader
     */
    public function update(Request $request, $id)
    {
        $rankCategoryTeam = RankCategoryTeam::findOrFail($id);

        $request->validate([
            'team_leader_name' => 'required|string|max:255',
            'team_leader_phone' => 'required|string|max:20',
            'team_leader_contact' => 'nullable|string|max:255',
        ]);

        try {
            $rankCategoryTeam->update([
                'team_leader_name' => $request->team_leader_name,
                'team_leader_phone' => $request->team_leader_phone,
                'team_leader_contact' => $request->team_leader_contact,
            ]);

            return response()->json([
                'message' => 'Ketua pasukan berjaya dikemaskini',
                'rank_category_team' => $rankCategoryTeam
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mengemaskini ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete rank category team leader
     */
    public function destroy($id)
    {
        try {
            $rankCategoryTeam = RankCategoryTeam::findOrFail($id);
            $rankCategoryTeam->delete();

            return response()->json([
                'message' => 'Ketua pasukan berjaya dipadamkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memadamkan ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get rank category team details
     */
    public function show($sportId, $rankCategoryId)
    {
        try {
            $rankCategoryTeam = RankCategoryTeam::where('sport_id', $sportId)
                ->where('rank_category_id', $rankCategoryId)
                ->with(['sport', 'rankCategory'])
                ->first();

            if (!$rankCategoryTeam) {
                return response()->json([
                    'message' => 'Ketua pasukan tidak dijumpai'
                ], 404);
            }

            return response()->json($rankCategoryTeam);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memuat data ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
