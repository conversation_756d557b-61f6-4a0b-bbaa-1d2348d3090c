<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Player;
use App\Models\Zone;
use App\Models\Sport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportsController extends Controller
{
    /**
     * Get reports data
     */
    public function index(Request $request)
    {
        $user = $request->user();

        try {
            // Get real data from database
            $totalParticipants = Player::where('is_active', true)->count();
            $totalMatches = DB::table('matches')->count();
            $completedMatches = DB::table('matches')->where('status', 'completed')->count();
            $completionRate = $totalMatches > 0 ? ($completedMatches / $totalMatches) * 100 : 0;

            // Get zone statistics
            $zoneStats = Zone::where('is_active', true)->get()->map(function ($zone) {
                $participants = Player::where('zone', $zone->code)
                                    ->where('is_active', true)
                                    ->count();

                $matchesPlayed = DB::table('matches')
                                  ->where(function ($q) use ($zone) {
                                      $q->where('zone_a', $zone->code)
                                        ->orWhere('zone_b', $zone->code);
                                  })
                                  ->where('status', 'completed')
                                  ->count();

                $wins = DB::table('matches')
                         ->where('winner_zone', $zone->code)
                         ->where('status', 'completed')
                         ->count();

                $losses = $matchesPlayed - $wins;
                $winRate = $matchesPlayed > 0 ? ($wins / $matchesPlayed) * 100 : 0;

                return [
                    'zone' => $zone->name,
                    'participants' => $participants,
                    'matches_played' => $matchesPlayed,
                    'wins' => $wins,
                    'losses' => $losses,
                    'win_rate' => round($winRate, 1),
                ];
            })->toArray();

            // Get sport statistics
            $sportStats = Sport::where('is_active', true)->get()->map(function ($sport) {
                $participants = DB::table('players')
                                 ->join('sport_teams', 'players.sport_team_id', '=', 'sport_teams.id')
                                 ->where('sport_teams.sport_id', $sport->id)
                                 ->where('players.is_active', true)
                                 ->count();

                $matches = DB::table('matches')
                            ->where('sport_id', $sport->id)
                            ->count();

                $completed = DB::table('matches')
                           ->where('sport_id', $sport->id)
                           ->where('status', 'completed')
                           ->count();

                $completionRate = $matches > 0 ? ($completed / $matches) * 100 : 0;

                return [
                    'sport' => $sport->name,
                    'participants' => $participants,
                    'matches' => $matches,
                    'completed' => $completed,
                    'completion_rate' => round($completionRate, 1),
                ];
            })->toArray();

            // Get monthly progress (last 3 months)
            $monthlyProgress = [];
            for ($i = 2; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $monthStart = $date->startOfMonth()->toDateString();
                $monthEnd = $date->endOfMonth()->toDateString();

                $scheduledMatches = DB::table('matches')
                                   ->whereBetween('match_date', [$monthStart, $monthEnd])
                                   ->count();

                $completedMatches = DB::table('matches')
                                   ->whereBetween('match_date', [$monthStart, $monthEnd])
                                   ->where('status', 'completed')
                                   ->count();

                $activeParticipants = Player::where('is_active', true)
                                          ->whereBetween('created_at', [$monthStart, $monthEnd])
                                          ->count();

                $monthlyProgress[] = [
                    'month' => $date->format('F'),
                    'matches_scheduled' => $scheduledMatches,
                    'matches_completed' => $completedMatches,
                    'participants_active' => $activeParticipants,
                ];
            }

            $reports = [
                'summary' => [
                    'total_participants' => $totalParticipants,
                    'total_matches' => $totalMatches,
                    'completion_rate' => round($completionRate, 1),
                    'average_participation' => round($totalParticipants / max(Zone::where('is_active', true)->count(), 1), 1),
                ],
                'by_zone' => $zoneStats,
                'by_sport' => $sportStats,
                'monthly_progress' => $monthlyProgress,
            ];

            // Filter data based on user role
            if ($user && $user->role === 'zone') {
                // Filter to show only data for user's zone
                $reports['by_zone'] = array_filter($reports['by_zone'], function($zone) use ($user) {
                    return strpos($zone['zone'], $user->zone) !== false;
                });
            }

            return response()->json($reports);
        } catch (\Exception $e) {
            \Log::error('Reports error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch reports: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Export reports
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'pdf'); // pdf, excel, csv
        $type = $request->get('type', 'summary'); // summary, zone, sport
        
        // Mock response - replace with actual export logic
        return response()->json([
            'message' => 'Laporan sedang disediakan',
            'download_url' => '/storage/reports/report_' . time() . '.' . $format,
            'format' => $format,
            'type' => $type,
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ]);
    }
}
