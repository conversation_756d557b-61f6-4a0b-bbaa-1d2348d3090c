<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Get all settings
     */
    public function index(Request $request)
    {
        $settings = SiteSetting::where('is_active', true)
                             ->pluck('value', 'key')
                             ->toArray();

        // Convert boolean strings to actual booleans
        foreach ($settings as $key => $value) {
            if ($value === '1' || $value === 'true') {
                $settings[$key] = true;
            } elseif ($value === '0' || $value === 'false') {
                $settings[$key] = false;
            }
        }

        return response()->json($settings);
    }

    /**
     * Update a specific setting
     */
    public function updateSetting(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
        ]);

        $key = $request->key;
        $value = $request->value;

        // Mock response - replace with actual database update
        // In real implementation, you would update the settings table
        
        return response()->json([
            'message' => 'Tetapan berjaya dikemaskini',
            'setting' => [
                'key' => $key,
                'value' => $value,
                'updated_at' => now()->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Upload logo
     */
    public function uploadLogo(Request $request)
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('logo')) {
            $file = $request->file('logo');
            $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs('public/images', $filename);
            $url = Storage::url('images/' . $filename);

            // Mock response - replace with actual database update
            return response()->json([
                'message' => 'Logo berjaya dimuat naik',
                'logo_url' => $url,
                'filename' => $filename,
                'uploaded_at' => now()->format('Y-m-d H:i:s'),
            ]);
        }

        return response()->json([
            'message' => 'Tiada fail yang dimuat naik'
        ], 400);
    }

    /**
     * Upload favicon
     */
    public function uploadFavicon(Request $request)
    {
        $request->validate([
            'favicon' => 'required|image|mimes:ico,png|max:1024',
        ]);

        if ($request->hasFile('favicon')) {
            $file = $request->file('favicon');
            $filename = 'favicon.' . $file->getClientOriginalExtension();
            
            // Store the file
            $path = $file->storeAs('public/images', $filename);
            $url = Storage::url('images/' . $filename);

            // Mock response - replace with actual database update
            return response()->json([
                'message' => 'Favicon berjaya dimuat naik',
                'favicon_url' => $url,
                'filename' => $filename,
                'uploaded_at' => now()->format('Y-m-d H:i:s'),
            ]);
        }

        return response()->json([
            'message' => 'Tiada fail yang dimuat naik'
        ], 400);
    }

    /**
     * Update general settings
     */
    public function updateGeneralSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'header_title_part1' => 'nullable|string|max:50',
            'header_title_part2' => 'nullable|string|max:50',
            'header_subtitle' => 'nullable|string|max:100',
            'copyright_text' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'site_name',
            'site_tagline',
            'site_description',
            'header_title_part1',
            'header_title_part2',
            'header_subtitle',
            'copyright_text'
        ]);

        // Handle logo upload if provided
        if ($request->hasFile('logo')) {
            $file = $request->file('logo');
            $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();

            try {
                // Ensure directory exists
                $logoDir = storage_path('app/public/logo');
                if (!file_exists($logoDir)) {
                    mkdir($logoDir, 0755, true);
                }

                // Store the file
                $file->move($logoDir, $filename);
                $data['logo_url'] = 'logo/' . $filename;

            } catch (\Exception $e) {
                \Log::error('Logo upload failed', [
                    'error' => $e->getMessage(),
                    'filename' => $filename
                ]);
                // Continue without logo if upload fails
            }
        }

        // Save to database
        foreach ($data as $key => $value) {
            SiteSetting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'is_active' => true,
                    'updated_at' => now()
                ]
            );
        }

        return response()->json([
            'message' => 'Tetapan umum berjaya dikemaskini',
            'data' => $data,
            'updated_at' => now()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Update notification settings
     */
    public function updateNotificationSettings(Request $request)
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'facebook_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
        ]);

        $data = $request->only([
            'email_notifications',
            'sms_notifications',
            'push_notifications',
            'facebook_url',
            'instagram_url',
            'twitter_url',
            'youtube_url'
        ]);

        // Save to database
        foreach ($data as $key => $value) {
            SiteSetting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'is_active' => true,
                    'updated_at' => now()
                ]
            );
        }

        return response()->json([
            'message' => 'Tetapan notifikasi berjaya dikemaskini',
            'data' => $data,
            'updated_at' => now()->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Get public settings (no authentication required)
     */
    public function getPublicSettings()
    {
        // Define which settings are public
        $publicKeys = [
            'site_name',
            'site_tagline',
            'site_description',
            'logo_url',
            'header_title_part1',
            'header_title_part2',
            'header_subtitle',
            'copyright_text',
            'facebook_url',
            'instagram_url',
            'twitter_url',
            'youtube_url'
        ];

        $settings = SiteSetting::where('is_active', true)
                             ->whereIn('key', $publicKeys)
                             ->pluck('value', 'key')
                             ->toArray();

        // Convert boolean strings to actual booleans
        foreach ($settings as $key => $value) {
            if ($value === '1' || $value === 'true') {
                $settings[$key] = true;
            } elseif ($value === '0' || $value === 'false') {
                $settings[$key] = false;
            }
        }

        return response()->json($settings);
    }
}
