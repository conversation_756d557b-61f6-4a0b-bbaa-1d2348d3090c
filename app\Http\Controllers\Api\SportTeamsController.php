<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SportTeam;
use App\Models\Sport;
use App\Models\Zone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SportTeamsController extends Controller
{
    /**
     * Get sports with team information for a zone
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $zoneCode = $request->get('zone', $user->zone);

        // Zone admin can only see their own zone
        if ($user->role === 'zone' && $zoneCode !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak. Anda hanya boleh melihat zon anda sendiri.'
            ], 403);
        }

        try {
            // Get all active sports
            $sports = Sport::active()->ordered()->get();

            $sportsWithTeams = $sports->map(function ($sport) use ($zoneCode) {
                // Check if sport is rank-based (like Larian)
                if ($sport->sport_type === 'rank_based') {
                    // For rank-based sports, show rank categories instead of zone teams
                    $rankCategories = \App\Models\RankCategory::active()->ordered()->get();

                    return [
                        'id' => $sport->id,
                        'name' => $sport->name,
                        'icon' => $sport->logo_url,
                        'sport_type' => 'rank_based',
                        'rank_categories' => $rankCategories->map(function ($category) use ($sport) {
                            // Get team for this rank category
                            $rankCategoryTeam = \App\Models\RankCategoryTeam::where('sport_id', $sport->id)
                                ->where('rank_category_id', $category->id)
                                ->first();

                            $playersCount = \App\Models\Player::where('rank_category_id', $category->id)
                                ->whereHas('sports', function($query) use ($sport) {
                                    $query->where('sport_id', $sport->id);
                                })
                                ->count();

                            return [
                                'id' => $category->id,
                                'name' => $category->name,
                                'code' => $category->code,
                                'players_count' => $playersCount,
                                'team_leader_name' => $rankCategoryTeam ? $rankCategoryTeam->team_leader_name : null,
                                'team_leader_phone' => $rankCategoryTeam ? $rankCategoryTeam->team_leader_phone : null,
                                'team_leader_contact' => $rankCategoryTeam ? $rankCategoryTeam->team_leader_contact : null,
                                'has_leader' => $rankCategoryTeam && $rankCategoryTeam->team_leader_name ? true : false,
                            ];
                        }),
                        'total_players' => \App\Models\Player::whereHas('sports', function($query) use ($sport) {
                            $query->where('sport_id', $sport->id);
                        })->count(),
                        'can_manage' => true,
                    ];
                }

                // For zone-based sports (existing logic)
                $sportTeam = SportTeam::where('zone_code', $zoneCode)
                                    ->where('sport_id', $sport->id)
                                    ->with(['mainPlayers', 'substitutePlayers'])
                                    ->first();

                // Calculate actual player counts
                $mainPlayersCount = $sportTeam ? $sportTeam->mainPlayers->count() : 0;
                $substitutePlayersCount = $sportTeam ? $sportTeam->substitutePlayers->count() : 0;

                return [
                    'id' => $sport->id,
                    'name' => $sport->name,
                    'icon' => $sport->logo_url,
                    'sport_type' => 'zone_based',
                    'sport_team' => $sportTeam ? [
                        'id' => $sportTeam->id,
                        'team_leader_name' => $sportTeam->team_leader_name,
                        'team_leader_phone' => $sportTeam->team_leader_phone,
                        'team_leader_contact' => $sportTeam->team_leader_contact,
                    ] : null,
                    'team_leader_name' => $sportTeam ? $sportTeam->team_leader_name : null,
                    'main_players_count' => $mainPlayersCount,
                    'substitute_players_count' => $substitutePlayersCount,
                    'total_players' => $mainPlayersCount + $substitutePlayersCount,
                    'can_manage' => true, // Zone admin can manage their zone
                ];
            });

            return response()->json($sportsWithTeams);
        } catch (\Exception $e) {
            \Log::error('SportTeamsController@index exception', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Gagal memuat data sukan',
                'error' => $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    /**
     * Get detailed sport team information with players
     */
    public function show(Request $request, $sportId)
    {
        $user = $request->user();
        $zoneCode = $request->get('zone', $user->zone);

        // Zone admin can only see their own zone
        if ($user->role === 'zone' && $zoneCode !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        try {
            $sport = Sport::findOrFail($sportId);
            
            $sportTeam = SportTeam::where('zone_code', $zoneCode)
                                ->where('sport_id', $sportId)
                                ->with(['mainPlayers.rankCategory', 'substitutePlayers.rankCategory'])
                                ->first();

            $response = [
                'sport' => [
                    'id' => $sport->id,
                    'name' => $sport->name,
                    'icon' => $sport->logo_url,
                    'required_players' => $sport->required_players ?? 11,
                    'max_substitute_players' => $sport->max_substitute_players ?? 8,
                ],
                'team_leader' => $sportTeam ? [
                    'id' => $sportTeam->id,
                    'name' => $sportTeam->team_leader_name,
                    'phone' => $sportTeam->team_leader_phone,
                    'contact' => $sportTeam->team_leader_contact,
                ] : null,
                'main_players' => $sportTeam ? $sportTeam->mainPlayers->map(function ($player) {
                    return [
                        'id' => $player->id,
                        'name' => $player->name,
                        'phone' => $player->phone,
                        'status' => $player->status,
                        'player_type' => $player->player_type,
                        'rank_category_id' => $player->rank_category_id,
                        'rank_category' => $player->rankCategory ? [
                            'id' => $player->rankCategory->id,
                            'name' => $player->rankCategory->name,
                            'code' => $player->rankCategory->code,
                        ] : null,
                        'created_at' => $player->created_at->toISOString(),
                    ];
                }) : [],
                'substitute_players' => $sportTeam ? $sportTeam->substitutePlayers->map(function ($player) {
                    return [
                        'id' => $player->id,
                        'name' => $player->name,
                        'phone' => $player->phone,
                        'status' => $player->status,
                        'player_type' => $player->player_type,
                        'rank_category_id' => $player->rank_category_id,
                        'rank_category' => $player->rankCategory ? [
                            'id' => $player->rankCategory->id,
                            'name' => $player->rankCategory->name,
                            'code' => $player->rankCategory->code,
                        ] : null,
                        'created_at' => $player->created_at->toISOString(),
                    ];
                }) : [],
            ];

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memuat data pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update team leader
     */
    public function store(Request $request)
    {
        $user = $request->user();

        $request->validate([
            'zone_code' => 'required|string|exists:zones,code',
            'sport_id' => 'required|integer|exists:sports,id',
            'team_leader_name' => 'required|string|max:255',
            'team_leader_phone' => 'required|string|max:20',
            'team_leader_contact' => 'nullable|string|max:255',
        ]);

        // Zone admin can only manage their own zone
        if ($user->role === 'zone' && $request->zone_code !== $user->zone) {
            return response()->json([
                'message' => 'Anda hanya boleh menguruskan zon anda sendiri.'
            ], 403);
        }

        // Check if zone has overall leader (commented out for now)
        // if ($user->role === 'zone') {
        //     $zone = Zone::where('code', $user->zone)->first();
        //     if (!$zone || !$zone->leader_id) {
        //         return response()->json([
        //             'message' => 'Zon anda belum mempunyai ketua. Sila lantik ketua zon terlebih dahulu.'
        //         ], 403);
        //     }
        // }

        try {
            $sportTeam = SportTeam::updateOrCreate(
                [
                    'zone_code' => $request->zone_code,
                    'sport_id' => $request->sport_id,
                ],
                [
                    'team_leader_name' => $request->team_leader_name,
                    'team_leader_phone' => $request->team_leader_phone,
                    'team_leader_contact' => $request->team_leader_contact,
                ]
            );

            return response()->json([
                'message' => 'Ketua pasukan berjaya disimpan',
                'sport_team' => $sportTeam->getTeamSummary()
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal menyimpan ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update team leader
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();
        $sportTeam = SportTeam::findOrFail($id);

        // Zone admin can only manage their own zone
        if ($user->role === 'zone' && $sportTeam->zone_code !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        $request->validate([
            'team_leader_name' => 'required|string|max:255',
            'team_leader_phone' => 'required|string|max:20',
            'team_leader_contact' => 'nullable|string|max:255',
        ]);

        try {
            $sportTeam->update([
                'team_leader_name' => $request->team_leader_name,
                'team_leader_phone' => $request->team_leader_phone,
                'team_leader_contact' => $request->team_leader_contact,
            ]);

            return response()->json([
                'message' => 'Ketua pasukan berjaya dikemaskini',
                'sport_team' => $sportTeam->getTeamSummary()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mengemaskini ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete team leader (and associated players)
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $sportTeam = SportTeam::findOrFail($id);

        // Zone admin can only manage their own zone
        if ($user->role === 'zone' && $sportTeam->zone_code !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        try {
            DB::transaction(function () use ($sportTeam) {
                // Delete all players in this sport team
                $sportTeam->players()->delete();
                
                // Delete the sport team
                $sportTeam->delete();
            });

            return response()->json([
                'message' => 'Ketua pasukan dan semua peserta berjaya dipadamkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memadamkan ketua pasukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
