<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sport;
use Illuminate\Http\Request;

class SportsController extends Controller
{
    /**
     * Display a listing of sports
     */
    public function index(Request $request)
    {
        $query = Sport::query();

        // Filter by active status
        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        } else {
            // Default to active sports only
            $query->where('is_active', true);
        }

        $sports = $query->orderBy('display_order')
                       ->orderBy('name')
                       ->get();

        return response()->json($sports);
    }

    /**
     * Store a newly created sport
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // Only superadmin and admin can create sports
        if (!$user || !in_array($user->role, ['superadmin', 'admin'])) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'participants' => 'nullable|string|max:100',
            'venue' => 'nullable|string',
            'rules' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0',
        ]);

        try {
            $sport = Sport::create([
                'name' => $request->name,
                'slug' => \Str::slug($request->name),
                'description' => $request->description,
                'participants' => $request->participants,
                'venue' => $request->venue,
                'rules' => $request->rules,
                'display_order' => $request->display_order ?? 0,
                'is_active' => true,
                'created_by' => $user->getAttribute('id'),
            ]);

            return response()->json([
                'message' => 'Sukan berjaya dicipta',
                'sport' => $sport
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mencipta sukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified sport
     */
    public function show($id)
    {
        $sport = Sport::findOrFail($id);
        return response()->json($sport);
    }

    /**
     * Update the specified sport
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();

        // Only superadmin and admin can update sports
        if (!$user || !in_array($user->role, ['superadmin', 'admin'])) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        $sport = Sport::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'participants' => 'nullable|string|max:100',
            'venue' => 'nullable|string',
            'rules' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            $sport->update([
                'name' => $request->name,
                'slug' => \Str::slug($request->name),
                'description' => $request->description,
                'participants' => $request->participants,
                'venue' => $request->venue,
                'rules' => $request->rules,
                'display_order' => $request->display_order ?? $sport->display_order,
                'is_active' => $request->get('is_active', $sport->is_active),
            ]);

            return response()->json([
                'message' => 'Sukan berjaya dikemaskini',
                'sport' => $sport
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mengemaskini sukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified sport
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        // Only superadmin can delete sports
        if (!$user || $user->role !== 'superadmin') {
            return response()->json([
                'message' => 'Akses ditolak. Hanya superadmin boleh memadam sukan.'
            ], 403);
        }

        try {
            $sport = Sport::findOrFail($id);

            // Check if sport has players
            $playerCount = \App\Models\Player::whereHas('sports', function($query) use ($id) {
                $query->where('sport_id', $id);
            })->count();

            if ($playerCount > 0) {
                return response()->json([
                    'message' => "Tidak boleh memadam sukan. Terdapat {$playerCount} peserta dalam sukan ini."
                ], 400);
            }

            $sport->delete();

            return response()->json([
                'message' => 'Sukan berjaya dipadam'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memadam sukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync sport from CMS to database
     */
    public function sync(Request $request)
    {
        $user = $request->user();

        // Only superadmin and admin can sync sports
        if (!$user || !in_array($user->role, ['superadmin', 'admin'])) {
            return response()->json([
                'message' => 'Akses ditolak. Hanya superadmin dan admin boleh sync sukan.'
            ], 403);
        }

        $request->validate([
            'name' => 'required|string|max:100',
            'slug' => 'required|string|max:100',
            'description' => 'nullable|string',
            'logo_url' => 'nullable|string|max:500',
            'participants' => 'nullable|string|max:100',
            'venue' => 'nullable|string',
            'display_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        try {
            $sport = Sport::updateOrCreate(
                ['slug' => $request->slug], // Find by slug
                [
                    'name' => $request->name,
                    'slug' => $request->slug,
                    'description' => $request->description,
                    'logo_url' => $request->logo_url,
                    'participants' => $request->participants,
                    'venue' => $request->venue,
                    'display_order' => $request->display_order ?? 0,
                    'is_active' => $request->get('is_active', true),
                    'created_by' => $user->getAttribute('id'),
                ]
            );

            return response()->json([
                'message' => 'Sukan berjaya disync',
                'sport' => $sport
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal sync sukan',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
