<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UsersController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = User::query();

        // Filter by zone if specified
        if ($request->has('zone')) {
            $query->where('zone', $request->zone);
        }

        // Filter by role if specified
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        // Apply role-based restrictions
        if ($user->role === 'zone') {
            // Zone admin can only see users in their zone
            $query->where('zone', $user->zone);
        } elseif ($user->role === 'admin') {
            // Admin can see all users except superadmin
            $query->where('role', '!=', 'superadmin');
        }
        // Superadmin can see all users (no additional restrictions)

        $users = $query->select('id', 'name', 'username', 'role', 'zone', 'is_active')
                      ->orderBy('name')
                      ->get();

        return response()->json($users);
    }

    /**
     * Display the specified user
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $targetUser = User::findOrFail($id);

        // Check permissions
        if ($user->role === 'zone' && $targetUser->zone !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        if ($user->role === 'admin' && $targetUser->role === 'superadmin') {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        return response()->json($targetUser);
    }
}
