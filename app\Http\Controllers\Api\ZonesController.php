<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Zone;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ZonesController extends Controller
{
    /**
     * Display a listing of zones
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $query = Zone::query();

        // Filter by zone if user is zone admin
        if ($user && $user->role === 'zone') {
            $query->where('code', $user->zone);
        }

        // Include leader information
        $zones = $query->with('leader:id,name,username')
                      ->orderBy('display_order')
                      ->orderBy('name')
                      ->get();

        return response()->json($zones);
    }

    /**
     * Store a newly created zone
     */
    public function store(Request $request)
    {
        $user = $request->user();

        // Only superadmin can create zones
        if (!$user || $user->role !== 'superadmin') {
            return response()->json([
                'message' => 'Akses ditolak. Hanya superadmin boleh membuat zon baru.'
            ], 403);
        }

        $request->validate([
            'code' => 'required|string|max:20|unique:zones,code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_primary' => 'required|string|regex:/^#[0-9A-F]{6}$/i',
            'color_secondary' => 'required|string|regex:/^#[0-9A-F]{6}$/i',
            'leader_id' => 'nullable|exists:users,id',
            'display_order' => 'nullable|integer|min:0',
        ]);

        try {
            $zone = Zone::create([
                'code' => strtoupper($request->code),
                'name' => $request->name,
                'description' => $request->description,
                'color_primary' => $request->color_primary,
                'color_secondary' => $request->color_secondary,
                'leader_id' => $request->leader_id,
                'display_order' => $request->display_order ?? 0,
                'is_active' => true,
                'updated_by' => $user->getAttribute('id'),
            ]);

            return response()->json([
                'message' => 'Zon berjaya dicipta',
                'zone' => $zone->load('leader:id,name,username')
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mencipta zon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified zone
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();

        $query = Zone::with('leader:id,name,username');

        // Check if $id is numeric (ID) or string (code)
        if (is_numeric($id)) {
            // Find by ID
            $zone = $query->findOrFail($id);
        } else {
            // Find by code
            $zone = $query->where('code', $id)->firstOrFail();
        }

        // Check permissions for zone admin
        if ($user && $user->role === 'zone' && $zone->code !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak. Anda hanya boleh melihat zon anda sendiri.'
            ], 403);
        }

        return response()->json($zone);
    }

    /**
     * Update the specified zone
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();

        // Check if $id is numeric (ID) or string (code)
        if (is_numeric($id)) {
            // Find by ID
            $zone = Zone::findOrFail($id);
        } else {
            // Find by code
            $zone = Zone::where('code', $id)->firstOrFail();
        }

        // Check permissions
        if ($user->role === 'zone' && $zone->code !== $user->zone) {
            return response()->json([
                'message' => 'Akses ditolak. Anda hanya boleh mengemaskini zon anda sendiri.'
            ], 403);
        }

        if ($user->role !== 'superadmin' && $user->role !== 'zone') {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_primary' => 'required|string|regex:/^#[0-9A-F]{6}$/i',
            'color_secondary' => 'required|string|regex:/^#[0-9A-F]{6}$/i',
            'leader_id' => 'nullable|exists:users,id',
            'leader_name' => 'nullable|string|max:255',
            'leader_contact' => 'nullable|string|max:20',
        ];

        // Only superadmin can update code and display_order
        if ($user->role === 'superadmin') {
            $rules['code'] = [
                'required',
                'string',
                'max:20',
                Rule::unique('zones', 'code')->ignore($zone->id)
            ];
            $rules['display_order'] = 'nullable|integer|min:0';
            $rules['is_active'] = 'boolean';
        }

        $request->validate($rules);

        try {
            $updateData = [
                'name' => $request->name,
                'description' => $request->description,
                'color_primary' => $request->color_primary,
                'color_secondary' => $request->color_secondary,
                'leader_id' => $request->leader_id,
                'leader_name' => $request->leader_name,
                'leader_contact' => $request->leader_contact,
                'updated_by' => $user->getAttribute('id'),
            ];

            // Add superadmin-only fields
            if ($user->role === 'superadmin') {
                if ($request->has('code')) {
                    $updateData['code'] = strtoupper($request->code);
                }
                if ($request->has('display_order')) {
                    $updateData['display_order'] = $request->display_order;
                }
                if ($request->has('is_active')) {
                    $updateData['is_active'] = $request->is_active;
                }
            }

            $zone->update($updateData);

            return response()->json([
                'message' => 'Zon berjaya dikemaskini',
                'zone' => $zone->fresh()->load('leader:id,name,username')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal mengemaskini zon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified zone
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();

        // Only superadmin can delete zones
        if (!$user || $user->role !== 'superadmin') {
            return response()->json([
                'message' => 'Akses ditolak. Hanya superadmin boleh memadam zon.'
            ], 403);
        }

        try {
            $zone = Zone::findOrFail($id);

            // Check if zone has players
            $playerCount = \App\Models\Player::where('zone', $zone->code)->count();
            if ($playerCount > 0) {
                return response()->json([
                    'message' => "Tidak boleh memadam zon. Terdapat {$playerCount} peserta dalam zon ini."
                ], 400);
            }

            $zone->delete();

            return response()->json([
                'message' => 'Zon berjaya dipadam'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Gagal memadam zon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update zone logo only
     */
    public function updateLogo(Request $request, $code)
    {
        $user = $request->user();

        // Check if user has permission to update zone
        if (!$user || ($user->role !== 'admin' && $user->role !== 'superadmin' && ($user->role !== 'zone' || $user->zone !== $code))) {
            return response()->json([
                'message' => 'Akses ditolak'
            ], 403);
        }

        $zone = Zone::where('code', $code)->first();
        if (!$zone) {
            return response()->json([
                'message' => 'Zon tidak dijumpai'
            ], 404);
        }

        $request->validate([
            'logo_url' => 'required|string|max:500',
        ]);

        $zone->update([
            'logo_url' => $request->logo_url,
            'updated_by' => $user->id,
        ]);

        return response()->json([
            'message' => 'Logo zon berjaya dikemaskini',
            'data' => $zone->fresh()
        ]);
    }

    /**
     * Get users in a specific zone
     */
    public function getZoneUsers(Request $request, $code)
    {
        $user = $request->user();

        // Check permissions
        if ($user->role === 'zone' && $user->zone !== $code) {
            return response()->json([
                'message' => 'Akses ditolak.'
            ], 403);
        }

        $users = User::where('zone', $code)
                    ->where('role', 'zone')
                    ->select('id', 'name', 'username', 'role', 'zone')
                    ->orderBy('name')
                    ->get();

        return response()->json($users);
    }
}
