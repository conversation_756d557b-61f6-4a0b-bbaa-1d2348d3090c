<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPasswordChange
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        \Log::info('CheckPasswordChange middleware called', [
            'user' => $user ? $user->username : 'no user',
            'must_change_password' => $user ? $user->must_change_password : 'N/A',
            'route' => $request->route()->getName(),
            'url' => $request->url()
        ]);

        // If user is authenticated and must change password
        if ($user && $user->must_change_password) {
            \Log::info('User must change password, checking route');
            // Allow access to password change routes and logout
            if (!$request->routeIs('password.change', 'password.update', 'logout')) {
                \Log::info('Redirecting to password change page');
                return redirect()->route('password.change');
            }
        }

        return $next($request);
    }
}
