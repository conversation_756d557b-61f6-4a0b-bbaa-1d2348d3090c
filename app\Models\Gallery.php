<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Gallery extends Model
{
    protected $table = 'gallery';

    protected $fillable = [
        'title',
        'description',
        'file_url',
        'file_urls',
        'image_count',
        'file_type',
        'category',
        'zone',
        'views',
        'is_featured',
        'uploaded_by',
    ];

    protected function casts(): array
    {
        return [
            'is_featured' => 'boolean',
            'file_urls' => 'array',
        ];
    }

    /**
     * Accessors & Mutators
     */
    public function getMainImageAttribute(): string
    {
        // Return first image from file_urls or fallback to file_url
        if ($this->file_urls && is_array($this->file_urls) && count($this->file_urls) > 0) {
            return $this->file_urls[0];
        }
        return $this->file_url ?? '';
    }

    public function getAllImagesAttribute(): array
    {
        // Return all images from file_urls or fallback to single file_url
        if ($this->file_urls && is_array($this->file_urls)) {
            return $this->file_urls;
        }
        return $this->file_url ? [$this->file_url] : [];
    }

    /**
     * Relationships
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scopes
     */
    public function scopeByType($query, $type)
    {
        return $query->where('file_type', $type);
    }

    public function scopeByZone($query, $zone)
    {
        return $query->where('zone', $zone)->orWhere('zone', 'all');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
