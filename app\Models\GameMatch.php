<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GameMatch extends Model
{
    protected $table = 'matches';

    protected $fillable = [
        'sport_id',
        'title',
        'zone_a',
        'zone_b',
        'match_date',
        'match_time',
        'venue',
        'status',
        'score_zone_a',
        'score_zone_b',
        'winner_zone',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'match_date' => 'date',
        'score_zone_a' => 'integer',
        'score_zone_b' => 'integer',
    ];

    /**
     * Relationships
     */
    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function zoneA(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_a', 'code');
    }

    public function zoneB(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_b', 'code');
    }

    public function winnerZone(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'winner_zone', 'code');
    }

    /**
     * Scopes
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeOngoing($query)
    {
        return $query->where('status', 'ongoing');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByZone($query, string $zone)
    {
        return $query->where('zone_a', $zone)->orWhere('zone_b', $zone);
    }

    public function scopeBySport($query, int $sportId)
    {
        return $query->where('sport_id', $sportId);
    }

    /**
     * Accessors
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getWinnerAttribute(): ?string
    {
        if (!$this->is_completed) {
            return null;
        }

        if ($this->score_zone_a > $this->score_zone_b) {
            return $this->zone_a;
        } elseif ($this->score_zone_b > $this->score_zone_a) {
            return $this->zone_b;
        }

        return 'draw';
    }
}
