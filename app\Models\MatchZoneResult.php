<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MatchZoneResult extends Model
{
    protected $fillable = [
        'match_id',
        'zone_code',
        'final_score',
        'time_result',
        'ranking_position',
        'points_earned',
        'detailed_results',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'final_score' => 'integer',
            'ranking_position' => 'integer',
            'points_earned' => 'integer',
            'detailed_results' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function match(): BelongsTo
    {
        return $this->belongsTo(SportMatch::class, 'match_id');
    }

    public function zone(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_code', 'code');
    }

    /**
     * Scopes
     */
    public function scopeByMatch($query, $matchId)
    {
        return $query->where('match_id', $matchId);
    }

    public function scopeByZone($query, $zoneCode)
    {
        return $query->where('zone_code', $zoneCode);
    }

    public function scopeOrderedByRanking($query)
    {
        return $query->orderBy('ranking_position');
    }

    public function scopeOrderedByPoints($query)
    {
        return $query->orderByDesc('points_earned');
    }

    public function scopeWinners($query)
    {
        return $query->where('ranking_position', 1);
    }

    public function scopeTopThree($query)
    {
        return $query->whereIn('ranking_position', [1, 2, 3]);
    }
}
