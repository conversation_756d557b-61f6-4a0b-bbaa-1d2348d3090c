<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PageContent extends Model
{
    protected $table = 'page_content';

    protected $fillable = [
        'page',
        'section',
        'content_key',
        'content_value',
        'content_type',
        'description',
        'sort_order',
        'is_active',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'content_value' => 'string',
    ];

    /**
     * Get the user who last updated this content
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get content for a specific page
     */
    public function scopeForPage($query, string $page)
    {
        return $query->where('page', $page)->where('is_active', true);
    }

    /**
     * Scope to get content for a specific page and section
     */
    public function scopeForPageSection($query, string $page, ?string $section = null)
    {
        $query = $query->where('page', $page)->where('is_active', true);

        if ($section) {
            $query->where('section', $section);
        }

        return $query;
    }

    /**
     * Get content value by page, section, and key
     */
    public static function getContent(string $page, ?string $section, string $key, $default = null)
    {
        $content = static::where('page', $page)
            ->where('section', $section)
            ->where('content_key', $key)
            ->where('is_active', true)
            ->first();

        return $content ? $content->content_value : $default;
    }

    /**
     * Get all content for a specific page - more efficient than multiple getContent() calls
     */
    public static function getPageContent(string $page)
    {
        return static::where('page', $page)
            ->where('is_active', true)
            ->get()
            ->groupBy('section')
            ->map(function ($sectionContent) {
                return $sectionContent->pluck('content_value', 'content_key')->toArray();
            })
            ->toArray();
    }

    /**
     * Set content value by page, section, and key
     */
    public static function setContent(string $page, ?string $section, string $key, $value, string $type = 'text')
    {
        return static::updateOrCreate(
            [
                'page' => $page,
                'section' => $section,
                'content_key' => $key,
            ],
            [
                'content_value' => $value,
                'content_type' => $type,
                'is_active' => true,
                'updated_by' => auth()->user() ? auth()->user()->id : null,
            ]
        );
    }
}
