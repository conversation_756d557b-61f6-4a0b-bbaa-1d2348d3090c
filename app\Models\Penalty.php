<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Penalty extends Model
{
    protected $fillable = [
        'offense',
        'penalty_description',
        'severity',
        'sort_order',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the user who created this penalty
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this penalty
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active penalties
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get penalties ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * Scope to get penalties by severity
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Get all active penalties ordered
     */
    public static function getAllActive()
    {
        return static::active()->ordered()->get();
    }

    /**
     * Get severity color class for UI
     */
    public function getSeverityColorAttribute(): string
    {
        return match($this->severity) {
            'high' => 'bg-red-500/20 text-red-300 border-red-500/30',
            'medium' => 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
            'low' => 'bg-green-500/20 text-green-300 border-green-500/30',
            default => 'bg-gray-500/20 text-gray-300 border-gray-500/30',
        };
    }
}
