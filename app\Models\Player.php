<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Player extends Model
{
    protected $fillable = [
        'name',
        'service_number',
        'zone',
        'rank',
        'rank_category_id',
        'unit',
        'phone',
        'address',
        'emergency_contact',
        'emergency_phone',
        'medical_conditions',
        'is_active',
        'status',
        'player_type',
        'sport_team_id',
        'registered_by',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function registeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_by');
    }

    public function sports(): BelongsToMany
    {
        return $this->belongsToMany(Sport::class, 'player_sports');
    }

    public function zoneModel(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone', 'code');
    }

    public function sportTeam(): BelongsTo
    {
        return $this->belongsTo(SportTeam::class, 'sport_team_id');
    }

    public function rankCategory(): BelongsTo
    {
        return $this->belongsTo(RankCategory::class);
    }

    /**
     * Scopes
     */
    public function scopeMainPlayers($query)
    {
        return $query->where('player_type', 'main');
    }

    public function scopeSubstitutePlayers($query)
    {
        return $query->where('player_type', 'substitute');
    }

    public function scopeForZone($query, $zoneCode)
    {
        return $query->where('zone', $zoneCode);
    }

    public function scopeForSportTeam($query, $sportTeamId)
    {
        return $query->where('sport_team_id', $sportTeamId);
    }
}
