<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RankCategory extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'display_order',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function players(): Has<PERSON>any
    {
        return $this->hasMany(Player::class, 'rank_category_id');
    }

    public function rankCategoryTeams(): HasMany
    {
        return $this->hasMany(RankCategoryTeam::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }
}
