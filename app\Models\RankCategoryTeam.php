<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RankCategoryTeam extends Model
{
    protected $fillable = [
        'sport_id',
        'rank_category_id',
        'team_leader_name',
        'team_leader_phone',
        'team_leader_contact',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function rankCategory(): BelongsTo
    {
        return $this->belongsTo(RankCategory::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForSport($query, $sportId)
    {
        return $query->where('sport_id', $sportId);
    }

    public function scopeForRankCategory($query, $rankCategoryId)
    {
        return $query->where('rank_category_id', $rankCategoryId);
    }
}
