<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Sport extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo_url',
        'participants',
        'venue',
        'rules',
        'required_players',
        'max_substitute_players',
        'sport_type',
        'is_active',
        'display_order',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function players(): BelongsToMany
    {
        return $this->belongsToMany(Player::class, 'player_sports');
    }

    public function matches(): HasMany
    {
        return $this->hasMany(SportMatch::class);
    }

    public function sportsSchedules(): HasMany
    {
        return $this->hasMany(SportsSchedule::class);
    }

    public function tournamentBrackets(): HasMany
    {
        return $this->hasMany(TournamentBracket::class);
    }

    public function gameMatches(): HasMany
    {
        return $this->hasMany(GameMatch::class);
    }

    public function tournamentResults(): HasMany
    {
        return $this->hasMany(TournamentResult::class);
    }

    public function sportsRules(): HasMany
    {
        return $this->hasMany(SportsRule::class, 'sport_name', 'name');
    }

    public function sportTeams(): HasMany
    {
        return $this->hasMany(SportTeam::class);
    }

    /**
     * Get sport teams for a specific zone
     */
    public function sportTeamsForZone($zoneCode): HasMany
    {
        return $this->hasMany(SportTeam::class)->where('zone_code', $zoneCode);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }
}
