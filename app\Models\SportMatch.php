<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SportMatch extends Model
{
    protected $table = 'matches';



    protected $fillable = [
        'sport_id',
        'sports_schedule_id',
        'bil',
        'title',
        'match_type',
        'event_type',
        'zone_a',
        'zone_b',
        'special_teams',
        'participating_zones',
        'match_date',
        'match_time',
        'end_time',
        'venue',
        'court_field',
        'category',
        'gender_type',
        'status',
        'score_zone_a',
        'score_zone_b',
        'points_zone_a',
        'points_zone_b',
        'time_result_a',
        'time_result_b',
        'zone_rankings',
        'detailed_scores',
        'winner_zone',
        'notes',
        'is_highlighted',
        'highlight_color',
        'bracket_round',
        'bracket_position',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'match_date' => 'date',
            'is_highlighted' => 'boolean',
            'bil' => 'integer',
            'bracket_position' => 'integer',
            'score_zone_a' => 'integer',
            'score_zone_b' => 'integer',
            'points_zone_a' => 'integer',
            'points_zone_b' => 'integer',
            'participating_zones' => 'array',
            'zone_rankings' => 'array',
            'detailed_scores' => 'json',
        ];
    }

    /**
     * Relationships
     */
    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function sportsSchedule(): BelongsTo
    {
        return $this->belongsTo(SportsSchedule::class, 'sports_schedule_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function zoneA(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_a', 'code');
    }

    public function zoneB(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_b', 'code');
    }

    public function winnerZone(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'winner_zone', 'code');
    }

    public function zoneResults(): HasMany
    {
        return $this->hasMany(MatchZoneResult::class, 'match_id');
    }

    /**
     * Scopes
     */
    public function scopeBySchedule($query, $scheduleId)
    {
        return $query->where('sports_schedule_id', $scheduleId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByMatchType($query, $matchType)
    {
        return $query->where('match_type', $matchType);
    }

    public function scopeByBracketRound($query, $round)
    {
        return $query->where('bracket_round', $round);
    }

    public function scopeHighlighted($query)
    {
        return $query->where('is_highlighted', true);
    }

    public function scopeByGender($query, $gender)
    {
        return $query->where('gender_type', $gender);
    }

    public function scopeByCourtField($query, $courtField)
    {
        return $query->where('court_field', $courtField);
    }

    public function scopeOrderedByBil($query)
    {
        return $query->orderBy('bil');
    }

    public function scopeOrderedByDateTime($query)
    {
        return $query->orderBy('match_date')->orderBy('match_time');
    }

    public function scopeHeadToHead($query)
    {
        return $query->where('event_type', 'head_to_head');
    }

    public function scopeGroupEvent($query)
    {
        return $query->where('event_type', 'group_event');
    }

    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }
}
