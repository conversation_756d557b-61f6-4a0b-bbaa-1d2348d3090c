<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SportTeam extends Model
{
    protected $fillable = [
        'zone_code',
        'sport_id',
        'team_leader_name',
        'team_leader_phone',
        'team_leader_contact',
    ];

    /**
     * Relationships
     */
    public function zone(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone_code', 'code');
    }

    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function players(): HasMany
    {
        return $this->hasMany(Player::class);
    }

    public function mainPlayers(): HasMany
    {
        return $this->hasMany(Player::class)->where('player_type', 'main');
    }

    public function substitutePlayers(): Has<PERSON>any
    {
        return $this->hasMany(Player::class)->where('player_type', 'substitute');
    }

    public function playerSports(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(PlayerSport::class);
    }

    /**
     * Scopes
     */
    public function scopeForZone($query, $zoneCode)
    {
        return $query->where('zone_code', $zoneCode);
    }

    public function scopeForSport($query, $sportId)
    {
        return $query->where('sport_id', $sportId);
    }

    /**
     * Helper methods
     */
    public function getMainPlayersCountAttribute()
    {
        return $this->mainPlayers()->count();
    }

    public function getSubstitutePlayersCountAttribute()
    {
        return $this->substitutePlayers()->count();
    }

    public function getTotalPlayersCountAttribute()
    {
        return $this->players()->count();
    }

    /**
     * Get team summary with player counts
     */
    public function getTeamSummary()
    {
        $mainPlayersCount = $this->mainPlayers()->count();
        $substitutePlayersCount = $this->substitutePlayers()->count();

        return [
            'id' => $this->id,
            'zone_code' => $this->zone_code,
            'sport_id' => $this->sport_id,
            'sport_name' => $this->sport->name ?? null,
            'sport_icon' => $this->sport->icon ?? null,
            'team_leader_name' => $this->team_leader_name,
            'team_leader_phone' => $this->team_leader_phone,
            'team_leader_contact' => $this->team_leader_contact,
            'main_players_count' => $mainPlayersCount,
            'substitute_players_count' => $substitutePlayersCount,
            'total_players_count' => $mainPlayersCount + $substitutePlayersCount,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Check if team is complete based on sport requirements
     */
    public function isTeamComplete()
    {
        $sport = $this->sport;
        if (!$sport) return false;

        $requiredMainPlayers = $sport->required_players ?? 11; // Default to 11 if not set
        $mainPlayersCount = $this->mainPlayers()->count();
        return $mainPlayersCount >= $requiredMainPlayers;
    }

    /**
     * Get team status
     */
    public function getTeamStatus()
    {
        if ($this->isTeamComplete()) {
            return 'complete';
        } elseif ($this->mainPlayers()->count() > 0) {
            return 'incomplete';
        } else {
            return 'empty';
        }
    }
}
