<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TournamentBracket extends Model
{
    protected $fillable = [
        'sport_id',
        'bracket_name',
        'bracket_structure',
        'tournament_type',
        'is_active',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'bracket_structure' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function matches(): HasMany
    {
        return $this->hasMany(SportMatch::class, 'sport_id', 'sport_id')
                    ->whereNotNull('bracket_round');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByTournamentType($query, $type)
    {
        return $query->where('tournament_type', $type);
    }

    /**
     * Helper methods
     */
    public function getBracketStructureAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setBracketStructureAttribute($value)
    {
        $this->attributes['bracket_structure'] = json_encode($value);
    }
}
