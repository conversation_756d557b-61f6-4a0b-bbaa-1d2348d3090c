<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TournamentResult extends Model
{
    protected $fillable = [
        'sport_id',
        'zone',
        'position',
        'points',
        'wins',
        'losses',
        'draws',
        'notes',
        'updated_by',
    ];

    protected $casts = [
        'position' => 'integer',
        'points' => 'integer',
        'wins' => 'integer',
        'losses' => 'integer',
        'draws' => 'integer',
    ];

    /**
     * Relationships
     */
    public function sport(): BelongsTo
    {
        return $this->belongsTo(Sport::class);
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function zoneModel(): BelongsTo
    {
        return $this->belongsTo(Zone::class, 'zone', 'code');
    }

    /**
     * Scopes
     */
    public function scopeByZone($query, string $zone)
    {
        return $query->where('zone', $zone);
    }

    public function scopeBySport($query, int $sportId)
    {
        return $query->where('sport_id', $sportId);
    }

    public function scopeByPosition($query, int $position)
    {
        return $query->where('position', $position);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('position')->orderBy('points', 'desc');
    }

    /**
     * Accessors
     */
    public function getTotalMatchesAttribute(): int
    {
        return $this->wins + $this->losses + $this->draws;
    }

    public function getWinRateAttribute(): float
    {
        $totalMatches = $this->total_matches;
        return $totalMatches > 0 ? ($this->wins / $totalMatches) * 100 : 0;
    }
}
