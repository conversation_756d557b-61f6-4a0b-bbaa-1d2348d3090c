<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'email',
        'name',
        'password',
        'role',
        'zone',
        'is_active',
        'must_change_password',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
            'must_change_password' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function createdSports()
    {
        return $this->hasMany(Sport::class, 'created_by');
    }

    public function registeredPlayers()
    {
        return $this->hasMany(Player::class, 'registered_by');
    }

    public function createdMatches()
    {
        return $this->hasMany(SportMatch::class, 'created_by');
    }

    public function createdNews()
    {
        return $this->hasMany(News::class, 'created_by');
    }

    public function uploadedGallery()
    {
        return $this->hasMany(Gallery::class, 'uploaded_by');
    }

    public function uploadedBackgroundImages()
    {
        return $this->hasMany(BackgroundImage::class, 'uploaded_by');
    }

    public function createdSportsRules()
    {
        return $this->hasMany(SportsRule::class, 'created_by');
    }

    public function updatedSportsRules()
    {
        return $this->hasMany(SportsRule::class, 'updated_by');
    }

    public function createdGeneralRules()
    {
        return $this->hasMany(GeneralRule::class, 'created_by');
    }

    public function updatedGeneralRules()
    {
        return $this->hasMany(GeneralRule::class, 'updated_by');
    }

    public function createdPenalties()
    {
        return $this->hasMany(Penalty::class, 'created_by');
    }

    public function updatedPenalties()
    {
        return $this->hasMany(Penalty::class, 'updated_by');
    }

    public function updatedPageContent()
    {
        return $this->hasMany(PageContent::class, 'updated_by');
    }

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return 'username';
    }

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->getAttribute($this->getAuthIdentifierName());
    }
}
