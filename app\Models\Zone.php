<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Zone extends Model
{
    protected $fillable = [
        'code',
        'name',
        'description',
        'logo_url',
        'color_primary',
        'color_secondary',
        'leader_id',
        'leader_name',
        'leader_contact',
        'is_active',
        'display_order',
        'updated_by',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function leader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'leader_id');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'zone', 'code');
    }

    public function players(): HasMany
    {
        return $this->hasMany(Player::class, 'zone', 'code');
    }

    public function matchesAsZoneA(): Has<PERSON>any
    {
        return $this->hasMany(GameMatch::class, 'zone_a', 'code');
    }

    public function matchesAsZoneB(): HasMany
    {
        return $this->hasMany(GameMatch::class, 'zone_b', 'code');
    }

    public function wonMatches(): HasMany
    {
        return $this->hasMany(GameMatch::class, 'winner_zone', 'code');
    }

    public function tournamentResults(): HasMany
    {
        return $this->hasMany(TournamentResult::class, 'zone', 'code');
    }

    public function sportTeams(): HasMany
    {
        return $this->hasMany(SportTeam::class, 'zone_code', 'code');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order');
    }

    /**
     * Helper methods
     */
    public static function getByCode($code)
    {
        return static::where('code', $code)->where('is_active', true)->first();
    }
}
