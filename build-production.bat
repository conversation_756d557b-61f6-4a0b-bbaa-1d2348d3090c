@echo off
echo Building for production...

REM Set production environment variables
set VITE_APP_NAME=INTRA-KOR
set VITE_APP_URL=https://sukan-intra-kkd-2025.mediakkd.com
set NODE_ENV=production

echo Environment variables set:
echo VITE_APP_NAME=%VITE_APP_NAME%
echo VITE_APP_URL=%VITE_APP_URL%
echo NODE_ENV=%NODE_ENV%

echo.
echo Running npm run build...
npm run build

echo.
echo Production build completed!
echo Built files are in public/build/

pause
