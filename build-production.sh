#!/bin/bash
echo "Building for production..."

# Set production environment variables
export VITE_APP_NAME=INTRA-KOR
export VITE_APP_URL=https://sukan-intra-kkd-2025.mediakkd.com
export NODE_ENV=production

echo "Environment variables set:"
echo "VITE_APP_NAME=$VITE_APP_NAME"
echo "VITE_APP_URL=$VITE_APP_URL"
echo "NODE_ENV=$NODE_ENV"

echo ""
echo "Running npm run build..."
npm run build

echo ""
echo "Production build completed!"
echo "Built files are in public/build/"
