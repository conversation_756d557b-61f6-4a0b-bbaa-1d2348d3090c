<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('matches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sport_id')->constrained('sports')->onDelete('cascade');
            $table->string('title');
            $table->string('zone_a', 20)->nullable();
            $table->string('zone_b', 20)->nullable();
            $table->date('match_date');
            $table->time('match_time');
            $table->string('venue')->nullable();
            $table->enum('status', ['scheduled', 'ongoing', 'completed', 'cancelled'])->default('scheduled');
            $table->integer('score_zone_a')->default(0);
            $table->integer('score_zone_b')->default(0);
            $table->string('winner_zone', 20)->nullable(); // Can be zone code or 'draw'
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['match_date', 'match_time']);
            $table->index(['sport_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('matches');
    }
};
