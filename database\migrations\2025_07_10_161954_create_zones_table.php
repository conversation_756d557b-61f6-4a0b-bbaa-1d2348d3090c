<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zones', function (Blueprint $table) {
            $table->id();
            $table->string('code', 20)->unique(); // Flexible code like 'A', 'B', 'C', 'BAKAT-KKD'
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('logo_url', 500)->nullable();
            $table->string('color_primary', 7)->default('#3B82F6'); // Hex color
            $table->string('color_secondary', 7)->default('#1E40AF'); // Hex color
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['is_active', 'display_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zones');
    }
};
