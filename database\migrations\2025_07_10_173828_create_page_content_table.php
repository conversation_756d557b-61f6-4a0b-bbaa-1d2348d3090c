<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_content', function (Blueprint $table) {
            $table->id();
            $table->string('page')->index(); // homepage, match-schedule, tournament-results, gallery, news, rules
            $table->string('section')->nullable(); // hero, sports, stats, cta (for homepage)
            $table->string('content_key'); // title, subtitle, description, background_image
            $table->text('content_value')->nullable();
            $table->string('content_type')->default('text'); // text, image, json, html
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['page', 'section', 'content_key']);
            $table->index(['page', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_content');
    }
};
