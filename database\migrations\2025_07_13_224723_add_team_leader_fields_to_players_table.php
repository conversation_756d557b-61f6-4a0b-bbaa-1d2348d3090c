<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('players', function (Blueprint $table) {
            $table->text('address')->nullable()->after('email');
            $table->string('team_leader_name')->nullable()->after('address');
            $table->string('team_leader_phone', 20)->nullable()->after('team_leader_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('players', function (Blueprint $table) {
            $table->dropColumn(['address', 'team_leader_name', 'team_leader_phone']);
        });
    }
};
