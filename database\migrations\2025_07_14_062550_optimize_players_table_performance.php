<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('players', function (Blueprint $table) {
            // Add indexes for better performance
            $table->index(['name'], 'players_name_index');
            $table->index(['email'], 'players_email_index');
            $table->index(['phone'], 'players_phone_index');
            $table->index(['service_number'], 'players_service_number_index');
            $table->index(['zone', 'name'], 'players_zone_name_index');
            $table->index(['is_active', 'zone'], 'players_active_zone_index');
            $table->index(['created_at'], 'players_created_at_index');

            // Add status field as string to match frontend
            $table->string('status', 20)->default('active')->after('is_active');
        });

        Schema::table('player_sports', function (Blueprint $table) {
            // Add indexes for better performance
            $table->index(['player_id'], 'player_sports_player_index');
            $table->index(['sport_id'], 'player_sports_sport_index');
        });

        // Update existing records to have status field
        DB::statement("UPDATE players SET status = CASE WHEN is_active = 1 THEN 'active' ELSE 'inactive' END");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('players', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('players_name_index');
            $table->dropIndex('players_email_index');
            $table->dropIndex('players_phone_index');
            $table->dropIndex('players_service_number_index');
            $table->dropIndex('players_zone_name_index');
            $table->dropIndex('players_active_zone_index');
            $table->dropIndex('players_created_at_index');

            // Drop status field
            $table->dropColumn('status');
        });

        Schema::table('player_sports', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('player_sports_player_index');
            $table->dropIndex('player_sports_sport_index');
        });
    }
};
