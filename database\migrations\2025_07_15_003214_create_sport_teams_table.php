<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sport_teams', function (Blueprint $table) {
            $table->id();
            $table->string('zone_code', 20);
            $table->unsignedBigInteger('sport_id');
            $table->string('team_leader_name');
            $table->string('team_leader_phone', 20);
            $table->string('team_leader_contact')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('zone_code')->references('code')->on('zones')->onDelete('cascade');
            $table->foreign('sport_id')->references('id')->on('sports')->onDelete('cascade');

            // Unique constraint - one team per zone per sport
            $table->unique(['zone_code', 'sport_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sport_teams');
    }
};
