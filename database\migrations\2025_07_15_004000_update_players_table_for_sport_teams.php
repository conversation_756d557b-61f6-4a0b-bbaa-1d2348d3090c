<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('players', function (Blueprint $table) {
            // Remove email field
            $table->dropColumn('email');
            
            // Remove team leader fields (moved to sport_teams table)
            $table->dropColumn(['team_leader_name', 'team_leader_phone']);
            
            // Add player type for main vs substitute players
            $table->enum('player_type', ['main', 'substitute'])->default('main')->after('status');
            
            // Add sport team relationship
            $table->unsignedBigInteger('sport_team_id')->nullable()->after('zone');
            $table->foreign('sport_team_id')->references('id')->on('sport_teams')->onDelete('set null');
            
            // Add index for better performance
            $table->index('sport_team_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('players', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['sport_team_id']);
            $table->dropColumn(['sport_team_id', 'player_type']);
            
            // Re-add removed columns
            $table->string('email')->nullable()->after('name');
            $table->string('team_leader_name')->nullable()->after('address');
            $table->string('team_leader_phone', 20)->nullable()->after('team_leader_name');
        });
    }
};
