<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('player_sports', function (Blueprint $table) {
            // Add sport team relationship to pivot table
            $table->unsignedBigInteger('sport_team_id')->nullable()->after('sport_id');
            $table->foreign('sport_team_id')->references('id')->on('sport_teams')->onDelete('cascade');
            
            // Add index for better performance
            $table->index('sport_team_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('player_sports', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['sport_team_id']);
            $table->dropColumn('sport_team_id');
        });
    }
};
