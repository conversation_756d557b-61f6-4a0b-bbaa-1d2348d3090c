<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Add fields for jadual pertandingan based on documentation
            $table->integer('bil')->nullable()->after('id'); // Sequence number
            $table->time('end_time')->nullable()->after('match_time'); // End time
            $table->string('match_type', 100)->nullable()->after('title'); // 1,2,3,Final, Final Lelaki, etc.
            $table->string('court_field', 10)->nullable()->after('venue'); // A,B,C for courts/tables
            $table->string('category', 100)->nullable()->after('court_field'); // Single Men, Double Women, etc.
            $table->enum('gender_type', ['Lelaki', 'Perempuan', 'Mixed'])->nullable()->after('category');
            $table->boolean('is_highlighted')->default(false)->after('gender_type'); // For finals
            $table->string('highlight_color', 50)->nullable()->after('is_highlighted'); // kuning, merah jambu, etc.
            $table->string('bracket_round', 100)->nullable()->after('highlight_color'); // First Round, Semi-Final, etc.
            $table->integer('bracket_position')->nullable()->after('bracket_round'); // Position in bracket
            $table->string('special_teams', 100)->nullable()->after('zone_b'); // BAKAT, etc.

            // Add indexes for better performance
            $table->index(['bil', 'sport_id']);
            $table->index(['match_type', 'sport_id']);
            $table->index(['category', 'sport_id']);
            $table->index(['bracket_round', 'sport_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            $table->dropIndex(['bil', 'sport_id']);
            $table->dropIndex(['match_type', 'sport_id']);
            $table->dropIndex(['category', 'sport_id']);
            $table->dropIndex(['bracket_round', 'sport_id']);

            $table->dropColumn([
                'bil',
                'end_time',
                'match_type',
                'court_field',
                'category',
                'gender_type',
                'is_highlighted',
                'highlight_color',
                'bracket_round',
                'bracket_position',
                'special_teams'
            ]);
        });
    }
};
