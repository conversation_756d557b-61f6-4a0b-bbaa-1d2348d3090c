<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sports_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sport_id')->constrained('sports')->onDelete('cascade');
            $table->string('schedule_name'); // e.g., "Acara: <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> 10 x 400 meter"
            $table->text('description')->nullable();
            $table->date('start_date');
            $table->date('end_date');
            $table->string('main_venue')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['sport_id', 'is_active']);
            $table->index(['start_date', 'end_date']);
            $table->index(['display_order', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sports_schedules');
    }
};
