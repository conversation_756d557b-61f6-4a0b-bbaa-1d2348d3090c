<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            $table->foreignId('sports_schedule_id')->nullable()->after('sport_id')->constrained('sports_schedules')->onDelete('cascade');
            $table->index(['sports_schedule_id', 'bil']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            $table->dropIndex(['sports_schedule_id', 'bil']);
            $table->dropForeign(['sports_schedule_id']);
            $table->dropColumn('sports_schedule_id');
        });
    }
};
