<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Add fields for group events support
            $table->enum('event_type', ['head_to_head', 'group_event'])->default('head_to_head')->after('sport_id');
            $table->json('participating_zones')->nullable()->after('zone_b'); // For group events: ["1","2","3","4","5","6"]
            
            // Add fields for point allocation system
            $table->integer('points_zone_a')->default(0)->after('score_zone_a'); // Points earned by zone A
            $table->integer('points_zone_b')->default(0)->after('score_zone_b'); // Points earned by zone B
            $table->json('zone_rankings')->nullable()->after('points_zone_b'); // For group events: [{"zone":"1","position":1,"score":"04:25.30","points":10}]
            
            // Add fields for sport-specific data
            $table->string('time_result_a', 20)->nullable()->after('points_zone_a'); // For time-based sports (MM:SS.ms)
            $table->string('time_result_b', 20)->nullable()->after('points_zone_b'); // For time-based sports (MM:SS.ms)
            $table->json('detailed_scores')->nullable()->after('zone_rankings'); // For complex scoring: {"sets":[{"zone_a":25,"zone_b":23},{"zone_a":22,"zone_b":25}]}
            
            // Add indexes for better performance
            $table->index(['event_type', 'sport_id']);
            $table->index(['points_zone_a', 'points_zone_b']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['event_type', 'sport_id']);
            $table->dropIndex(['points_zone_a', 'points_zone_b']);
            
            // Drop columns
            $table->dropColumn([
                'event_type',
                'participating_zones',
                'points_zone_a',
                'points_zone_b',
                'zone_rankings',
                'time_result_a',
                'time_result_b',
                'detailed_scores'
            ]);
        });
    }
};
