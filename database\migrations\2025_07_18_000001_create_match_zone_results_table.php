<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_zone_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('match_id')->constrained('matches')->onDelete('cascade');
            $table->string('zone_code', 20); // Zone identifier (1, 2, 3, etc.)
            $table->integer('final_score')->default(0); // Final score/time for this zone
            $table->string('time_result', 20)->nullable(); // For time-based sports (MM:SS.ms)
            $table->integer('ranking_position')->nullable(); // 1st, 2nd, 3rd, etc.
            $table->integer('points_earned')->default(0); // Points earned based on position (10, 8, 6, 4, 2, 1)
            $table->json('detailed_results')->nullable(); // For complex results: {"categories":{"MS":"win","WS":"loss"}}
            $table->text('notes')->nullable(); // Additional notes for this zone's performance
            $table->timestamps();

            // Indexes for performance
            $table->index(['match_id', 'zone_code']);
            $table->index(['match_id', 'ranking_position']);
            $table->index(['zone_code', 'points_earned']);
            
            // Unique constraint to prevent duplicate zone results for same match
            $table->unique(['match_id', 'zone_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_zone_results');
    }
};
