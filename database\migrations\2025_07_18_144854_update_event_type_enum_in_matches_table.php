<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Update event_type enum to include new types
            $table->enum('event_type', ['head_to_head', 'group_event', 'knockout_group', 'category_based'])
                  ->default('head_to_head')
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Revert back to original enum values
            $table->enum('event_type', ['head_to_head', 'group_event'])
                  ->default('head_to_head')
                  ->change();
        });
    }
};
