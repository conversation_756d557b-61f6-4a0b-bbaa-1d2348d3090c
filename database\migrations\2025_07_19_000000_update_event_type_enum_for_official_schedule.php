<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Update event_type enum to include all required types for official schedule
            $table->enum('event_type', [
                'head_to_head',                // Basic 1v1 matches
                'group_event',                 // <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> (all zones compete together)
                'knockout_group',              // Knockout tournament
                'category_based',              // Legacy category-based (not used in official schedule)
                'round_robin_final',           // Round Robin + Final (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
                'category_based_round_robin',  // Category Round Robin (<PERSON><PERSON><PERSON>, <PERSON>)
                'knockout_bracket_category'    // Knockout + Category (Badminton Serikandi)
            ])
            ->default('head_to_head')
            ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('matches', function (Blueprint $table) {
            // Revert back to previous enum values
            $table->enum('event_type', ['head_to_head', 'group_event', 'knockout_group', 'category_based'])
                  ->default('head_to_head')
                  ->change();
        });
    }
};
