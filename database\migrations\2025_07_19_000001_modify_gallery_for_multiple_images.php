<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gallery', function (Blueprint $table) {
            // Change file_url to JSON to store multiple image URLs
            $table->json('file_urls')->nullable()->after('description');
            
            // Add image count for quick reference
            $table->integer('image_count')->default(1)->after('file_urls');
            
            // Keep original file_url for backward compatibility (will be deprecated)
            // We'll populate file_urls from existing file_url data
        });

        // Migrate existing data
        DB::statement("
            UPDATE gallery 
            SET file_urls = JSON_ARRAY(file_url),
                image_count = 1
            WHERE file_urls IS NULL
        ");

        // Add index for better performance
        Schema::table('gallery', function (Blueprint $table) {
            $table->index(['file_type', 'image_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gallery', function (Blueprint $table) {
            $table->dropIndex(['file_type', 'image_count']);
            $table->dropColumn(['file_urls', 'image_count']);
        });
    }
};
