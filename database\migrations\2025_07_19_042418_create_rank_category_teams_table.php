<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rank_category_teams', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sport_id')->constrained('sports')->onDelete('cascade');
            $table->foreignId('rank_category_id')->constrained('rank_categories')->onDelete('cascade');
            $table->string('team_leader_name');
            $table->string('team_leader_phone');
            $table->string('team_leader_contact')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['sport_id', 'rank_category_id']);
            $table->index(['sport_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rank_category_teams');
    }
};
