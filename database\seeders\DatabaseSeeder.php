<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core Data
            ZoneSeeder::class,
            UserSeeder::class,
            SportSeeder::class, // Managed by superadmin via CMS
            RankCategorySeeder::class, // Rank categories for players
            SiteSettingSeeder::class,

            // Players and Matches (depends on zones, users, sports)
            // PlayerSeeder::class,
            // MatchSeeder::class,

            // Jadual Pertandingan (depends on sports)
            SportsScheduleSeeder::class,
            // JadualPertandinganSeeder::class, // Replaced with OfficialScheduleSeeder
            OfficialScheduleSeeder::class, // Official schedule from 5 pages jadual rasmi (includes all brackets)
            // TournamentBracketSeeder::class, // Disabled - OfficialScheduleSeeder includes all tournament data

            // Statistics
            StatisticSeeder::class,

            // CMS Content
            PageContentSeeder::class,
            SportsRuleSeeder::class,
            GeneralRuleSeeder::class,
            PenaltySeeder::class,

            // Media Content
            NewsSeeder::class,
            GallerySeeder::class,
        ]);
    }
}
