<?php

namespace Database\Seeders;

use App\Models\Gallery;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class GallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mediaUsers = User::where('role', 'media')->get();

        $galleryItems = [
            [
                'title' => 'Majlis Pelancaran Portal Sukan Intra KKD 2025',
                'description' => 'Majlis pelancaran rasmi portal sukan yang dihadiri oleh pegawai kanan KKD dan wakil dari semua zon.',
                'file_url' => 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                'file_type' => 'image',
                'category' => 'event',
                'zone' => 'all',
                'is_featured' => true,
                'views' => rand(50, 150),
                'created_at' => Carbon::now()->subDays(30),
            ],
            [
                'title' => 'Sesi Taklimat Teknikal Untuk Semua Zon',
                'description' => 'Sesi taklimat teknikal mengenai peraturan pertandingan dan prosedur kejohanan.',
                'file_url' => 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                'file_type' => 'image',
                'category' => 'event',
                'zone' => 'all',
                'is_featured' => false,
                'views' => rand(30, 80),
                'created_at' => Carbon::now()->subDays(25),
            ],
            [
                'title' => 'Latihan Intensif Pasukan Bola Sepak',
                'description' => 'Sesi latihan intensif pasukan bola sepak dari semua zon dalam persiapan menghadapi kejohanan.',
                'file_url' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                'file_type' => 'image',
                'category' => 'football',
                'zone' => 'all',
                'is_featured' => false,
                'views' => rand(25, 75),
                'created_at' => Carbon::now()->subDays(18),
            ],
            [
                'title' => 'Latihan Badminton di Dewan Indoor',
                'description' => 'Pemain badminton mengasah kemahiran dalam sesi latihan di dewan badminton.',
                'file_url' => 'https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                'file_type' => 'image',
                'category' => 'badminton',
                'zone' => 'all',
                'is_featured' => false,
                'views' => rand(18, 55),
                'created_at' => Carbon::now()->subDays(10),
            ],
            [
                'title' => 'Persiapan Peralatan Sukan',
                'description' => 'Pemeriksaan dan penyediaan peralatan sukan untuk semua kategori pertandingan.',
                'file_url' => 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
                'file_type' => 'image',
                'category' => 'preparation',
                'zone' => 'all',
                'is_featured' => false,
                'views' => rand(15, 50),
                'created_at' => Carbon::now()->subDays(3),
            ],
        ];

        foreach ($galleryItems as $index => $item) {
            $author = $mediaUsers->random();

            Gallery::create([
                'title' => $item['title'],
                'description' => $item['description'],
                'file_url' => $item['file_url'],
                'file_type' => $item['file_type'],
                'category' => $item['category'],
                'zone' => $item['zone'],
                'is_featured' => $item['is_featured'],
                'views' => $item['views'],
                'uploaded_by' => $author->id,
                'created_at' => $item['created_at'],
                'updated_at' => $item['created_at'],
            ]);
        }
    }
}
