<?php

namespace Database\Seeders;

use App\Models\GeneralRule;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GeneralRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $generalRules = [
            [
                'rule_text' => 'Semua peserta mesti mendaftar melalui Portal Sukan Intra Kor Kesihatan DiRaja sebelum tarikh tutup pendaftaran',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'rule_text' => '<PERSON>ya anggota Kor Kesihatan DiRaja yang layak untuk menyertai pertandingan ini',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Setiap peserta boleh menyertai lebih dari satu kategori sukan mengikut zone masing-masing',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Peserta mesti hadir 30 minit sebelum masa pertandingan bermula di lokasi yang ditetapkan',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Pakaian sukan yang sesuai dan seragam zone adalah wajib untuk semua peserta',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Sebarang bentuk kecurangan atau kelakuan tidak sportif akan mengakibatkan diskualifikasi',
                'sort_order' => 6,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Keputusan pengadil dan jawatankuasa teknikal adalah muktamad dan tidak boleh dipertikaikan',
                'sort_order' => 7,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Peserta yang tidak hadir tanpa notis akan dianggap mengundurkan diri dari pertandingan',
                'sort_order' => 8,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Semua peserta mesti menghormati lawan, pengadil, dan pegawai pertandingan',
                'sort_order' => 9,
                'is_active' => true,
            ],
            [
                'rule_text' => 'Penggunaan bahan terlarang atau doping adalah dilarang sama sekali',
                'sort_order' => 10,
                'is_active' => true,
            ],
        ];

        foreach ($generalRules as $rule) {
            GeneralRule::updateOrCreate(
                ['rule_text' => $rule['rule_text']], // Find by rule_text
                $rule // Update or create with this data
            );
        }
    }
}
