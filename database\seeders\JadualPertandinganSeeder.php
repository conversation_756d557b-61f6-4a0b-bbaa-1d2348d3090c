<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SportMatch;
use App\Models\SportsSchedule;
use App\Models\Sport;
use Carbon\Carbon;

class JadualPertandinganSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get sports and schedules for reference
        $sports = Sport::whereIn('name', [
            '<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON> Tampar',
            'Badminton',
            'Ping Pong'
        ])->get()->keyBy('name');

        $schedules = SportsSchedule::with('sport')->get()->keyBy('schedule_name');

        // 1. <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> 10 x 400 meter
        $this->seedLariLari($sports, $schedules);

        // 2. <PERSON>la <PERSON>ak
        $this->seedBolaSepak($sports, $schedules);

        // 3. <PERSON><PERSON> (Perempuan)
        $this->seedBolaJaring($sports, $schedules);

        // 4. <PERSON><PERSON> (Lelaki & Perempuan)
        $this->seedBolaTampar($sports, $schedules);

        // 5. <PERSON><PERSON><PERSON>on (Mixed Categories)
        $this->seedBadmintonZon($sports, $schedules);

        // 6. Badminton Serikandi (Women Only)
        $this->seedBadmintonSerikandi($sports, $schedules);

        // 7. Ping Pong (Mixed Categories)
        $this->seedPingPong($sports, $schedules);
    }

    private function seedLariLari($sports, $schedules)
    {
        $sport = $sports->get('Lari-Lari Berganti-ganti');
        $schedule = $schedules->get('Acara: Lari-Lari Berganti-ganti 10 x 400 meter');

        if ($sport && $schedule) {
            SportMatch::create([
                'sport_id' => $sport->id,
                'sports_schedule_id' => $schedule->id,
                'bil' => 1,
                'title' => 'Final Lari-Lari Berganti-ganti 10 x 400 meter',
                'match_type' => 'Final',
                'zone_a' => '1',
                'zone_b' => '2',
                'special_teams' => '3',
                'match_date' => Carbon::parse('2025-07-21'),
                'match_time' => '21:00',
                'end_time' => '21:30',
                'venue' => 'Stadium PU Sendayan',
                'status' => 'scheduled',
                'is_highlighted' => true,
                'highlight_color' => 'kuning',
                'notes' => 'Zon 1: Lorong 1, Zon 2: Lorong 3, Zon 3: Lorong 2',
                'created_by' => 1,
            ]);
        }
    }

    private function seedBolaSepak($sports, $schedules)
    {
        $sport = $sports->get('Bola Sepak');
        $schedule = $schedules->get('Acara: Bola Sepak');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 1,
                    'title' => 'Perlawanan 1 - Bola Sepak',
                    'match_type' => '1',
                    'zone_a' => '3',
                    'zone_b' => '1',
                    'match_date' => '2025-07-22',
                    'match_time' => '08:00',
                    'end_time' => '09:30',
                ],
                [
                    'bil' => 2,
                    'title' => 'Perlawanan 2 - Bola Sepak',
                    'match_type' => '2',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-23',
                    'match_time' => '20:00',
                    'end_time' => '21:30',
                ],
                [
                    'bil' => 3,
                    'title' => 'Perlawanan 3 - Bola Sepak',
                    'match_type' => '3',
                    'zone_a' => '3',
                    'zone_b' => '2',
                    'match_date' => '2025-07-23',
                    'match_time' => '08:00',
                    'end_time' => '09:30',
                ],
                [
                    'bil' => 4,
                    'title' => 'Final Bola Sepak',
                    'match_type' => 'Final',
                    'zone_a' => 'FINAL',
                    'zone_b' => 'FINAL',
                    'match_date' => '2025-07-24',
                    'match_time' => '08:00',
                    'end_time' => '10:00',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Stadium PU Sendayan',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    private function seedBolaJaring($sports, $schedules)
    {
        $sport = $sports->get('Bola Jaring');
        $schedule = $schedules->get('Acara: Bola Jaring (Perempuan)');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 1,
                    'title' => 'Perlawanan 1 - Bola Jaring',
                    'match_type' => '1',
                    'zone_a' => '3',
                    'special_teams' => 'BAKAT',
                    'match_date' => '2025-07-22',
                    'match_time' => '09:00',
                    'end_time' => '10:45',
                    'gender_type' => 'Perempuan',
                ],
                [
                    'bil' => 2,
                    'title' => 'Perlawanan 2 - Bola Jaring',
                    'match_type' => '2',
                    'zone_a' => '2',
                    'zone_b' => '1',
                    'match_date' => '2025-07-22',
                    'match_time' => '11:00',
                    'end_time' => '11:45',
                    'gender_type' => 'Perempuan',
                ],
                [
                    'bil' => 7,
                    'title' => 'Final Bola Jaring',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon Y',
                    'zone_b' => 'Zon Z',
                    'match_date' => '2025-07-22',
                    'match_time' => '16:15',
                    'end_time' => '17:00',
                    'gender_type' => 'Perempuan',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Kompleks Sukan PU Sendayan',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    private function seedBolaTampar($sports, $schedules)
    {
        $sport = $sports->get('Bola Tampar');
        $schedule = $schedules->get('Acara: Bola Tampar (Lelaki & Perempuan)');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 4,
                    'title' => 'Final Bola Tampar Lelaki',
                    'match_type' => 'Final Lelaki',
                    'zone_a' => 'Zon W',
                    'zone_b' => 'Zon W',
                    'match_date' => '2025-07-22',
                    'match_time' => '14:30',
                    'end_time' => '16:00',
                    'gender_type' => 'Lelaki',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
                [
                    'bil' => 8,
                    'title' => 'Final Bola Tampar Perempuan',
                    'match_type' => 'Final Perempuan',
                    'zone_a' => 'Zon Y',
                    'zone_b' => 'Zon Z',
                    'match_date' => '2025-07-23',
                    'match_time' => '14:30',
                    'end_time' => '16:00',
                    'gender_type' => 'Perempuan',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Kompleks Sukan PU Sendayan',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    private function seedBadmintonZon($sports, $schedules)
    {
        $sport = $sports->get('Badminton');
        $schedule = $schedules->get('Acara: Badminton Zon (Mixed Categories)');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 1,
                    'title' => 'Badminton Zon - Single Men',
                    'match_type' => '1',
                    'zone_a' => '3',
                    'zone_b' => '1',
                    'match_date' => '2025-07-23',
                    'match_time' => '08:00',
                    'end_time' => '08:45',
                    'court_field' => 'A',
                    'category' => 'Single Men',
                    'gender_type' => 'Mixed',
                ],
                [
                    'bil' => 2,
                    'title' => 'Badminton Zon - Double Women',
                    'match_type' => '2',
                    'zone_a' => '3',
                    'zone_b' => '1',
                    'match_date' => '2025-07-23',
                    'match_time' => '08:00',
                    'end_time' => '08:45',
                    'court_field' => 'B',
                    'category' => 'Double Women',
                    'gender_type' => 'Mixed',
                    'highlight_color' => 'merah jambu',
                ],
                [
                    'bil' => 3,
                    'title' => 'Badminton Zon - Double Men A',
                    'match_type' => '3',
                    'zone_a' => '1',
                    'zone_b' => '2',
                    'match_date' => '2025-07-23',
                    'match_time' => '08:00',
                    'end_time' => '08:45',
                    'court_field' => 'C',
                    'category' => 'Double Men A',
                    'gender_type' => 'Mixed',
                    'highlight_color' => 'kuning',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Dewan Angkasaraya',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    private function seedBadmintonSerikandi($sports, $schedules)
    {
        $sport = $sports->get('Badminton');
        $schedule = $schedules->get('Acara: Badminton Serikandi (Single Women, Double Women)');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 7,
                    'title' => 'Final Single Women',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon Y',
                    'zone_b' => 'Zon Z',
                    'match_date' => '2025-07-23',
                    'match_time' => '16:15',
                    'end_time' => '17:00',
                    'court_field' => 'A',
                    'category' => 'Single Women',
                    'gender_type' => 'Perempuan',
                    'bracket_round' => 'Final',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
                [
                    'bil' => 8,
                    'title' => 'Final Double Women',
                    'match_type' => 'Final',
                    'zone_a' => 'Zon Y',
                    'zone_b' => 'Zon Z',
                    'match_date' => '2025-07-23',
                    'match_time' => '16:15',
                    'end_time' => '17:00',
                    'court_field' => 'B',
                    'category' => 'Double Women',
                    'gender_type' => 'Perempuan',
                    'bracket_round' => 'Final',
                    'is_highlighted' => true,
                    'highlight_color' => 'kuning',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Dewan Angkasaraya',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }

    private function seedPingPong($sports, $schedules)
    {
        $sport = $sports->get('Ping Pong');
        $schedule = $schedules->get('Acara: Ping Pong (Mixed Categories)');

        if ($sport && $schedule) {
            $matches = [
                [
                    'bil' => 1,
                    'title' => 'Ping Pong - Single Men',
                    'match_type' => '1',
                    'zone_a' => '3',
                    'zone_b' => '2',
                    'match_date' => '2025-07-22',
                    'match_time' => '09:00',
                    'end_time' => '09:45',
                    'court_field' => 'A',
                    'category' => 'Single Men',
                    'gender_type' => 'Mixed',
                ],
                [
                    'bil' => 2,
                    'title' => 'Ping Pong - Double Women',
                    'match_type' => '2',
                    'zone_a' => '3',
                    'zone_b' => '1',
                    'match_date' => '2025-07-22',
                    'match_time' => '09:00',
                    'end_time' => '09:45',
                    'court_field' => 'B',
                    'category' => 'Double Women',
                    'gender_type' => 'Mixed',
                    'highlight_color' => 'merah jambu',
                ],
                [
                    'bil' => 3,
                    'title' => 'Ping Pong - Double Men A',
                    'match_type' => '3',
                    'zone_a' => '2',
                    'zone_b' => '1',
                    'match_date' => '2025-07-22',
                    'match_time' => '09:00',
                    'end_time' => '09:45',
                    'court_field' => 'C',
                    'category' => 'Double Men A',
                    'gender_type' => 'Mixed',
                    'highlight_color' => 'oren',
                ],
            ];

            foreach ($matches as $matchData) {
                SportMatch::create(array_merge($matchData, [
                    'sport_id' => $sport->id,
                    'sports_schedule_id' => $schedule->id,
                    'venue' => 'Dewan Angkasaraya',
                    'status' => 'scheduled',
                    'created_by' => 1,
                ]));
            }
        }
    }
}
