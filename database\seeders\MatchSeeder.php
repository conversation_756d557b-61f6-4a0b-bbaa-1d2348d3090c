<?php

namespace Database\Seeders;

use App\Models\GameMatch;
use App\Models\Sport;
use App\Models\Zone;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class MatchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $zones = Zone::all();
        $sports = Sport::all();
        
        // Tournament dates: 21-25 July 2025
        $startDate = Carbon::create(2025, 7, 21);
        $endDate = Carbon::create(2025, 7, 25);
        
        $venues = [
            'football' => 'Padang Bola Sepak Utama',
            'netball' => 'Gelanggang Bola Jaring',
            'relay' => 'Trek Atletik 400m',
            'badminton' => 'Dewan Badminton Indoor',
            'table-tennis' => 'Dewan Ping Pong',
            'volleyball' => 'Dewan Bola Tampar',
            'traditional' => 'Kawasan Sukan Rakyat'
        ];
        
        $matchTypes = ['group_stage', 'quarter_final', 'semi_final', 'final'];
        $statuses = ['scheduled', 'ongoing', 'completed'];
        
        foreach ($sports as $sport) {
            $venue = $venues[$sport->slug] ?? 'Venue Utama';
            
            // Create matches between zones
            $zoneList = $zones->toArray();
            $matchCount = 0;
            
            // Group stage matches (round robin)
            for ($i = 0; $i < count($zoneList); $i++) {
                for ($j = $i + 1; $j < count($zoneList); $j++) {
                    $zone1 = $zoneList[$i];
                    $zone2 = $zoneList[$j];
                    
                    // Distribute matches across tournament days
                    $matchDay = $startDate->copy()->addDays($matchCount % 5);
                    $matchTime = $matchDay->copy()->setTime(rand(8, 17), rand(0, 1) * 30);
                    
                    $status = $matchTime->isPast() ? 'completed' : 'scheduled';
                    
                    $match = GameMatch::create([
                        'sport_id' => $sport->id,
                        'title' => $sport->name . ' - ' . $zone1['name'] . ' vs ' . $zone2['name'],
                        'zone_a' => $zone1['code'],
                        'zone_b' => $zone2['code'],
                        'match_date' => $matchTime->format('Y-m-d'),
                        'match_time' => $matchTime->format('H:i:s'),
                        'venue' => $venue,
                        'status' => $status,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    
                    // Add scores for completed matches
                    if ($status === 'completed') {
                        $scoreA = rand(0, 5);
                        $scoreB = rand(0, 5);

                        $winnerZone = $scoreA > $scoreB ? $zone1['code'] :
                                     ($scoreB > $scoreA ? $zone2['code'] : 'draw');

                        $match->update([
                            'score_zone_a' => $scoreA,
                            'score_zone_b' => $scoreB,
                            'winner_zone' => $winnerZone,
                            'notes' => 'Perlawanan yang sangat sengit dan menarik.',
                        ]);
                    }
                    
                    $matchCount++;
                }
            }
            
            // Create knockout stage matches (semi-finals and finals)
            if ($zones->count() >= 4) {
                // Semi-final 1
                $scoreA1 = rand(1, 4);
                $scoreB1 = rand(0, 3);
                $winner1 = $scoreA1 > $scoreB1 ? $zones[0]->code : $zones[1]->code;

                $semiFinal1 = GameMatch::create([
                    'sport_id' => $sport->id,
                    'title' => $sport->name . ' Semi Final 1 - ' . $zones[0]->name . ' vs ' . $zones[1]->name,
                    'zone_a' => $zones[0]->code,
                    'zone_b' => $zones[1]->code,
                    'match_date' => $endDate->copy()->subDay()->format('Y-m-d'),
                    'match_time' => '14:00:00',
                    'venue' => $venue,
                    'status' => 'completed',
                    'score_zone_a' => $scoreA1,
                    'score_zone_b' => $scoreB1,
                    'winner_zone' => $winner1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Semi-final 2
                $scoreA2 = rand(0, 3);
                $scoreB2 = rand(1, 4);
                $winner2 = $scoreA2 > $scoreB2 ? $zones[2]->code : ($zones[3]->code ?? $zones[0]->code);

                $semiFinal2 = GameMatch::create([
                    'sport_id' => $sport->id,
                    'title' => $sport->name . ' Semi Final 2 - ' . $zones[2]->name . ' vs ' . ($zones[3]->name ?? $zones[0]->name),
                    'zone_a' => $zones[2]->code,
                    'zone_b' => $zones[3]->code ?? $zones[0]->code,
                    'match_date' => $endDate->copy()->subDay()->format('Y-m-d'),
                    'match_time' => '16:00:00',
                    'venue' => $venue,
                    'status' => 'completed',
                    'score_zone_a' => $scoreA2,
                    'score_zone_b' => $scoreB2,
                    'winner_zone' => $winner2,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Final
                $finalZoneA = Zone::where('code', $winner1)->first();
                $finalZoneB = Zone::where('code', $winner2)->first();

                GameMatch::create([
                    'sport_id' => $sport->id,
                    'title' => $sport->name . ' Final - ' . $finalZoneA->name . ' vs ' . $finalZoneB->name,
                    'zone_a' => $winner1,
                    'zone_b' => $winner2,
                    'match_date' => $endDate->format('Y-m-d'),
                    'match_time' => '19:00:00',
                    'venue' => $venue,
                    'status' => 'scheduled',
                    'notes' => 'Perlawanan akhir yang dinanti-nantikan!',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
