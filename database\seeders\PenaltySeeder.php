<?php

namespace Database\Seeders;

use App\Models\Penalty;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PenaltySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $penalties = [
            [
                'offense' => 'Tidak hadir tanpa notis awal',
                'penalty_description' => 'Diskualifikasi dari pertandingan dan zone kehilangan mata',
                'severity' => 'high',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'offense' => 'Kelakuan tidak sopan atau tidak menghormati lawan',
                'penalty_description' => '<PERSON>an pertama, diskualifikasi jika berulang',
                'severity' => 'medium',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'offense' => 'Kecurangan atau penggunaan bahan terlarang',
                'penalty_description' => 'Diskualifikasi segera dan laporan kepada pihak atasan',
                'severity' => 'high',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'offense' => 'Lewat hadir (5-15 minit)',
                'penalty_description' => 'Amaran dan pengurangan masa persediaan',
                'severity' => 'low',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'offense' => 'Lewat hadir (lebih 15 minit)',
                'penalty_description' => 'Diskualifikasi automatik dari pertandingan',
                'severity' => 'high',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'offense' => 'Tidak memakai pakaian seragam zone',
                'penalty_description' => 'Tidak dibenarkan bertanding sehingga memakai pakaian yang betul',
                'severity' => 'medium',
                'sort_order' => 6,
                'is_active' => true,
            ],
            [
                'offense' => 'Mempertikaikan keputusan pengadil secara tidak sopan',
                'penalty_description' => 'Amaran atau diskualifikasi bergantung kepada tahap kesalahan',
                'severity' => 'medium',
                'sort_order' => 7,
                'is_active' => true,
            ],
        ];

        foreach ($penalties as $penalty) {
            Penalty::updateOrCreate(
                ['offense' => $penalty['offense']], // Find by offense
                $penalty // Update or create with this data
            );
        }
    }
}
