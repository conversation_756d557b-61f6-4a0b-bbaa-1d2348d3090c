<?php

namespace Database\Seeders;

use App\Models\Player;
use App\Models\Sport;
use App\Models\Zone;
use Illuminate\Database\Seeder;

class PlayerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $zones = Zone::all();
        $sports = Sport::all();

        $playerNames = [
            // <PERSON><PERSON><PERSON>
            '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON> bin <PERSON>',
            '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>',
            '<PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>',
            '<PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON> bin <PERSON>',
            '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>',
            '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
            '<PERSON><PERSON> bin <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> bin <PERSON>',
            
            // <PERSON><PERSON><PERSON><PERSON>
            '<PERSON><PERSON><PERSON><PERSON> bin<PERSON>', '<PERSON> <PERSON> bin<PERSON>', '<PERSON> bin<PERSON>',
            '<PERSON><PERSON> bin<PERSON><PERSON>', '<PERSON><PERSON> bin<PERSON>', '<PERSON> bin<PERSON>',
            '<PERSON> bin<PERSON>', '<PERSON><PERSON> bin<PERSON>', '<PERSON><PERSON><PERSON> bin<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON> bin<PERSON>', '<PERSON> bin<PERSON>', '<PERSON><PERSON> bin<PERSON> <PERSON>',
            '<PERSON> <PERSON> bin<PERSON> <PERSON>', 'Khadijah binti Mahmud', 'Ramlah binti Sulaiman',
            'Salina binti Hamid', 'Norsham binti Zain', 'Fauziah binti Rosli',
            'Rohani binti Nordin', 'Zaiton binti Latif', 'Halimah binti Kadir',
            'Norazah binti Shaari', 'Rusnah binti Hashim', 'Sakinah binti Razali',
            'Noraini binti Nasir', 'Rashidah binti Aziz', 'Salmah binti Halim',
            'Norlela binti Wahab', 'Zaleha binti Yasin', 'Aminah binti Bakar'
        ];

        $positions = [
            'football' => ['Penjaga Gol', 'Bek Kanan', 'Bek Kiri', 'Bek Tengah', 'Pemain Tengah', 'Penyerang'],
            'netball' => ['Goal Shooter', 'Goal Attack', 'Wing Attack', 'Centre', 'Wing Defence', 'Goal Defence', 'Goal Keeper'],
            'relay' => ['Pelari 1', 'Pelari 2', 'Pelari 3', 'Pelari 4', 'Pelari 5', 'Pelari 6', 'Pelari 7', 'Pelari 8', 'Pelari 9', 'Pelari 10'],
            'badminton' => ['Tunggal Lelaki', 'Tunggal Wanita', 'Beregu Lelaki', 'Beregu Wanita', 'Beregu Campuran'],
            'table-tennis' => ['Tunggal Lelaki', 'Tunggal Wanita', 'Beregu Lelaki', 'Beregu Wanita'],
            'volleyball' => ['Setter', 'Outside Hitter', 'Middle Blocker', 'Opposite Hitter', 'Libero', 'Defensive Specialist'],
            'traditional' => ['Tarik Tali', 'Lompat Getah', 'Galah Panjang', 'Baling Selipar', 'Lari Dalam Guni']
        ];

        foreach ($zones as $zone) {
            foreach ($sports as $sport) {
                $sportPositions = $positions[$sport->slug] ?? ['Pemain'];
                $playersPerSport = count($sportPositions);
                
                // Add some extra players as reserves
                $totalPlayers = $playersPerSport + 3;
                
                for ($i = 0; $i < $totalPlayers; $i++) {
                    $playerName = $playerNames[array_rand($playerNames)];
                    $position = $i < count($sportPositions) ? $sportPositions[$i] : 'Simpanan';
                    
                    // Generate unique service number
                    $serviceNumber = sprintf('KKD%04d%02d',
                        rand(1000, 9999), // Service number
                        rand(10, 99)      // Suffix
                    );

                    $ranks = ['Leftenan', 'Kapten', 'Major', 'Leftenan Kolonel', 'Kolonel', 'Sarjan', 'Koperal', 'Lans Koperal'];
                    $units = ['Unit Perubatan', 'Unit Pergigian', 'Unit Farmasi', 'Unit Kejururawatan', 'Unit Pentadbiran'];

                    $player = Player::create([
                        'name' => $playerName,
                        'service_number' => $serviceNumber,
                        'zone' => $zone->code,
                        'rank' => $ranks[array_rand($ranks)],
                        'unit' => $units[array_rand($units)],
                        'phone' => '01' . rand(1, 9) . '-' . rand(1000000, 9999999),
                        'status' => 'active',
                        'is_active' => true,
                        'emergency_contact' => 'Waris Terdekat',
                        'emergency_phone' => '01' . rand(1, 9) . '-' . rand(1000000, 9999999),
                        'medical_conditions' => rand(0, 10) > 8 ? 'Asma' : null, // 20% chance of medical condition
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Attach the sport to the player
                    $player->sports()->attach($sport->id);
                }
            }
        }
    }
}
