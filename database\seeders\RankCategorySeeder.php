<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RankCategory;

class RankCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Pegawai Kanan',
                'code' => 'pegawai_kanan',
                'description' => 'Kategori untuk pegawai kanan',
                'display_order' => 1,
            ],
            [
                'name' => 'Pegawai Berpangkat (Mej/Kapt/Lt/LtM)',
                'code' => 'pegawai_berpangkat',
                'description' => 'Kategori untuk pegawai berpangkat Major, Kapten, Leftenan, Leftenan Muda',
                'display_order' => 2,
            ],
            [
                'name' => 'PTT Kanan (PW/Ssjn/Sjn)',
                'code' => 'ptt_kanan',
                'description' => 'Kategori untuk PTT Kanan - Pegawai Waran, <PERSON><PERSON><PERSON>, <PERSON>',
                'display_order' => 3,
            ],
            [
                'name' => 'PTT Rendah (Kpl/Lkpl/Pbt)',
                'code' => 'ptt_rendah',
                'description' => 'Kategori untuk PTT Rendah - Koperal, Lans Ko<PERSON>al, Prebet',
                'display_order' => 4,
            ],
            [
                'name' => 'Ahli BAKAT',
                'code' => 'ahli_bakat',
                'description' => 'Kategori untuk ahli BAKAT',
                'display_order' => 5,
            ],
            [
                'name' => 'Terbuka (Anggota Pegawai KKD/Lain2 perkhidmatan/awam yang berkhidmat di bawah PKAT)',
                'code' => 'terbuka',
                'description' => 'Kategori terbuka untuk anggota pegawai KKD dan lain-lain perkhidmatan/awam yang berkhidmat di bawah PKAT',
                'display_order' => 6,
            ],
        ];

        foreach ($categories as $categoryData) {
            RankCategory::updateOrCreate(
                ['code' => $categoryData['code']], // Find by code
                $categoryData // Update or create with this data
            );
        }
    }
}
