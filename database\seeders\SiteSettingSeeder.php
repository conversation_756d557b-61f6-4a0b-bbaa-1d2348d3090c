<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Seeder;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Seeder yang sesuai dengan form Tetapan Sistem yang ada
     */
    public function run(): void
    {
        $settings = [
            // General Settings - sesuai dengan GeneralSettingsTab
            [
                'key' => 'site_name',
                'value' => 'Portal Sukan Intra KKD',
                'type' => 'text',
                'description' => 'Nama rasmi portal sukan',
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Sukan Intra Kor Kesihatan DiRaja 2025',
                'type' => 'text',
                'description' => 'Tagline portal',
            ],
            [
                'key' => 'site_description',
                'value' => '',
                'type' => 'text',
                'description' => 'Deskripsi portal (optional)',
            ],
            [
                'key' => 'header_title_part1',
                'value' => 'SUKAN',
                'type' => 'text',
                'description' => 'Bahagian pertama tajuk header',
            ],
            [
                'key' => 'header_title_part2',
                'value' => 'INTRA',
                'type' => 'text',
                'description' => 'Bahagian kedua tajuk header',
            ],
            [
                'key' => 'header_subtitle',
                'value' => 'KOR KESIHATAN DIRAJA 2025',
                'type' => 'text',
                'description' => 'Subtajuk header',
            ],
            [
                'key' => 'copyright_text',
                'value' => 'Semua hak terpelihara',
                'type' => 'text',
                'description' => 'Teks copyright di footer',
            ],
            [
                'key' => 'logo_url',
                'value' => '',
                'type' => 'image',
                'description' => 'Logo rasmi portal',
            ],

            // Notification Settings - sesuai dengan NotificationSettingsTab
            [
                'key' => 'email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Aktifkan notifikasi email',
            ],
            [
                'key' => 'sms_notifications',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Aktifkan notifikasi SMS',
            ],
            [
                'key' => 'push_notifications',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Aktifkan push notifications',
            ],

            // Social Media Settings
            [
                'key' => 'facebook_url',
                'value' => '',
                'type' => 'text',
                'description' => 'URL halaman Facebook rasmi',
            ],
            [
                'key' => 'instagram_url',
                'value' => '',
                'type' => 'text',
                'description' => 'URL halaman Instagram rasmi',
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'type' => 'text',
                'description' => 'URL halaman Twitter rasmi',
            ],
            [
                'key' => 'youtube_url',
                'value' => '',
                'type' => 'text',
                'description' => 'URL channel YouTube rasmi',
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']], // Find by key
                [
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                    'description' => $setting['description'],
                    'is_active' => true,
                    'updated_by' => 5, // superadmin user ID
                ]
            );
        }
    }
}
