<?php

namespace Database\Seeders;

use App\Models\Sport;
use Illuminate\Database\Seeder;

class SportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sports = [
            [
                'name' => '<PERSON>ri-<PERSON><PERSON>-g<PERSON>',
                'slug' => 'relay',
                'description' => 'Pertandingan lari berganti-ganti 10 x 400 meter antara zon. Setiap zon akan menghantar 10 pelari untuk berlari secara berganti-ganti.',
                'logo_url' => '/images/relaypsd.png',
                'participants' => '10 pelari per zon',
                'venue' => 'Stadium PU Sendayan',
                'rules' => 'Lari berganti-ganti 10 x 400 meter dengan peraturan IAAF standard.',
                'display_order' => 1,
            ],
            [
                'name' => 'Bola Sepak',
                'slug' => 'football',
                'description' => 'Pertandingan bola sepak antara zon dengan format 11 vs 11 pemain. Setiap zon akan bertanding dalam format liga untuk menentukan juara keseluruhan.',
                'logo_url' => '/images/bolasepakpsd.png',
                'participants' => '22 pemain per perlawanan',
                'venue' => 'Padang Bola Sepak Utama',
                'rules' => 'Menggunakan peraturan FIFA standard dengan masa permainan 2 x 45 minit.',
                'display_order' => 2,
            ],
            [
                'name' => 'Bola Jaring',
                'slug' => 'netball',
                'description' => 'Sukan bola jaring yang dimainkan oleh dua pasukan dengan 7 pemain setiap satu. Pertandingan akan menggunakan peraturan standard netball antarabangsa.',
                'logo_url' => '/images/netballpsd.png',
                'participants' => '7 pemain per pasukan',
                'venue' => 'Gelanggang Bola Jaring',
                'rules' => 'Menggunakan peraturan INF (International Netball Federation) dengan masa permainan 4 x 15 minit.',
                'display_order' => 3,
            ],

            [
                'name' => 'Badminton',
                'slug' => 'badminton',
                'description' => 'Pertandingan badminton dalam kategori perseorangan lelaki, perseorangan wanita, dan beregu campuran. Format pertandingan menggunakan sistem kalah mati.',
                'logo_url' => '/images/badmintonpsd.png',
                'participants' => '1-2 pemain per perlawanan',
                'venue' => 'Dewan Badminton Indoor',
                'rules' => 'Menggunakan peraturan BWF dengan format best of 3 games.',
                'display_order' => 4,
            ],
            [
                'name' => 'Ping Pong',
                'slug' => 'table-tennis',
                'description' => 'Pertandingan ping pong dalam kategori perseorangan dan beregu. Setiap zon akan menghantar wakil terbaik untuk bertanding.',
                'logo_url' => '/images/pingpongpsd.png',
                'participants' => '1-2 pemain per perlawanan',
                'venue' => 'Dewan Ping Pong',
                'rules' => 'Menggunakan peraturan ITTF dengan format best of 5 games.',
                'display_order' => 5,
            ],
            [
                'name' => 'Bola Tampar',
                'slug' => 'volleyball',
                'description' => 'Pertandingan bola tampar indoor dengan 6 pemain setiap pasukan. Format pertandingan menggunakan sistem liga.',
                'logo_url' => '/images/bolatamparpsd.png',
                'participants' => '6 pemain per pasukan',
                'venue' => 'Dewan Bola Tampar',
                'rules' => 'Menggunakan peraturan FIVB dengan format best of 5 sets.',
                'display_order' => 6,
            ],

        ];

        foreach ($sports as $sportData) {
            Sport::updateOrCreate(
                ['slug' => $sportData['slug']], // Find by slug
                $sportData // Update or create with this data
            );
        }
    }
}
