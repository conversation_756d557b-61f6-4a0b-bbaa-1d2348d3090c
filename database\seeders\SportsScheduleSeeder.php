<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SportsSchedule;
use App\Models\Sport;
use Carbon\Carbon;

class SportsScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get sports by name for reference
        $sports = Sport::whereIn('name', [
            '<PERSON>ri-<PERSON><PERSON>-g<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            'Bola Tampar',
            'Badminton',
            'Ping Pong'
        ])->get()->keyBy('name');

        $schedules = [
            [
                'sport_name' => '<PERSON><PERSON>-<PERSON><PERSON>-g<PERSON>',
                'schedule_name' => 'Acara: <PERSON><PERSON>-<PERSON><PERSON>-<PERSON> 10 x 400 meter',
                'description' => 'Pertandingan lari berganti-ganti 10 x 400 meter antara zon',
                'start_date' => '2025-07-21',
                'end_date' => '2025-07-21',
                'main_venue' => 'Stadium PU Sendayan',
                'display_order' => 1,
            ],
            [
                'sport_name' => 'Bola <PERSON>ak',
                'schedule_name' => 'Acara: <PERSON><PERSON>',
                'description' => 'Pertandingan bola sepak antara zon',
                'start_date' => '2025-07-22',
                'end_date' => '2025-07-24',
                'main_venue' => 'Stadium PU Sendayan',
                'display_order' => 2,
            ],
            [
                'sport_name' => 'Bola Jaring',
                'schedule_name' => 'Acara: Bola Jaring (Perempuan)',
                'description' => 'Pertandingan bola jaring untuk kategori perempuan',
                'start_date' => '2025-07-22',
                'end_date' => '2025-07-22',
                'main_venue' => 'Kompleks Sukan PU Sendayan',
                'display_order' => 3,
            ],
            [
                'sport_name' => 'Bola Tampar',
                'schedule_name' => 'Acara: Bola Tampar (Lelaki & Perempuan)',
                'description' => 'Pertandingan bola tampar untuk kategori lelaki dan perempuan',
                'start_date' => '2025-07-22',
                'end_date' => '2025-07-23',
                'main_venue' => 'Kompleks Sukan PU Sendayan',
                'display_order' => 4,
            ],
            [
                'sport_name' => 'Badminton',
                'schedule_name' => 'Acara: Badminton Zon (Mixed Categories)',
                'description' => 'Pertandingan badminton dengan pelbagai kategori (Single Men, Double Women, Double Men A, Double Men B, Double Mix)',
                'start_date' => '2025-07-23',
                'end_date' => '2025-07-23',
                'main_venue' => 'Dewan Angkasaraya',
                'display_order' => 5,
            ],
            [
                'sport_name' => 'Badminton',
                'schedule_name' => 'Acara: Badminton Serikandi (Single Women, Double Women)',
                'description' => 'Pertandingan badminton khusus untuk wanita dengan sistem tournament bracket',
                'start_date' => '2025-07-23',
                'end_date' => '2025-07-23',
                'main_venue' => 'Dewan Angkasaraya',
                'display_order' => 6,
            ],
            [
                'sport_name' => 'Ping Pong',
                'schedule_name' => 'Acara: Ping Pong (Mixed Categories)',
                'description' => 'Pertandingan ping pong dengan pelbagai kategori (Single Men, Double Men A, Double Men B, Double Women, Double Mix)',
                'start_date' => '2025-07-22',
                'end_date' => '2025-07-22',
                'main_venue' => 'Dewan Angkasaraya',
                'display_order' => 7,
            ],
        ];

        foreach ($schedules as $scheduleData) {
            $sport = $sports->get($scheduleData['sport_name']);

            if ($sport) {
                SportsSchedule::create([
                    'sport_id' => $sport->id,
                    'schedule_name' => $scheduleData['schedule_name'],
                    'description' => $scheduleData['description'],
                    'start_date' => Carbon::parse($scheduleData['start_date']),
                    'end_date' => Carbon::parse($scheduleData['end_date']),
                    'main_venue' => $scheduleData['main_venue'],
                    'display_order' => $scheduleData['display_order'],
                    'is_active' => true,
                    'created_by' => 1, // Assuming admin user ID is 1
                ]);
            }
        }
    }
}
