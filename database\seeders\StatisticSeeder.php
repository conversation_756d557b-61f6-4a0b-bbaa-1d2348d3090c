<?php

namespace Database\Seeders;

use App\Models\Statistic;
use Illuminate\Database\Seeder;

class StatisticSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statistics = [
            [
                'key' => 'total_participants',
                'value' => '150+',
                'label' => 'Jumlah P<PERSON>rta',
                'icon' => 'users',
                'display_order' => 1,
            ],
            [
                'key' => 'total_sports',
                'value' => '7',
                'label' => 'Kategori Sukan',
                'icon' => 'trophy',
                'display_order' => 2,
            ],
            [
                'key' => 'total_zones',
                'value' => '3',
                'label' => 'Zon <PERSON>anding',
                'icon' => 'map-pin',
                'display_order' => 3,
            ],
            [
                'key' => 'total_matches',
                'value' => '42',
                'label' => 'Perlawanan <PERSON>',
                'icon' => 'calendar',
                'display_order' => 4,
            ],
        ];

        foreach ($statistics as $statistic) {
            Statistic::updateOrCreate(
                ['key' => $statistic['key']], // Find by key
                $statistic // Update or create with this data
            );
        }
    }
}
