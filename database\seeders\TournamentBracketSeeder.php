<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sport;
use App\Models\SportsSchedule;
use App\Models\SportMatch;

class TournamentBracketSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedBolaSepakBracket();
        $this->seedBolaJaringBracket();
        $this->seedBolaTamparBracket();
        $this->seedBadmintonSerikandiBracket();
    }

    /**
     * Seed Bola Sepak Tournament Bracket
     * Round Robin format: Zon 1, Zon 2, Zon 3
     * Round Robin: Zon 3 vs Zon 1, Zon 1 vs Zon 2, Zon 3 vs Zon 2
     * Final: Top 2 teams from round robin
     */
    private function seedBolaSepakBracket()
    {
        $sport = Sport::where('name', 'Bola Sepak')->first();
        $schedule = SportsSchedule::where('schedule_name', 'Acara: Bola Sepak')->first();

        if (!$sport || !$schedule) return;

        // Clear existing bracket matches for this sport
        SportMatch::where('sport_id', $sport->id)
            ->whereNotNull('bracket_round')
            ->delete();

        $matches = [
            // Round Robin Match 1: Zon 3 vs Zon 1 (22 Jul)
            [
                'bil' => 1,
                'title' => 'Bola Sepak - Zon 3 vs Zon 1',
                'match_type' => 'Round Robin',
                'zone_a' => '3',
                'zone_b' => '1',
                'match_date' => '2025-07-22',
                'match_time' => '08:00',
                'end_time' => '09:30',
                'bracket_round' => 'Round Robin',
                'bracket_position' => 1,
                'status' => 'scheduled',
            ],
            // Round Robin Match 2: Zon 1 vs Zon 2 (22 Jul)
            [
                'bil' => 2,
                'title' => 'Bola Sepak - Zon 1 vs Zon 2',
                'match_type' => 'Round Robin',
                'zone_a' => '1',
                'zone_b' => '2',
                'match_date' => '2025-07-22',
                'match_time' => '20:00',
                'end_time' => '21:30',
                'bracket_round' => 'Round Robin',
                'bracket_position' => 2,
                'status' => 'scheduled',
            ],
            // Round Robin Match 3: Zon 3 vs Zon 2 (23 Jul)
            [
                'bil' => 3,
                'title' => 'Bola Sepak - Zon 3 vs Zon 2',
                'match_type' => 'Round Robin',
                'zone_a' => '3',
                'zone_b' => '2',
                'match_date' => '2025-07-23',
                'match_time' => '08:00',
                'end_time' => '09:30',
                'bracket_round' => 'Round Robin',
                'bracket_position' => 3,
                'status' => 'scheduled',
            ],
            // Final: Top 2 teams from round robin (24 Jul)
            [
                'bil' => 4,
                'title' => 'Final Bola Sepak',
                'match_type' => 'Final',
                'zone_a' => 'Zon X', // Top team from round robin
                'zone_b' => 'Zon X', // Second team from round robin
                'match_date' => '2025-07-24',
                'match_time' => '08:00',
                'end_time' => '10:00',
                'bracket_round' => 'Final',
                'bracket_position' => 1,
                'is_highlighted' => true,
                'highlight_color' => 'kuning',
                'status' => 'scheduled',
            ],
        ];

        foreach ($matches as $matchData) {
            SportMatch::create(array_merge($matchData, [
                'sport_id' => $sport->id,
                'sports_schedule_id' => $schedule->id,
                'event_type' => 'round_robin_final',
                'venue' => 'Stadium PU Sendayan',
                'gender_type' => 'Lelaki',
                'created_by' => 1,
            ]));
        }
    }

    /**
     * Seed Bola Jaring Tournament Bracket
     * 4 teams: Zon 1, Zon 2, Zon 3, BAKAT
     */
    private function seedBolaJaringBracket()
    {
        $sport = Sport::where('name', 'Bola Jaring')->first();
        $schedule = SportsSchedule::where('schedule_name', 'Acara: Bola Jaring (Perempuan)')->first();

        if (!$sport || !$schedule) return;

        // Clear existing bracket matches for this sport
        SportMatch::where('sport_id', $sport->id)
            ->whereNotNull('bracket_round')
            ->delete();

        $matches = [
            // Semi-Final 1: Zon 1 vs Zon 2
            [
                'bil' => 1,
                'title' => 'Semi-Final Bola Jaring 1',
                'match_type' => 'Semi-Final',
                'zone_a' => '1',
                'zone_b' => '2',
                'match_date' => '2025-07-22',
                'match_time' => '14:00',
                'end_time' => '14:45',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 1,
                'status' => 'scheduled',
            ],
            // Semi-Final 2: Zon 3 vs BAKAT
            [
                'bil' => 2,
                'title' => 'Semi-Final Bola Jaring 2',
                'match_type' => 'Semi-Final',
                'zone_a' => '3',
                'zone_b' => 'BAKAT',
                'match_date' => '2025-07-22',
                'match_time' => '15:00',
                'end_time' => '15:45',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 2,
                'status' => 'scheduled',
            ],
            // Final: Winner Semi 1 vs Winner Semi 2
            [
                'bil' => 3,
                'title' => 'Final Bola Jaring',
                'match_type' => 'Final',
                'zone_a' => 'W1', // Winner of Semi-Final 1
                'zone_b' => 'W2', // Winner of Semi-Final 2
                'match_date' => '2025-07-22',
                'match_time' => '16:15',
                'end_time' => '17:00',
                'bracket_round' => 'Final',
                'bracket_position' => 1,
                'is_highlighted' => true,
                'highlight_color' => 'kuning',
                'status' => 'scheduled',
            ],
        ];

        foreach ($matches as $matchData) {
            SportMatch::create(array_merge($matchData, [
                'sport_id' => $sport->id,
                'sports_schedule_id' => $schedule->id,
                'event_type' => 'round_robin_final',
                'venue' => 'Kompleks Sukan PU Sendayan',
                'gender_type' => 'Perempuan',
                'created_by' => 1,
            ]));
        }
    }

    /**
     * Seed Bola Tampar Tournament Bracket
     * Separate brackets for Lelaki and Perempuan
     */
    private function seedBolaTamparBracket()
    {
        $sport = Sport::where('name', 'Bola Tampar')->first();
        $schedule = SportsSchedule::where('schedule_name', 'Acara: Bola Tampar (Lelaki & Perempuan)')->first();

        if (!$sport || !$schedule) return;

        // Clear existing bracket matches for this sport
        SportMatch::where('sport_id', $sport->id)
            ->whereNotNull('bracket_round')
            ->delete();

        // Lelaki Bracket
        $lelakiMatches = [
            // Semi-Final 1: Zon 1 vs Zon 2
            [
                'bil' => 1,
                'title' => 'Semi-Final Bola Tampar (L) 1',
                'match_type' => 'Semi-Final',
                'zone_a' => '1',
                'zone_b' => '2',
                'match_date' => '2025-07-24',
                'match_time' => '09:00',
                'end_time' => '10:00',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 1,
                'gender_type' => 'Lelaki',
                'status' => 'scheduled',
            ],
            // Semi-Final 2: Zon 3 vs BAKAT
            [
                'bil' => 2,
                'title' => 'Semi-Final Bola Tampar (L) 2',
                'match_type' => 'Semi-Final',
                'zone_a' => '3',
                'zone_b' => 'BAKAT',
                'match_date' => '2025-07-24',
                'match_time' => '10:15',
                'end_time' => '11:15',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 2,
                'gender_type' => 'Lelaki',
                'status' => 'scheduled',
            ],
            // Final Lelaki
            [
                'bil' => 3,
                'title' => 'Final Bola Tampar (L)',
                'match_type' => 'Final',
                'zone_a' => 'W1',
                'zone_b' => 'W2',
                'match_date' => '2025-07-24',
                'match_time' => '11:45',
                'end_time' => '12:45',
                'bracket_round' => 'Final',
                'bracket_position' => 1,
                'gender_type' => 'Lelaki',
                'is_highlighted' => true,
                'highlight_color' => 'kuning',
                'status' => 'scheduled',
            ],
        ];

        // Perempuan Bracket
        $perempuanMatches = [
            // Semi-Final 1: Zon 1 vs Zon 2
            [
                'bil' => 4,
                'title' => 'Semi-Final Bola Tampar (P) 1',
                'match_type' => 'Semi-Final',
                'zone_a' => '1',
                'zone_b' => '2',
                'match_date' => '2025-07-24',
                'match_time' => '15:00',
                'end_time' => '16:00',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 3,
                'gender_type' => 'Perempuan',
                'status' => 'scheduled',
            ],
            // Semi-Final 2: Zon 3 vs BAKAT
            [
                'bil' => 5,
                'title' => 'Semi-Final Bola Tampar (P) 2',
                'match_type' => 'Semi-Final',
                'zone_a' => '3',
                'zone_b' => 'BAKAT',
                'match_date' => '2025-07-24',
                'match_time' => '16:15',
                'end_time' => '17:15',
                'bracket_round' => 'Semi-Final',
                'bracket_position' => 4,
                'gender_type' => 'Perempuan',
                'status' => 'scheduled',
            ],
            // Final Perempuan
            [
                'bil' => 6,
                'title' => 'Final Bola Tampar (P)',
                'match_type' => 'Final',
                'zone_a' => 'W3',
                'zone_b' => 'W4',
                'match_date' => '2025-07-24',
                'match_time' => '17:45',
                'end_time' => '18:45',
                'bracket_round' => 'Final',
                'bracket_position' => 2,
                'gender_type' => 'Perempuan',
                'is_highlighted' => true,
                'highlight_color' => 'kuning',
                'status' => 'scheduled',
            ],
        ];

        $allMatches = array_merge($lelakiMatches, $perempuanMatches);

        foreach ($allMatches as $matchData) {
            SportMatch::create(array_merge($matchData, [
                'sport_id' => $sport->id,
                'sports_schedule_id' => $schedule->id,
                'event_type' => 'round_robin_final',
                'venue' => 'Dewan Serbaguna',
                'created_by' => 1,
            ]));
        }
    }

    /**
     * Keep existing Badminton Serikandi bracket structure
     * This is already properly seeded in BadmintonPingPongSeederMethods
     */
    private function seedBadmintonSerikandiBracket()
    {
        // Badminton Serikandi already has proper bracket structure
        // No changes needed as it's already seeded properly
    }
}
