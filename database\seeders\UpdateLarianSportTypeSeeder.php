<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sport;

class UpdateLarianSportTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update Larian sport to be rank-based
        Sport::where('name', 'LIKE', '%larian%')
            ->orWhere('name', 'LIKE', '%Larian%')
            ->orWhere('name', 'LIKE', '%LARIAN%')
            ->orWhere('slug', 'LIKE', '%larian%')
            ->update(['sport_type' => 'rank_based']);

        echo "Updated Larian sport type to rank_based\n";
    }
}
