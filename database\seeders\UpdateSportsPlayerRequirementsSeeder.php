<?php

namespace Database\Seeders;

use App\Models\Sport;
use Illuminate\Database\Seeder;

class UpdateSportsPlayerRequirementsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sportsRequirements = [
            '<PERSON><PERSON>ak' => [
                'required_players' => 11,
                'max_substitute_players' => 8,
            ],
            'Badminton' => [
                'required_players' => 6, // Singles + Doubles (Men/Women/Mixed)
                'max_substitute_players' => 4,
            ],
            'Ping Pong' => [
                'required_players' => 4, // Singles + Doubles (Men/Women)
                'max_substitute_players' => 2,
            ],
            'Netball' => [
                'required_players' => 7,
                'max_substitute_players' => 5,
            ],
            '<PERSON><PERSON>' => [
                'required_players' => 6,
                'max_substitute_players' => 6,
            ],
            '<PERSON><PERSON>' => [
                'required_players' => 5,
                'max_substitute_players' => 7,
            ],
            'Ragbi' => [
                'required_players' => 15,
                'max_substitute_players' => 8,
            ],
            'Hoki' => [
                'required_players' => 11,
                'max_substitute_players' => 6,
            ],
            'Takraw' => [
                'required_players' => 3,
                'max_substitute_players' => 3,
            ],
            'Tenis' => [
                'required_players' => 4, // Singles + Doubles (Men/Women)
                'max_substitute_players' => 2,
            ],
        ];

        foreach ($sportsRequirements as $sportName => $requirements) {
            Sport::where('name', $sportName)->update([
                'required_players' => $requirements['required_players'],
                'max_substitute_players' => $requirements['max_substitute_players'],
            ]);
        }

        // Update any sports that don't have specific requirements with defaults
        Sport::whereNull('required_players')->update([
            'required_players' => 11,
            'max_substitute_players' => 8,
        ]);
    }
}
