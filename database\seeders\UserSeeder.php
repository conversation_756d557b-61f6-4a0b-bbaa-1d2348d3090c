<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            // Zone Users
            [
                'username' => 'zon-1',
                'email' => '<EMAIL>',
                'name' => 'Zon 1 Administrator',
                'role' => 'zone',
                'zone' => '1',
            ],
            [
                'username' => 'zon-2',
                'email' => '<EMAIL>',
                'name' => 'Zon 2 Administrator',
                'role' => 'zone',
                'zone' => '2',
            ],
            [
                'username' => 'zon-3',
                'email' => '<EMAIL>',
                'name' => 'Zon 3 Administrator',
                'role' => 'zone',
                'zone' => '3',
            ],
            [
                'username' => 'bakat-kkd',
                'email' => '<EMAIL>',
                'name' => 'Bakat KKD Administrator',
                'role' => 'zone',
                'zone' => 'BAKAT-KKD',
            ],

            // Super Admin
            [
                'username' => 'superadmin',
                'email' => '<EMAIL>',
                'name' => 'Super Administrator',
                'role' => 'superadmin',
                'zone' => null,
            ],

             [
                'username' => 'fahmi',
                'email' => '<EMAIL>',
                'name' => 'Super Administrator',
                'role' => 'superadmin',
                'zone' => null,
            ],

            // Admins
            [
                'username' => 'admin1',
                'email' => '<EMAIL>',
                'name' => 'Administrator 1',
                'role' => 'admin',
                'zone' => null,
            ],
            [
                'username' => 'admin2',
                'email' => '<EMAIL>',
                'name' => 'Administrator 2',
                'role' => 'admin',
                'zone' => null,
            ],
            [
                'username' => 'admin3',
                'email' => '<EMAIL>',
                'name' => 'Administrator 3',
                'role' => 'admin',
                'zone' => null,
            ],
            [
                'username' => 'admin4',
                'email' => '<EMAIL>',
                'name' => 'Administrator 4',
                'role' => 'admin',
                'zone' => null,
            ],
            [
                'username' => 'admin5',
                'email' => '<EMAIL>',
                'name' => 'Administrator 5',
                'role' => 'admin',
                'zone' => null,
            ],

            // Media
            [
                'username' => 'media1',
                'email' => '<EMAIL>',
                'name' => 'Media Officer 1',
                'role' => 'media',
                'zone' => null,
            ],
            [
                'username' => 'media2',
                'email' => '<EMAIL>',
                'name' => 'Media Officer 2',
                'role' => 'media',
                'zone' => null,
            ],
            [
                'username' => 'media3',
                'email' => '<EMAIL>',
                'name' => 'Media Officer 3',
                'role' => 'media',
                'zone' => null,
            ],
            [
                'username' => 'media4',
                'email' => '<EMAIL>',
                'name' => 'Media Officer 4',
                'role' => 'media',
                'zone' => null,
            ],
        ];

        foreach ($users as $userData) {
            User::updateOrCreate(
                ['username' => $userData['username']], // Find by username
                [
                    'email' => $userData['email'],
                    'name' => $userData['name'],
                    'password' => Hash::make('Abcd@1234'),
                    'role' => $userData['role'],
                    'zone' => $userData['zone'],
                    'is_active' => true,
                    'must_change_password' => true,
                ]
            );
        }
    }
}
