<?php

namespace Database\Seeders;

use App\Models\Zone;
use Illuminate\Database\Seeder;

class ZoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $zones = [
            [
                'code' => '1',
                'name' => 'Zon 1',
                'description' => 'Zon 1 - <PERSON><PERSON><PERSON>',
                'logo_url' => '/images/zones/zona-logo.png',
                'color_primary' => '#DC2626', // Red
                'color_secondary' => '#B91C1C',
                'display_order' => 1,
            ],
            [
                'code' => '2',
                'name' => 'Zon 2',
                'description' => 'Zon 2 - <PERSON><PERSON><PERSON>',
                'logo_url' => '/images/zones/zone-b-logo.png',
                'color_primary' => '#2563EB', // Blue
                'color_secondary' => '#1D4ED8',
                'display_order' => 2,
            ],
            [
                'code' => '3',
                'name' => 'Zon 3',
                'description' => 'Zon 3 - <PERSON><PERSON><PERSON>',
                'logo_url' => '/images/zones/zonec-logo.png',
                'color_primary' => '#16A34A', // Green
                'color_secondary' => '#15803D',
                'display_order' => 3,
            ],
            [
                'code' => 'BAKAT-KKD',
                'name' => 'Bakat KKD',
                'description' => 'Pasukan Bakat Khas Kor Kesihatan DiRaja',
                'logo_url' => '/images/zones/bakat-kkd-logo.png',
                'color_primary' => '#7C3AED', // Purple
                'color_secondary' => '#6D28D9',
                'display_order' => 4,
            ],
        ];

        foreach ($zones as $zoneData) {
            Zone::updateOrCreate(
                ['code' => $zoneData['code']], // Find by code
                $zoneData // Update or create with this data
            );
        }
    }
}
