<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .url-input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CORS Debug Tool untuk Portal Sukan</h1>
        
        <div class="info">
            <h3>📋 Langkah-langkah debugging:</h3>
            <ol>
                <li>Pastikan server Laravel berjalan di <code>http://127.0.0.1:8000</code></li>
                <li>Buka halaman ini di browser yang sama</li>
                <li>Klik tombol test di bawah untuk memeriksa koneksi</li>
                <li>Periksa console browser untuk error details</li>
            </ol>
        </div>

        <div>
            <h3>🌐 Base URL Configuration</h3>
            <input type="text" id="baseUrl" class="url-input" value="http://127.0.0.1:8000" placeholder="Base URL">
            <button onclick="updateBaseUrl()">Update Base URL</button>
        </div>

        <div>
            <h3>🧪 API Tests</h3>
            <button onclick="testAPI('/api/public/settings')">Test Settings API</button>
            <button onclick="testAPI('/api/public/homepage-content')">Test Homepage Content</button>
            <button onclick="testAPI('/api/user')">Test User API (Auth)</button>
            <button onclick="testAPI('/api/public/jadual/sports-schedules')">Test Jadual API</button>
            <button onclick="clearAllCache()">🗑️ Clear All Cache</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results"></div>

        <div>
            <h3>🔍 Browser Information</h3>
            <div id="browserInfo"></div>
        </div>

        <div>
            <h3>📝 Manual Test URLs</h3>
            <p>Copy dan paste URLs ini di tab baru untuk test manual:</p>
            <pre id="testUrls"></pre>
        </div>
    </div>

    <script>
        let baseUrl = 'http://127.0.0.1:8000';
        
        function updateBaseUrl() {
            baseUrl = document.getElementById('baseUrl').value;
            updateTestUrls();
            addResult('info', `Base URL updated to: ${baseUrl}`);
        }

        function updateTestUrls() {
            const urls = [
                `${baseUrl}/api/public/settings`,
                `${baseUrl}/api/public/homepage-content`,
                `${baseUrl}/api/user`,
                `${baseUrl}/api/public/jadual/sports-schedules`
            ];
            document.getElementById('testUrls').textContent = urls.join('\n');
        }

        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testAPI(endpoint) {
            const url = `${baseUrl}${endpoint}`;
            addResult('info', `Testing: ${url}`);
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('success', `✅ ${endpoint} - Status: ${response.status} - Data received`);
                    console.log(`${endpoint} response:`, data);
                } else {
                    addResult('error', `❌ ${endpoint} - Status: ${response.status} - ${response.statusText}`);
                }
            } catch (error) {
                addResult('error', `❌ ${endpoint} - Error: ${error.message}`);
                console.error(`${endpoint} error:`, error);
                
                // Check if it's a CORS error
                if (error.message.includes('CORS') || error.message.includes('fetch')) {
                    addResult('error', `🚫 CORS Error detected! Check browser console for details.`);
                }
            }
        }

        function displayBrowserInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Current URL': window.location.href,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Cookies Enabled': navigator.cookieEnabled,
                'Language': navigator.language
            };

            let html = '<pre>';
            for (const [key, value] of Object.entries(info)) {
                html += `${key}: ${value}\n`;
            }
            html += '</pre>';
            
            document.getElementById('browserInfo').innerHTML = html;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTestUrls();
            displayBrowserInfo();
            addResult('info', '🚀 CORS Debug Tool loaded. Ready for testing!');
        });

        // Auto-test on load
        setTimeout(() => {
            addResult('info', '🔄 Running automatic test...');
            testAPI('/api/public/settings');
        }, 1000);

        // Monitor for production URLs
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('mediakkd.com')) {
                addResult('error', `🚨 PRODUCTION URL DETECTED: ${url}`);
                console.log('🚨 PRODUCTION URL DETECTED IN FETCH:', url);
                console.trace('Call stack:');
            }
            return originalFetch.apply(this, args);
        };

        // Monitor XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (typeof url === 'string' && url.includes('mediakkd.com')) {
                addResult('error', `🚨 PRODUCTION URL DETECTED: ${url}`);
                console.log('🚨 PRODUCTION URL DETECTED IN XHR:', url);
                console.trace('Call stack:');
            }
            return originalOpen.call(this, method, url, ...args);
        };

        // Clear all cache function
        function clearAllCache() {
            addResult('info', '🗑️ Clearing all cache...');

            // Clear localStorage
            localStorage.clear();
            addResult('success', '✅ localStorage cleared');

            // Clear sessionStorage
            sessionStorage.clear();
            addResult('success', '✅ sessionStorage cleared');

            // Clear IndexedDB
            if ('indexedDB' in window) {
                indexedDB.databases().then(databases => {
                    databases.forEach(db => {
                        if (db.name) {
                            indexedDB.deleteDatabase(db.name);
                            addResult('success', `✅ IndexedDB "${db.name}" cleared`);
                        }
                    });
                }).catch(err => {
                    addResult('error', `❌ Error clearing IndexedDB: ${err.message}`);
                });
            }

            // Clear service worker cache
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    return Promise.all(
                        cacheNames.map(cacheName => {
                            caches.delete(cacheName);
                            addResult('success', `✅ Cache "${cacheName}" cleared`);
                        })
                    );
                }).catch(err => {
                    addResult('error', `❌ Error clearing caches: ${err.message}`);
                });
            }

            // Clear cookies for current domain
            document.cookie.split(";").forEach(function(c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
            });
            addResult('success', '✅ Cookies cleared');

            addResult('success', '🎉 All cache cleared! Please refresh the page.');
        }
    </script>
</body>
</html>
