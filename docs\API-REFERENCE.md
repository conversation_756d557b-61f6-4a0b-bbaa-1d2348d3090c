# API Reference - Portal Sukan Intra KKD

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [Player Management](#player-management)
3. [Zone Management](#zone-management)
4. [Match Management](#match-management)
5. [Content Management](#content-management)
6. [Settings Management](#settings-management)
7. [Public APIs](#public-apis)
8. [Error Handling](#error-handling)
9. [Rate Limiting](#rate-limiting)

---

## 🔐 Authentication

### Base URL
```
Production: https://yourdomain.com/api
Development: http://localhost:8000/api
```

### Authentication Headers
```http
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

### Login
```http
POST /api/login
Content-Type: application/json

{
    "username": "superadmin",
    "password": "Abcd@1234",
    "remember": false
}
```

**Response (200 OK):**
```json
{
    "token": "1|abc123def456...",
    "user": {
        "id": 1,
        "username": "superadmin",
        "name": "Super Admin",
        "email": "<EMAIL>",
        "role": "superadmin",
        "zone": null,
        "must_change_password": true,
        "last_login_at": "2025-07-14T10:30:00.000000Z"
    }
}
```

**Error Response (401 Unauthorized):**
```json
{
    "message": "Username atau kata laluan tidak sah"
}
```

### Logout
```http
POST /api/logout
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "message": "Berjaya log keluar"
}
```

### Get Current User
```http
GET /api/user
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "id": 1,
    "username": "superadmin",
    "name": "Super Admin",
    "email": "<EMAIL>",
    "role": "superadmin",
    "zone": null,
    "must_change_password": false,
    "last_login_at": "2025-07-14T10:30:00.000000Z"
}
```

### Change Password
```http
PUT /api/password/change
Authorization: Bearer {token}
Content-Type: application/json

{
    "current_password": "Abcd@1234",
    "new_password": "NewPassword123!",
    "new_password_confirmation": "NewPassword123!"
}
```

**Response (200 OK):**
```json
{
    "message": "Kata laluan berjaya dikemaskini"
}
```

**Error Response (422 Unprocessable Entity):**
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "current_password": ["Kata laluan semasa tidak betul"],
        "new_password": ["Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara"]
    }
}
```

---

## 👥 Player Management

### Get Players List
```http
GET /api/admin/players?page=1&search=ahmad&zone=Zon A&sport=1&status=active
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (optional): Page number for pagination
- `search` (optional): Search by player name
- `zone` (optional): Filter by zone
- `sport` (optional): Filter by sport ID
- `status` (optional): Filter by status (active/inactive)

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 1,
            "name": "Ahmad Ali bin Abu",
            "phone": "0123456789",
            "email": "<EMAIL>",
            "zone": "Zon A",
            "status": "active",
            "team_leader_name": "Captain Ahmad",
            "team_leader_phone": "0198765432",
            "sports": [
                {
                    "id": 1,
                    "name": "Badminton"
                },
                {
                    "id": 2,
                    "name": "Bola Sepak"
                }
            ],
            "registered_by": {
                "id": 1,
                "name": "Super Admin"
            },
            "created_at": "2025-07-14T10:30:00.000000Z"
        }
    ],
    "links": {
        "first": "http://localhost:8000/api/admin/players?page=1",
        "last": "http://localhost:8000/api/admin/players?page=5",
        "prev": null,
        "next": "http://localhost:8000/api/admin/players?page=2"
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "per_page": 10,
        "to": 10,
        "total": 50
    }
}
```

### Create Player
```http
POST /api/admin/players
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Ahmad Ali bin Abu",
    "phone": "0123456789",
    "email": "<EMAIL>",
    "zone": "Zon A",
    "sports": [1, 2],
    "status": "active",
    "team_leader_name": "Captain Ahmad",
    "team_leader_phone": "0198765432"
}
```

**Response (201 Created):**
```json
{
    "message": "Peserta berjaya didaftarkan",
    "player": {
        "id": 1,
        "name": "Ahmad Ali bin Abu",
        "phone": "0123456789",
        "email": "<EMAIL>",
        "zone": "Zon A",
        "status": "active",
        "team_leader_name": "Captain Ahmad",
        "team_leader_phone": "0198765432",
        "sports": [
            {
                "id": 1,
                "name": "Badminton"
            },
            {
                "id": 2,
                "name": "Bola Sepak"
            }
        ]
    }
}
```

### Update Player
```http
PUT /api/admin/players/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Ahmad Ali bin Abu (Updated)",
    "phone": "0123456789",
    "email": "<EMAIL>",
    "zone": "Zon A",
    "sports": [1, 3],
    "status": "active",
    "team_leader_name": "Captain Ahmad",
    "team_leader_phone": "0198765432"
}
```

**Response (200 OK):**
```json
{
    "message": "Maklumat peserta berjaya dikemaskini",
    "player": {
        "id": 1,
        "name": "Ahmad Ali bin Abu (Updated)",
        "phone": "0123456789",
        "email": "<EMAIL>",
        "zone": "Zon A",
        "status": "active",
        "sports": [
            {
                "id": 1,
                "name": "Badminton"
            },
            {
                "id": 3,
                "name": "Bola Jaring"
            }
        ]
    }
}
```

### Delete Player
```http
DELETE /api/admin/players/{id}
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "message": "Peserta berjaya dipadam"
}
```

### Check Zone Leader
```http
GET /api/admin/players/check-zone-leader
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "hasLeader": true,
    "zoneName": "Zon A",
    "message": null
}
```

**Response (Zone without leader):**
```json
{
    "hasLeader": false,
    "zoneName": "Zon A",
    "message": "Zon anda belum mempunyai ketua. Sila lantik ketua zon terlebih dahulu."
}
```

---

## 🏢 Zone Management

### Get Zones List
```http
GET /api/admin/zones
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 1,
            "code": "Zon A",
            "name": "Zon A - Kesihatan",
            "description": "Zon untuk anggota kesihatan",
            "logo_url": "/storage/zones/zona-logo.png",
            "color_primary": "#FF0000",
            "color_secondary": "#FFCCCC",
            "leader_id": 3,
            "leader_name": "Captain Ahmad",
            "leader_contact": "0123456789",
            "is_active": true,
            "display_order": 1,
            "leader": {
                "id": 3,
                "name": "Captain Ahmad",
                "username": "zona"
            },
            "players_count": 25,
            "created_at": "2025-07-14T10:30:00.000000Z"
        }
    ]
}
```

### Update Zone
```http
PUT /api/admin/zones/{code}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Zon A - Kesihatan (Updated)",
    "description": "Zon untuk anggota kesihatan dan perubatan",
    "color_primary": "#FF0000",
    "color_secondary": "#FFCCCC",
    "leader_id": 3,
    "leader_name": "Captain Ahmad Updated",
    "leader_contact": "0123456789",
    "is_active": true,
    "display_order": 1
}
```

**Response (200 OK):**
```json
{
    "message": "Maklumat zon berjaya dikemaskini",
    "zone": {
        "id": 1,
        "code": "Zon A",
        "name": "Zon A - Kesihatan (Updated)",
        "description": "Zon untuk anggota kesihatan dan perubatan",
        "color_primary": "#FF0000",
        "color_secondary": "#FFCCCC",
        "leader_name": "Captain Ahmad Updated",
        "leader_contact": "0123456789"
    }
}
```

### Update Zone Logo
```http
PATCH /api/admin/zones/{code}/logo
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "logo": [file]
}
```

**Response (200 OK):**
```json
{
    "message": "Logo zon berjaya dikemaskini",
    "logo_url": "/storage/zones/zona-logo-1642123456.png"
}
```

### Get Zone Users
```http
GET /api/admin/zones/{code}/users
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 3,
            "username": "zona",
            "name": "Zone A Admin",
            "email": "<EMAIL>",
            "role": "zone",
            "is_active": true,
            "last_login_at": "2025-07-14T10:30:00.000000Z"
        }
    ]
}
```

---

## 🏆 Match Management

### Get Matches List
```http
GET /api/matches?sport=1&status=scheduled&date=2025-07-21
Authorization: Bearer {token}
```

**Query Parameters:**
- `sport` (optional): Filter by sport ID
- `status` (optional): Filter by status (scheduled/ongoing/completed/cancelled)
- `date` (optional): Filter by match date (YYYY-MM-DD)

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 1,
            "sport_id": 1,
            "sport_name": "Badminton",
            "title": "Badminton Lelaki - Pusingan 1",
            "zone_a": "Zon A",
            "zone_b": "Zon B",
            "match_date": "2025-07-21",
            "match_time": "09:00:00",
            "venue": "Dewan Badminton",
            "status": "scheduled",
            "score_zone_a": null,
            "score_zone_b": null,
            "winner_zone": null,
            "notes": null,
            "created_by": {
                "id": 1,
                "name": "Super Admin"
            },
            "sport": {
                "id": 1,
                "name": "Badminton",
                "category": "Lelaki & Wanita"
            },
            "created_at": "2025-07-14T10:30:00.000000Z"
        }
    ]
}
```

### Create Match
```http
POST /api/matches
Authorization: Bearer {token}
Content-Type: application/json

{
    "sport_id": 1,
    "title": "Badminton Lelaki - Pusingan 1",
    "zone_a": "Zon A",
    "zone_b": "Zon B",
    "match_date": "2025-07-21",
    "match_time": "09:00",
    "venue": "Dewan Badminton",
    "notes": "Perlawanan pembukaan"
}
```

**Response (201 Created):**
```json
{
    "message": "Perlawanan berjaya dijadualkan",
    "match": {
        "id": 1,
        "sport_id": 1,
        "title": "Badminton Lelaki - Pusingan 1",
        "zone_a": "Zon A",
        "zone_b": "Zon B",
        "match_date": "2025-07-21",
        "match_time": "09:00:00",
        "venue": "Dewan Badminton",
        "status": "scheduled",
        "notes": "Perlawanan pembukaan"
    }
}
```

### Update Match Score
```http
PUT /api/matches/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "score_zone_a": 21,
    "score_zone_b": 18,
    "status": "completed",
    "winner_zone": "Zon A",
    "notes": "Perlawanan sengit, Zon A menang dengan skor tipis"
}
```

**Response (200 OK):**
```json
{
    "message": "Keputusan perlawanan berjaya dikemaskini",
    "match": {
        "id": 1,
        "score_zone_a": 21,
        "score_zone_b": 18,
        "status": "completed",
        "winner_zone": "Zon A",
        "notes": "Perlawanan sengit, Zon A menang dengan skor tipis"
    }
}
```

### Get Match Statistics
```http
GET /api/matches/stats
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "total_matches": 24,
    "scheduled_matches": 8,
    "ongoing_matches": 2,
    "completed_matches": 14,
    "cancelled_matches": 0,
    "by_sport": [
        {
            "sport_name": "Badminton",
            "total": 6,
            "completed": 4,
            "scheduled": 2
        },
        {
            "sport_name": "Bola Sepak",
            "total": 8,
            "completed": 5,
            "scheduled": 3
        }
    ],
    "by_zone": [
        {
            "zone": "Zon A",
            "wins": 8,
            "losses": 3,
            "draws": 1
        },
        {
            "zone": "Zon B",
            "wins": 6,
            "losses": 5,
            "draws": 1
        }
    ]
}
```

---

## 📝 Content Management

### Get Page Content
```http
GET /api/cms/page-content?page=homepage
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 1,
            "page": "homepage",
            "section": "hero",
            "content_key": "title",
            "content_value": "SUKAN INTRA",
            "content_type": "text",
            "description": "Main title for hero section",
            "sort_order": 1,
            "is_active": true,
            "updated_by": {
                "id": 1,
                "name": "Super Admin"
            },
            "updated_at": "2025-07-14T10:30:00.000000Z"
        },
        {
            "id": 2,
            "page": "homepage",
            "section": "hero",
            "content_key": "subtitle",
            "content_value": "KOR KESIHATAN DIRAJA 2025",
            "content_type": "text",
            "description": "Subtitle for hero section",
            "sort_order": 2,
            "is_active": true,
            "updated_at": "2025-07-14T10:30:00.000000Z"
        }
    ]
}
```

### Create/Update Content
```http
POST /api/cms/page-content
Authorization: Bearer {token}
Content-Type: application/json

{
    "page": "homepage",
    "section": "hero",
    "content_key": "title",
    "content_value": "SUKAN INTRA UPDATED",
    "content_type": "text",
    "description": "Updated main title for hero section",
    "sort_order": 1,
    "is_active": true
}
```

**Response (201 Created):**
```json
{
    "message": "Kandungan berjaya disimpan",
    "content": {
        "id": 1,
        "page": "homepage",
        "section": "hero",
        "content_key": "title",
        "content_value": "SUKAN INTRA UPDATED",
        "content_type": "text",
        "description": "Updated main title for hero section",
        "sort_order": 1,
        "is_active": true
    }
}
```

### Upload CMS Image
```http
POST /api/cms/upload-image
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "image": [file],
    "section": "hero",
    "description": "Hero background image"
}
```

**Response (200 OK):**
```json
{
    "message": "Imej berjaya dimuat naik",
    "image_url": "/storage/cms/hero-bg-1642123456.jpg",
    "filename": "hero-bg-1642123456.jpg",
    "size": 1024000,
    "dimensions": {
        "width": 1920,
        "height": 1080
    }
}
```

### Delete Content
```http
DELETE /api/cms/page-content/{id}
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "message": "Kandungan berjaya dipadam"
}
```

---

## ⚙️ Settings Management

### Get Settings
```http
GET /api/settings
Authorization: Bearer {token}
```

**Response (200 OK):**
```json
{
    "site_name": "Portal Sukan Intra KKD",
    "site_tagline": "Platform Rasmi Sukan Intra",
    "site_description": "Portal rasmi untuk pengurusan sukan intra Kor Kesihatan DiRaja",
    "logo_url": "/storage/logo/logo.png",
    "favicon_url": "/storage/logo/favicon.ico",
    "header_title_part1": "SUKAN",
    "header_title_part2": "INTRA",
    "header_subtitle": "KOR KESIHATAN DIRAJA 2025",
    "copyright_text": "© 2025 Kor Kesihatan DiRaja. Hak cipta terpelihara.",
    "contact_email": "<EMAIL>",
    "contact_phone": "03-12345678"
}
```

### Update General Settings
```http
POST /api/admin/settings/general
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "site_name": "Portal Sukan Intra KKD Updated",
    "site_tagline": "Platform Rasmi Sukan Intra",
    "site_description": "Portal rasmi untuk pengurusan sukan intra",
    "header_title_part1": "SUKAN",
    "header_title_part2": "INTRA",
    "header_subtitle": "KOR KESIHATAN DIRAJA 2025",
    "copyright_text": "© 2025 Kor Kesihatan DiRaja",
    "logo": [file]
}
```

**Response (200 OK):**
```json
{
    "message": "Tetapan am berjaya dikemaskini",
    "settings": {
        "site_name": "Portal Sukan Intra KKD Updated",
        "logo_url": "/storage/logo/logo-1642123456.png",
        "header_title_part1": "SUKAN",
        "header_title_part2": "INTRA",
        "header_subtitle": "KOR KESIHATAN DIRAJA 2025"
    }
}
```

### Upload Logo
```http
POST /api/settings/upload-logo
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "logo": [file]
}
```

**Response (200 OK):**
```json
{
    "message": "Logo berjaya dimuat naik",
    "logo_url": "/storage/logo/logo-1642123456.png",
    "filename": "logo-1642123456.png",
    "uploaded_at": "2025-07-14T10:30:00.000000Z"
}
```

### Upload Favicon
```http
POST /api/settings/upload-favicon
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "favicon": [file]
}
```

**Response (200 OK):**
```json
{
    "message": "Favicon berjaya dimuat naik",
    "favicon_url": "/storage/logo/favicon.ico",
    "filename": "favicon.ico",
    "uploaded_at": "2025-07-14T10:30:00.000000Z"
}
```

---

## 🌐 Public APIs

### Get Public Settings
```http
GET /api/public/settings
```

**Response (200 OK):**
```json
{
    "site_name": "Portal Sukan Intra KKD",
    "logo_url": "/storage/logo/logo.png",
    "header_title_part1": "SUKAN",
    "header_title_part2": "INTRA",
    "header_subtitle": "KOR KESIHATAN DIRAJA 2025",
    "copyright_text": "© 2025 Kor Kesihatan DiRaja"
}
```

### Get Homepage Content
```http
GET /api/public/homepage-content
```

**Response (200 OK):**
```json
{
    "hero": {
        "title": "SUKAN INTRA",
        "subtitle": "KOR KESIHATAN DIRAJA 2025",
        "description": "Platform rasmi untuk pengurusan sukan intra",
        "countdown_text": "21-25 JULAI 2025",
        "background_image": "/storage/cms/hero-bg.jpg"
    },
    "sports": {
        "title": "Kategori Sukan",
        "subtitle": "7 kategori sukan yang dipertandingkan",
        "background_image": "/storage/cms/sports-bg.jpg",
        "sports_data": "[{\"name\":\"Badminton\",\"icon\":\"🏸\"},{\"name\":\"Bola Sepak\",\"icon\":\"⚽\"}]"
    },
    "stats": {
        "title": "Statistik Penyertaan",
        "subtitle": "Jumlah penyertaan mengikut kategori",
        "background_image": "/storage/cms/stats-bg.jpg",
        "participants_count": "150",
        "sports_count": "7",
        "zones_count": "3",
        "events_count": "24"
    },
    "cta": {
        "title": "Sertai Sukan Intra KKD 2025",
        "subtitle": "Daftar sekarang dan sertai pertandingan",
        "description": "Jangan lepaskan peluang untuk menyertai sukan intra",
        "background_image": "/storage/cms/cta-bg.jpg"
    }
}
```

### Get Page Content
```http
GET /api/public/content/{page}
```

**Example:**
```http
GET /api/public/content/match-schedule
```

**Response (200 OK):**
```json
{
    "hero": {
        "title": "JADUAL PERLAWANAN",
        "subtitle": "Jadual lengkap semua perlawanan",
        "description": "Saksikan semua perlawanan dari 21-25 Julai 2025",
        "background_image": "/storage/cms/match-schedule-bg.jpg"
    }
}
```

### Get Public Matches
```http
GET /api/public/matches?sport=1&date=2025-07-21
```

**Response (200 OK):**
```json
{
    "data": [
        {
            "id": 1,
            "sport_name": "Badminton",
            "zone_a": "Zon A",
            "zone_b": "Zon B",
            "match_date": "2025-07-21",
            "match_time": "09:00:00",
            "venue": "Dewan Badminton",
            "status": "scheduled"
        }
    ]
}
```

---

## ❌ Error Handling

### Standard Error Response Format
```json
{
    "message": "Error message in Bahasa Malaysia",
    "errors": {
        "field_name": [
            "Specific error message for this field"
        ]
    }
}
```

### HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful GET, PUT, PATCH requests |
| 201 | Created | Successful POST requests |
| 204 | No Content | Successful DELETE requests |
| 400 | Bad Request | Invalid request format |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |

### Common Error Examples

#### Validation Error (422)
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "name": ["Nama diperlukan"],
        "phone": ["Format no. telefon tidak sah"],
        "zone": ["Zon diperlukan"]
    }
}
```

#### Authentication Error (401)
```json
{
    "message": "Unauthenticated."
}
```

#### Authorization Error (403)
```json
{
    "message": "Anda tidak mempunyai kebenaran untuk mengakses sumber ini."
}
```

#### Not Found Error (404)
```json
{
    "message": "Sumber yang diminta tidak dijumpai."
}
```

#### Rate Limit Error (429)
```json
{
    "message": "Terlalu banyak permintaan. Sila cuba lagi kemudian.",
    "retry_after": 60
}
```

---

## 🚦 Rate Limiting

### Default Limits
- **Authentication endpoints:** 5 requests per minute
- **General API endpoints:** 60 requests per minute
- **File upload endpoints:** 10 requests per minute

### Rate Limit Headers
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1642123456
```

### Handling Rate Limits
When rate limit is exceeded, the API returns HTTP 429 with retry information:

```json
{
    "message": "Terlalu banyak permintaan. Sila cuba lagi kemudian.",
    "retry_after": 60
}
```

---

**Portal Sukan Intra Kor Kesihatan DiRaja 2025 - API Reference**  
Last Updated: July 2025  
Version: 1.0.0
