# Portal Sukan Intra Kor Kesihatan DiRaja 2025 - Complete Documentation

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Installation & Setup](#installation--setup)
4. [User Management](#user-management)
5. [Core Features](#core-features)
6. [API Documentation](#api-documentation)
7. [Database Schema](#database-schema)
8. [Frontend Components](#frontend-components)
9. [Production Setup](#production-setup)
10. [Troubleshooting](#troubleshooting)

---

## 🎯 Project Overview

### Purpose
Portal Sukan Intra Kor Kesihatan DiRaja 2025 adalah sistem pengurusan tournament sukan yang direka khusus untuk anggota tentera. Sistem ini menyediakan platform lengkap untuk pendaftaran peserta, pengurusan perlawanan, dan pengurusan content secara dinamik.

### Key Features
- **Dynamic Content Management System (CMS)**
- **Zone-based Player Management**
- **Real-time Match Scheduling & Results**
- **Professional Sports Website Design**
- **Mobile-responsive Admin Panel**
- **Sanctum Token Authentication**
- **Image Upload & Compression**
- **Multi-role User Management**

### Technology Stack
- **Frontend:** React 19 + TypeScript + Tailwind CSS v4 + Shadcn UI
- **Backend:** Laravel 12 + MySQL
- **Authentication:** Laravel Sanctum
- **State Management:** TanStack Query + React Context
- **Routing:** React Router DOM
- **Validation:** Zod
- **Notifications:** React Hot Toast

---

## 🏗️ System Architecture

### Application Structure
```
Portal Sukan/
├── Frontend (React SPA)
│   ├── Public Pages (No Auth Required)
│   ├── Admin Dashboard (Auth Required)
│   └── Authentication System
├── Backend (Laravel API)
│   ├── API Routes (/api/*)
│   ├── Public API (/api/public/*)
│   └── Admin API (/api/admin/*)
└── Database (MySQL)
    ├── User Management
    ├── Zone Management
    ├── Sports & Matches
    ├── CMS Content
    └── Settings
```

### User Roles & Permissions
1. **Superadmin**
   - Full system access
   - User management
   - System configuration
   - All CMS functions

2. **Admin**
   - Limited system access
   - Zone-specific management
   - Content management
   - Reports access

3. **Zone**
   - Zone profile management
   - Player registration (if zone has leader)
   - Zone-specific data access

### Authentication Flow
1. User login dengan username/password
2. Laravel Sanctum generates token
3. Token stored dalam localStorage
4. All API requests include Bearer token
5. Role-based access control pada routes

---

## 🚀 Installation & Setup

### Prerequisites
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- NPM/Yarn

### Local Development Setup

#### 1. Clone Repository
```bash
git clone <repository-url>
cd intra-kor
```

#### 2. Backend Setup
```bash
# Install PHP dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database in .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=intra_kor
DB_USERNAME=root
DB_PASSWORD=

# Run migrations and seeders
php artisan migrate
php artisan db:seed

# Create storage symlink
php artisan storage:link
```

#### 3. Frontend Setup
```bash
# Install Node dependencies
npm install

# Build assets for development
npm run dev
```

#### 4. Start Development Servers
```bash
# Start Laravel server
php artisan serve

# Start Vite dev server (in another terminal)
npm run dev
```

### Production Setup
Refer to [Production Setup](#production-setup) section.

---

## 👥 User Management

### Default Users
System comes with pre-seeded users:

| Username | Password | Role | Zone | Description |
|----------|----------|------|------|-------------|
| superadmin | Abcd@1234 | superadmin | - | Full system access |
| admin | Abcd@1234 | admin | - | General admin access |
| zona | Abcd@1234 | zone | Zon A | Zone A management |
| zonb | Abcd@1234 | zone | Zon B | Zone B management |
| zonc | Abcd@1234 | zone | Zon C | Zone C management |

**Note:** All users must change password on first login.

### User Management Features
- **Password Change Requirement:** Mandatory on first login
- **Role-based Access Control:** Different permissions per role
- **Zone Assignment:** Users assigned to specific zones
- **Activity Tracking:** Last login tracking
- **Account Status:** Active/inactive user management

### Adding New Users
1. Login as superadmin
2. Navigate to User Management (if implemented)
3. Create new user with appropriate role and zone
4. User receives temporary password
5. User must change password on first login

---

## 🎯 Core Features

### 1. Content Management System (CMS)

#### Page Content Management
- **Homepage Sections:** Hero, Sports, Statistics, CTA
- **Dynamic Content:** All text content editable
- **Image Management:** Background images per section
- **Real-time Updates:** Changes immediately visible

#### CMS Structure
```
Page Content:
├── Homepage
│   ├── Hero Section (title, subtitle, description, background)
│   ├── Sports Section (sports data, background)
│   ├── Statistics Section (counts, background)
│   └── CTA Section (call-to-action content)
├── Match Schedule
├── Tournament Results
├── Gallery
├── News
└── Rules
```

#### Content Types
- **Text:** Plain text content
- **HTML:** Rich text with formatting
- **Image:** Image URLs and file paths
- **JSON:** Structured data (sports list, statistics)

### 2. Zone Management

#### Zone Structure
- **Zone A, B, C:** Primary zones
- **Flexible Zones:** Can add "Bakat KKD dan lain-lain"
- **Zone Leaders:** Each zone has assigned leader
- **Zone Branding:** Custom logos and colors

#### Zone Features
- **Leader Assignment:** Zone must have leader before adding players
- **Player Management:** Zone-specific player registration
- **Statistics Tracking:** Zone performance metrics
- **Profile Management:** Zone information and branding

### 3. Player Management

#### Registration Process
- **Zone-based Registration:** Players assigned to zones
- **Multi-sport Participation:** Players can join multiple sports
- **Team Leader Information:** Required for each player
- **Validation:** Phone number format, email optional

#### Player List Features
- **Advanced Filtering:** Filter by zone, sport, and search by name
- **Pagination:** 5 players per page with navigation controls
- **Real-time Search:** Instant search results as you type
- **Export Functionality:** Download filtered results as Excel
- **Responsive Design:** Optimized for all screen sizes

#### Player Data Structure
```php
Player {
    name: string
    phone: string (Malaysian format)
    email: string (optional)
    zone: string
    sports: array
    team_leader_name: string
    team_leader_phone: string
    status: active|inactive
}
```

### 4. Match Management

#### Match Types
- **Zone vs Zone:** Inter-zone competitions
- **Sport-specific:** Matches organized by sport
- **Tournament Format:** Flexible tournament structures

#### Match Features
- **Real-time Scoring:** Live score updates
- **Status Tracking:** scheduled, ongoing, completed, cancelled
- **Venue Management:** Location and time tracking
- **Results Recording:** Winner determination and statistics

### 5. Settings Management

#### General Settings
- **Site Information:** Name, tagline, description
- **Header Configuration:** Multi-part title with colors
- **Logo Management:** Upload and auto-favicon
- **Copyright Information:** Footer content

#### System Settings
- **Image Compression:** Automatic file optimization
- **File Storage:** Organized directory structure
- **Cache Management:** Performance optimization
- **Security Settings:** Authentication configuration

---

## 📡 API Documentation

### Authentication Endpoints

#### Login
```http
POST /api/login
Content-Type: application/json

{
    "username": "superadmin",
    "password": "Abcd@1234",
    "remember": false
}

Response:
{
    "token": "1|abc123...",
    "user": {
        "id": 1,
        "username": "superadmin",
        "name": "Super Admin",
        "role": "superadmin",
        "must_change_password": true
    }
}
```

#### Logout
```http
POST /api/logout
Authorization: Bearer {token}

Response:
{
    "message": "Logged out successfully"
}
```

#### Change Password
```http
PUT /api/password/change
Authorization: Bearer {token}
Content-Type: application/json

{
    "current_password": "Abcd@1234",
    "new_password": "NewPassword123!",
    "new_password_confirmation": "NewPassword123!"
}
```

### Player Management Endpoints

#### Get All Players (Superadmin/Admin)
```http
GET /api/admin/players/all?page=1&per_page=5&search=&zone=&sport=
Authorization: Bearer {token}

Parameters:
- page: Page number (default: 1)
- per_page: Items per page (default: 5)
- search: Search by player name
- zone: Filter by zone code (e.g., "A", "B", "C")
- sport: Filter by sport ID

Response:
{
    "data": [
        {
            "id": 1,
            "name": "Ahmad Ali",
            "phone": "0123456789",
            "player_type": "main",
            "status": "active",
            "zone": "A",
            "zone_name": "Zon A",
            "sport": "Badminton",
            "sport_id": 1,
            "team_leader_name": "Captain Ahmad",
            "team_leader_phone": "0198765432",
            "created_at": "15/01/2025"
        }
    ],
    "current_page": 1,
    "last_page": 10,
    "per_page": 5,
    "total": 50,
    "from": 1,
    "to": 5,
    "players": [...] // Backward compatibility
}
```

#### Get Players (Zone Admin)
```http
GET /api/admin/players?page=1&per_page=5&search=&zone=&sport=
Authorization: Bearer {token}

Response: (Same structure as above, filtered by user's zone)
```

#### Create Player
```http
POST /api/admin/players
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Ahmad Ali",
    "phone": "0123456789",
    "email": "<EMAIL>",
    "zone": "Zon A",
    "sports": [1, 2],
    "team_leader_name": "Captain Ahmad",
    "team_leader_phone": "0198765432"
}
```

### CMS Endpoints

#### Get Page Content
```http
GET /api/public/content/homepage
Response:
{
    "hero": {
        "title": "SUKAN INTRA",
        "subtitle": "KOR KESIHATAN DIRAJA 2025",
        "description": "Platform rasmi...",
        "background_image": "/storage/images/hero-bg.jpg"
    },
    "sports": { ... },
    "stats": { ... },
    "cta": { ... }
}
```

#### Update Content
```http
POST /api/cms/page-content
Authorization: Bearer {token}
Content-Type: application/json

{
    "page": "homepage",
    "section": "hero",
    "content_key": "title",
    "content_value": "SUKAN INTRA",
    "content_type": "text"
}
```

### Settings Endpoints

#### Get Public Settings
```http
GET /api/public/settings
Response:
{
    "site_name": "Portal Sukan Intra KKD",
    "logo_url": "/storage/logo/logo.png",
    "header_title_part1": "SUKAN",
    "header_title_part2": "INTRA",
    "header_subtitle": "KOR KESIHATAN DIRAJA 2025"
}
```

#### Upload Logo
```http
POST /api/admin/settings/general
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "site_name": "Portal Sukan",
    "logo": [file],
    "header_title_part1": "SUKAN",
    "header_title_part2": "INTRA"
}
```

---

## 🗄️ Database Schema

### Core Tables

#### users
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('superadmin', 'admin', 'zone') NOT NULL,
    zone VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    must_change_password BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### zones
```sql
CREATE TABLE zones (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    color_primary VARCHAR(7),
    color_secondary VARCHAR(7),
    leader_id BIGINT,
    leader_name VARCHAR(255),
    leader_contact VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (leader_id) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

#### players
```sql
CREATE TABLE players (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    service_number VARCHAR(50),
    zone VARCHAR(50) NOT NULL,
    rank VARCHAR(50),
    unit VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    team_leader_name VARCHAR(255),
    team_leader_phone VARCHAR(20),
    emergency_contact VARCHAR(255),
    emergency_phone VARCHAR(20),
    medical_conditions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    registered_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registered_by) REFERENCES users(id),
    INDEX idx_zone (zone),
    INDEX idx_status (status)
);
```

#### sports
```sql
CREATE TABLE sports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    rules TEXT,
    max_players_per_team INT,
    min_players_per_team INT,
    icon VARCHAR(255),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### matches
```sql
CREATE TABLE matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sport_id BIGINT NOT NULL,
    title VARCHAR(255),
    zone_a VARCHAR(50) NOT NULL,
    zone_b VARCHAR(50) NOT NULL,
    match_date DATE NOT NULL,
    match_time TIME NOT NULL,
    venue VARCHAR(255) NOT NULL,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
    score_zone_a INT,
    score_zone_b INT,
    winner_zone VARCHAR(50),
    notes TEXT,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sport_id) REFERENCES sports(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_match_date (match_date),
    INDEX idx_status (status)
);
```

#### page_content
```sql
CREATE TABLE page_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    page VARCHAR(100) NOT NULL,
    section VARCHAR(100),
    sort_order INT DEFAULT 0,
    content_key VARCHAR(255) NOT NULL,
    content_value TEXT,
    content_type ENUM('text', 'image', 'json', 'html') DEFAULT 'text',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_page (page),
    INDEX idx_page_section_order (page, section, sort_order)
);
```

#### site_settings
```sql
CREATE TABLE site_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    type ENUM('text', 'number', 'boolean', 'json', 'file') DEFAULT 'text',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

### Relationship Tables

#### player_sports
```sql
CREATE TABLE player_sports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    player_id BIGINT NOT NULL,
    sport_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
    UNIQUE KEY unique_player_sport (player_id, sport_id)
);
```

#### tournament_results
```sql
CREATE TABLE tournament_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    sport_id BIGINT NOT NULL,
    zone VARCHAR(50) NOT NULL,
    position INT NOT NULL,
    points INT DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    notes TEXT,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sport_id) REFERENCES sports(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_sport_position (sport_id, position)
);
```

---

## 🎨 Frontend Components

### Component Architecture

#### Public Components
Located in `resources/js/components/public/`

- **HeroSection.tsx** - Homepage hero with countdown
- **SportsCategories.tsx** - Sports grid display
- **StatsSection.tsx** - Statistics showcase
- **CTASection.tsx** - Call-to-action section
- **PublicHeader.tsx** - Public navigation
- **PublicFooter.tsx** - Footer with links

#### Admin Components
Located in `resources/js/components/admin/`

- **Players/** - Player management components
  - `PlayersListTable.tsx` - Paginated player list with filters
  - `SportsTable.tsx` - Zone sports management table
  - `SportManagementModal.tsx` - Add/edit players for sports
  - `PlayerDetailsModal.tsx` - View player details
- **Matches/** - Match scheduling components
- **Settings/** - System settings components
- **PageContent/** - CMS content editors

#### UI Components
Located in `resources/js/components/ui/` (Shadcn UI)

- **Button, Card, Dialog, Input, Select** - Base UI components
- **Table, Tabs, Badge, Avatar** - Data display components
- **Form, Label, Checkbox** - Form components

### Context Providers

#### AuthContext
```typescript
interface AuthContextType {
    user: User | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    login: (username: string, password: string) => Promise<any>;
    logout: () => Promise<void>;
    updatePassword: (current: string, new: string) => Promise<void>;
    checkAuth: () => Promise<void>;
}
```

#### SettingsContext
```typescript
interface SettingsContextType {
    settings: SiteSettings;
    loading: boolean;
    updateSettings: (settings: Partial<SiteSettings>) => Promise<void>;
    refreshSettings: () => Promise<void>;
}
```

#### ZoneContext
```typescript
interface ZoneContextType {
    zones: Zone[];
    currentZone: Zone | null;
    loading: boolean;
    refreshZones: () => Promise<void>;
}
```

### Routing Structure

#### Public Routes
```typescript
// Public pages (no authentication required)
/ - Homepage
/match-schedule - Match schedules
/tournament-results - Results and rankings
/gallery - Photo gallery
/news - News and announcements
/rules - Competition rules
/social-media - Social media links
```

#### Protected Routes
```typescript
// Admin dashboard (authentication required)
/dashboard - Main dashboard
/dashboard/admin/players - Player management
/dashboard/admin/matches - Match management
/dashboard/admin/zone-profile - Zone profile
/dashboard/admin/reports - Reports and statistics
/dashboard/admin/settings - System settings
/dashboard/admin/cms/page-content - CMS content management
```

#### Authentication Routes
```typescript
/auth/login - Login page
/auth/password/change - Password change (first login)
```

### State Management

#### TanStack Query Usage
```typescript
// Fetch players with caching
const { data: players, isLoading } = useQuery({
    queryKey: ['players', page, search, zone],
    queryFn: () => api.get('/admin/players', { params: { page, search, zone } }),
    staleTime: 5 * 60 * 1000, // 5 minutes
});

// Create player mutation
const createPlayerMutation = useMutation({
    mutationFn: (playerData) => api.post('/admin/players', playerData),
    onSuccess: () => {
        queryClient.invalidateQueries(['players']);
        toast.success('Peserta berjaya didaftarkan');
    },
});
```

#### Form Validation with Zod
```typescript
const playerSchema = z.object({
    name: z.string().min(2, 'Nama mestilah sekurang-kurangnya 2 aksara'),
    phone: z.string().regex(/^(\+?6?01)[0-46-9]-*[0-9]{7,8}$/, 'Format telefon tidak sah'),
    zone: z.string().min(1, 'Zon diperlukan'),
    sports: z.array(z.number()).min(1, 'Pilih sekurang-kurangnya satu sukan'),
});

type PlayerFormData = z.infer<typeof playerSchema>;
```

### Pagination Implementation

#### Frontend Pagination
```typescript
// Pagination state management
const [currentPage, setCurrentPage] = useState<number>(1);
const [totalPages, setTotalPages] = useState<number>(1);
const [totalPlayers, setTotalPlayers] = useState<number>(0);
const [perPage] = useState<number>(5);

// Fetch with pagination
const fetchPlayers = async () => {
    const params = new URLSearchParams();
    if (selectedZone && selectedZone !== 'all') params.append('zone', selectedZone);
    if (selectedSport && selectedSport !== 'all') params.append('sport', selectedSport);
    if (searchTerm) params.append('search', searchTerm);
    params.append('page', currentPage.toString());
    params.append('per_page', perPage.toString());

    const response = await api.get(`/admin/players/all?${params}`);
    setPlayers(response.data.data || response.data.players || []);
    setTotalPages(response.data.last_page || 1);
    setTotalPlayers(response.data.total || 0);
};
```

#### Pagination Controls
- **Previous/Next Buttons:** Navigate between pages
- **Page Numbers:** Direct page navigation (max 5 visible)
- **Smart Page Display:** Shows relevant page numbers based on current position
- **Results Counter:** Shows "X to Y of Z results"
- **Auto-reset:** Returns to page 1 when filters change

---

## 🚀 Production Setup

### cPanel Deployment

#### 1. Build Production Assets
```bash
# Install dependencies
npm install

# Build for production
npm run build
```

#### 2. Prepare Files for Upload
1. **Laravel Backend Files**: Upload entire project root to your cPanel directory (same level as public_html)
2. **Built Assets**: The `public/build/` folder contains your compiled assets

#### 3. Database Configuration
1. Create MySQL database in cPanel
2. Update `.env` file:
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password

SANCTUM_STATEFUL_DOMAINS=yourdomain.com
SESSION_DOMAIN=yourdomain.com
FORCE_HTTPS=true
```

#### 4. Run Migrations
In cPanel Terminal or SSH:
```bash
php artisan migrate --force
php artisan db:seed --force
```

#### 5. Create Storage Symlink
```bash
php artisan storage:link
```

#### 6. Set Permissions
```bash
chmod -R 755 ./
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

#### 7. Clear Cache
```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

### Environment Variables

#### Production .env
```env
APP_NAME="Portal Sukan Intra KKD"
APP_ENV=production
APP_KEY=base64:your-app-key
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

SESSION_DRIVER=database
SESSION_LIFETIME=120

CACHE_STORE=database
QUEUE_CONNECTION=database

SANCTUM_STATEFUL_DOMAINS=yourdomain.com
SESSION_DOMAIN=yourdomain.com
```

### Build Process

#### Production Build
```bash
# Install dependencies
npm install

# Build for production
npm run build

# The build files will be in public/build/
```

#### Laravel Optimization
```bash
# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

### SSL Configuration
1. Enable SSL in cPanel
2. Update APP_URL to https://
3. Configure SANCTUM_STATEFUL_DOMAINS
4. Test all functionality

---

## 🔧 Troubleshooting

### Common Issues

#### 1. Authentication Issues

**Problem:** Mixed Content Error on Login (HTTPS/HTTP)
```
Error: "Mixed Content: The page at 'https://...' was loaded over HTTPS, but requested an insecure XMLHttpRequest endpoint 'http://...'"

Solution:
1. Ensure APP_URL in .env uses https://
2. Set FORCE_HTTPS=true in .env
3. Add Content Security Policy meta tag in app.blade.php
4. Clear browser cache and try again
5. Check axios configuration uses correct protocol
```

**Problem:** TypeError p.createElement in Production
```
Error: "Uncaught TypeError: p.createElement is not a function"

Solution:
1. Rebuild production assets: npm run build
2. Check manifest.json exists in public/build/.vite/
3. Verify all JavaScript chunks are loaded correctly
4. Clear browser cache completely
5. Check app.blade.php loads assets from manifest
```

**Problem:** Token not working after login
```
Solution:
1. Check SANCTUM_STATEFUL_DOMAINS in .env
2. Verify SESSION_DOMAIN matches your domain
3. Clear browser localStorage
4. Check CORS configuration
5. Ensure withCredentials: true in axios config
```

**Problem:** Password change not working
```
Solution:
1. Verify current password is correct
2. Check password validation rules
3. Ensure user has must_change_password flag
4. Check API endpoint is accessible
```

#### 2. File Upload Issues

**Problem:** Images not uploading
```
Solution:
1. Check storage directory permissions (775)
2. Verify storage symlink exists
3. Check file size limits in php.ini
4. Ensure storage/app/public directory exists
```

**Problem:** Images not displaying
```
Solution:
1. Verify storage symlink: ln -s ../laravel-app/storage/app/public storage
2. Check file paths in database
3. Verify web server can access storage directory
4. Check .htaccess configuration
```

#### 3. Database Issues

**Problem:** Migration fails
```
Solution:
1. Check database credentials in .env
2. Ensure database exists
3. Verify user has proper permissions
4. Check for existing tables with same names
```

**Problem:** Seeder fails
```
Solution:
1. Run migrations first: php artisan migrate
2. Check for foreign key constraints
3. Verify seeder files are not corrupted
4. Run seeders individually to identify issues
```

#### 4. Performance Issues

**Problem:** Slow page loading
```
Solution:
1. Enable caching: php artisan config:cache
2. Optimize images (automatic compression enabled)
3. Check database query performance
4. Enable gzip compression in .htaccess
```

**Problem:** High memory usage
```
Solution:
1. Increase PHP memory limit
2. Optimize database queries
3. Implement pagination for large datasets
4. Clear unnecessary cache files
```

#### 5. Frontend Issues

**Problem:** React components not loading
```
Solution:
1. Check build files exist in public/build/
2. Verify Vite configuration
3. Check browser console for errors
4. Ensure all dependencies are installed
```

**Problem:** API calls failing
```
Solution:
1. Check API base URL configuration
2. Verify CORS settings
3. Check authentication token
4. Verify API endpoints are accessible
```

### Debug Commands

#### Laravel Debug
```bash
# Check application status
php artisan about

# View logs
tail -f storage/logs/laravel.log

# Check routes
php artisan route:list

# Check configuration
php artisan config:show

# Clear all cache
php artisan optimize:clear
```

#### Database Debug
```bash
# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Check migrations status
php artisan migrate:status

# Rollback migrations
php artisan migrate:rollback

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

### Log Files

#### Important Log Locations
- **Laravel Logs:** `storage/logs/laravel.log`
- **Web Server Logs:** Check cPanel error logs
- **PHP Logs:** Check cPanel PHP error logs
- **Database Logs:** MySQL slow query log

#### Log Monitoring
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor with filtering
tail -f storage/logs/laravel.log | grep ERROR
```

### Performance Monitoring

#### Key Metrics to Monitor
- **Page Load Time:** Should be < 3 seconds
- **Database Query Time:** Monitor slow queries
- **Memory Usage:** Check PHP memory limits
- **Storage Usage:** Monitor file uploads

#### Optimization Tips
1. **Enable Caching:** Use Laravel's built-in caching
2. **Optimize Images:** Automatic compression is enabled
3. **Database Indexing:** Ensure proper indexes on frequently queried columns
4. **CDN Usage:** Consider CDN for static assets
5. **Gzip Compression:** Enable in web server configuration

---

## 📞 Support & Maintenance

### Regular Maintenance Tasks

#### Daily
- Monitor error logs
- Check system performance
- Verify backup completion

#### Weekly
- Update content as needed
- Review user activity
- Check storage usage

#### Monthly
- Update dependencies (if needed)
- Review security logs
- Performance optimization

### Backup Strategy

#### Database Backup
```bash
# Create database backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# Restore from backup
mysql -u username -p database_name < backup_20250714.sql
```

#### File Backup
```bash
# Backup uploaded files
tar -czf storage_backup_$(date +%Y%m%d).tar.gz storage/app/public/

# Backup entire application
tar -czf app_backup_$(date +%Y%m%d).tar.gz laravel-app/
```

### Security Considerations

#### Regular Security Tasks
1. **Update Dependencies:** Keep Laravel and packages updated
2. **Monitor Logs:** Check for suspicious activity
3. **Password Policy:** Enforce strong passwords
4. **File Permissions:** Regularly verify correct permissions
5. **SSL Certificate:** Ensure SSL is active and valid

#### Security Best Practices
- Use strong database passwords
- Limit file upload types and sizes
- Regular security audits
- Monitor failed login attempts
- Keep backups secure and encrypted

---

## 📚 Additional Resources

### Laravel Documentation
- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [Laravel Sanctum](https://laravel.com/docs/12.x/sanctum)
- [Laravel Migrations](https://laravel.com/docs/12.x/migrations)

### React Documentation
- [React 19 Documentation](https://react.dev/)
- [TanStack Query](https://tanstack.com/query/latest)
- [React Router](https://reactrouter.com/)

### UI Framework
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn UI](https://ui.shadcn.com/)
- [Lucide Icons](https://lucide.dev/)

### Development Tools
- [Vite](https://vitejs.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [Zod Validation](https://zod.dev/)

---

**Portal Sukan Intra Kor Kesihatan DiRaja 2025**  
*Complete Sports Tournament Management System*

Last Updated: July 2025  
Version: 1.0.0
