# Sistem Jadual Pertandingan - Portal Sukan Intra KKD 2025

## Overview

Sistem Jadual Pertandingan adalah modul komprehensif yang menguruskan jadual lengkap pertandingan untuk Sukan Intra Kor Kesihatan DiRaja 2025. Sistem ini menyokong pelbagai jenis sukan, tournament brackets, dan paparan awam yang responsif.

## Features Utama

### 1. Pengurusan <PERSON>ad<PERSON> (Sports Schedules)
- **CRUD Operations**: Tambah, edit, padam jadual sukan
- **Multi-Sport Support**: Sokongan untuk pelbagai jenis sukan
- **Date Range Management**: Pengurusan tarikh mula dan tamat
- **Venue Management**: Pengurusan venue utama untuk setiap jadual

### 2. Pengurus<PERSON> (Matches)
- **Comprehensive Match Data**: 
  - Bil (nombor urutan)
  - Tajuk perlawanan
  - <PERSON><PERSON> (First Round, Semi-Final, Final, etc.)
  - Pasukan (Zone A vs Zone B atau pasukan khas)
  - <PERSON><PERSON><PERSON> dan masa (mula dan tamat)
  - Venue dan court/field
  - <PERSON><PERSON><PERSON> dan jenis jantina
  - Status (scheduled, ongoing, completed, cancelled)

### 3. Tournament Bracket System
- **Bracket Rounds**: First Round, Semi-Final, Third Place, Final
- **Bracket Positioning**: Sistem kedudukan dalam bracket
- **Visual Bracket Display**: Paparan visual tournament bracket
- **Winner Tracking**: Pengesanan pemenang setiap perlawanan

### 4. Advanced Features
- **Color Coding**: Sistem warna untuk highlight perlawanan penting
- **Filtering & Search**: Penapis mengikut sukan, status, jadual
- **Export/Import**: Sokongan Excel dan CSV
- **Live Updates**: Kemaskini langsung untuk perlawanan sedang berlangsung

## Database Structure

### Tables

#### 1. sports_schedules
```sql
- id (Primary Key)
- sport_id (Foreign Key to sports table)
- schedule_name (VARCHAR)
- description (TEXT)
- start_date (DATE)
- end_date (DATE)
- main_venue (VARCHAR)
- is_active (BOOLEAN)
- display_order (INTEGER)
- created_by (Foreign Key to users)
- timestamps
```

#### 2. sport_matches
```sql
- id (Primary Key)
- bil (INTEGER) - Nombor urutan
- sport_id (Foreign Key to sports)
- sports_schedule_id (Foreign Key to sports_schedules)
- title (VARCHAR) - Tajuk perlawanan
- match_type (VARCHAR) - Jenis perlawanan
- zone_a (VARCHAR) - Pasukan A
- zone_b (VARCHAR) - Pasukan B
- special_teams (VARCHAR) - Pasukan khas
- match_date (DATE)
- match_time (TIME)
- end_time (TIME)
- venue (VARCHAR)
- court_field (VARCHAR)
- category (VARCHAR)
- gender_type (ENUM: Lelaki, Perempuan, Mixed)
- status (ENUM: scheduled, ongoing, completed, cancelled)
- is_highlighted (BOOLEAN)
- highlight_color (VARCHAR)
- bracket_round (VARCHAR)
- bracket_position (INTEGER)
- score_zone_a (INTEGER)
- score_zone_b (INTEGER)
- winner_zone (VARCHAR)
- notes (TEXT)
- timestamps
```

## API Endpoints

### Admin Endpoints (Protected)

#### Sports Schedules
```
GET    /api/admin/sports-schedules           - List all schedules
POST   /api/admin/sports-schedules           - Create new schedule
GET    /api/admin/sports-schedules/{id}      - Get specific schedule
PUT    /api/admin/sports-schedules/{id}      - Update schedule
DELETE /api/admin/sports-schedules/{id}      - Delete schedule
```

#### Sport Matches
```
GET    /api/admin/sport-matches              - List all matches
POST   /api/admin/sport-matches              - Create new match
GET    /api/admin/sport-matches/{id}         - Get specific match
PUT    /api/admin/sport-matches/{id}         - Update match
DELETE /api/admin/sport-matches/{id}         - Delete match
GET    /api/admin/sport-matches/form-data/all - Get form data
GET    /api/admin/sport-matches/export       - Export matches
POST   /api/admin/sport-matches/import       - Import matches
GET    /api/admin/sport-matches/template     - Download template
```

### Public Endpoints (No Authentication)

```
GET /api/public/jadual/sports-schedules      - Get public schedules
GET /api/public/jadual/matches               - Get public matches
GET /api/public/jadual/matches/schedule/{id} - Get matches by schedule
GET /api/public/jadual/tournament-brackets   - Get tournament brackets
GET /api/public/jadual/live-matches          - Get live matches
GET /api/public/jadual/upcoming-matches      - Get upcoming matches
GET /api/public/jadual/statistics            - Get match statistics
```

## Frontend Components

### Admin Components

#### 1. JadualPertandingan (Main Admin Page)
- **Location**: `resources/js/pages/admin/jadual-pertandingan.tsx`
- **Features**:
  - Statistics cards
  - Filtering and search
  - Grouped matches display
  - CRUD operations
  - Tournament brackets view
  - Export/Import functionality

#### 2. MatchForm
- **Location**: `resources/js/components/admin/match-form.tsx`
- **Features**:
  - Comprehensive form with validation
  - Sport and schedule selection
  - Team and participant management
  - Date/time/venue management
  - Tournament bracket settings

#### 3. SportsScheduleForm
- **Location**: `resources/js/components/admin/sports-schedule-form.tsx`
- **Features**:
  - Schedule creation and editing
  - Sport selection
  - Date range management
  - Venue configuration

#### 4. TournamentBracket
- **Location**: `resources/js/components/admin/tournament-bracket.tsx`
- **Features**:
  - Visual bracket display
  - Round-by-round organization
  - Winner tracking
  - Match status indicators

#### 5. ExportImportMatches
- **Location**: `resources/js/components/admin/export-import-matches.tsx`
- **Features**:
  - Excel/CSV export
  - File import with validation
  - Template download
  - Filtering options

### Public Components

#### 1. JadualPertandingan (Public Page)
- **Location**: `resources/js/pages/public/jadual-pertandingan.tsx`
- **Features**:
  - Hero section with background
  - Information cards
  - Live updates section

#### 2. JadualPertandinganPublic
- **Location**: `resources/js/components/public/jadual-pertandingan-public.tsx`
- **Features**:
  - Schedule cards view
  - Matches list view
  - Filtering and search
  - Responsive design

#### 3. LiveMatchesWidget
- **Location**: `resources/js/components/public/live-matches-widget.tsx`
- **Features**:
  - Statistics overview
  - Live matches display
  - Upcoming matches
  - Auto-refresh functionality

## Models and Relationships

### SportsSchedule Model
```php
// Relationships
public function sport() // belongsTo Sport
public function matches() // hasMany SportMatch
public function createdBy() // belongsTo User

// Scopes
public function scopeActive($query)
public function scopeOrdered($query)
```

### SportMatch Model
```php
// Relationships
public function sport() // belongsTo Sport
public function sportsSchedule() // belongsTo SportsSchedule

// Scopes
public function scopeOrderedByDateTime($query)
public function scopeOrderedByBil($query)
public function scopeLive($query)
public function scopeUpcoming($query)
```

## Routes

### Public Routes
```
/jadual-pertandingan - Public jadual pertandingan page
```

### Admin Routes
```
/dashboard/admin/jadual-pertandingan - Admin jadual pertandingan management
```

## Navigation Integration

### Public Header
- Link "Jadual Pertandingan" ditambah ke navigation menu
- Accessible dari semua halaman public

### Admin Sidebar
- Menu "Jadual Pertandingan" untuk semua roles (superadmin, admin, zone)
- Icon: Calendar

## Data Seeding

### SportsSchedulesSeeder
- 6 jadual sukan dengan data realistik
- Merangkumi semua jenis sukan utama
- Tarikh dan venue yang sesuai

### SportMatchesSeeder
- 17 perlawanan dengan data lengkap
- Merangkumi pelbagai jenis perlawanan
- Tournament bracket data
- Status dan kategori yang bervariasi

## Testing Checklist

### Admin Functionality
- [x] Create sports schedule
- [x] Edit sports schedule
- [x] Delete sports schedule
- [x] Create match
- [x] Edit match
- [x] Delete match
- [x] View tournament brackets
- [x] Export matches
- [x] Import matches
- [x] Filtering and search

### Public Functionality
- [x] View sports schedules
- [x] View matches list
- [x] Filter matches
- [x] View live matches
- [x] View upcoming matches
- [x] Responsive design

### API Functionality
- [x] All admin endpoints working
- [x] All public endpoints working
- [x] Proper authentication
- [x] Data validation
- [x] Error handling

## Future Enhancements

1. **Real-time Updates**: WebSocket integration untuk live scores
2. **Mobile App**: React Native app untuk mobile access
3. **Push Notifications**: Notifikasi untuk perlawanan penting
4. **Advanced Analytics**: Statistik mendalam dan reporting
5. **Social Integration**: Share ke social media
6. **Live Streaming**: Integration dengan platform streaming

## Conclusion

Sistem Jadual Pertandingan telah berjaya dibangunkan dengan lengkap mengikut keperluan Portal Sukan Intra KKD 2025. Sistem ini menyediakan pengurusan yang komprehensif untuk jadual pertandingan dengan sokongan untuk tournament brackets, export/import, dan paparan awam yang menarik.
