# Skema Pemarkahan Sukan Intra Kor Kesihatan DiRaja 2025

## 📋 Ringkasan Sistem Pemarkahan

Portal Sukan menggunakan sistem pemarkahan yang berbeza untuk setiap jenis sukan berdasarkan format pertandingan sebenar.

---

## 🏃‍♂️ **1. LARI-LARI BERGANTI-GANTI 10 x 400 METER**

### Format Pertandingan
- **Peserta:** SEMUA ZON terlibat
- **Format:** Sistem knockout - pu<PERSON><PERSON>, separuh akhir, akhir
- **Kategori:** Berdasarkan pangkat (10 pelari per zon)
- **Susunan:** Pegawai Kanan → PTT Kanan → PTT Rendah → Waran → PTT Rendah → Terbuka → PTT Rendah → PTT Kanan → PTT Rendah → Pegawai Kanan

### Sistem Pemarkahan
**<PERSON><PERSON><PERSON> & Separuh Akhir:**
```
Layak ke pusingan seterusnya: 3 mata
Tidak layak: 0 mata
```

**Pusing<PERSON> Akhir:**
```
Kedudukan 1: 10 mata
Kedudukan 2: 8 mata
Kedudukan 3: 6 mata
Kedudukan 4: 4 mata
Kedudukan 5: 2 mata
Kedudukan 6: 1 mata
```

### Contoh Keputusan
```
Pusingan Akhir:
Zon 1: 04:25.30 (10 mata) - JUARA
Zon 2: 04:28.15 (8 mata)
Zon 3: 04:30.45 (6 mata)
Zon 4: 04:32.10 (4 mata)

Pusingan Saringan (tidak layak):
Zon 5: 04:45.20 (3 mata)
Zon 6: 04:50.10 (3 mata)
```

---

## ⚽ **2. BOLA SEPAK**

### Format Pertandingan
- **Peserta:** 2 zon (head-to-head)
- **Format:** Goal-based dengan point system
- **Durasi:** 2 x 45 minit

### Sistem Pemarkahan
```
Menang: 3 mata
Seri: 1 mata
Kalah: 0 mata
```

### Contoh Keputusan
```
Zon 1 vs Zon 2
Skor: 2-1
Zon 1: 3 mata (MENANG)
Zon 2: 0 mata
```

---

## 🏐 **3. BOLA TAMPAR**

### Format Pertandingan
- **Peserta:** 2 zon (head-to-head)
- **Format:** Best of 3 sets (25-25-15)
- **Pemenang:** First to win 2 sets

### Sistem Pemarkahan
```
Menang 2-0: 3 mata
Menang 2-1: 2 mata
Kalah 1-2: 1 mata
Kalah 0-2: 0 mata
```

### Contoh Keputusan
```
Zon 1 vs Zon 2
Set 1: 25-23 (Zon 1)
Set 2: 22-25 (Zon 2)
Set 3: 15-12 (Zon 1)
Hasil: Zon 1 menang 2-1
Zon 1: 2 mata
Zon 2: 1 mata
```

---

## 🏸 **4. BADMINTON**

### Format Pertandingan
- **Peserta:** 2 zon (head-to-head)
- **Format:** 5 kategori (MS, WS, MD, WD, XD)
- **Pemenang:** Kiraan mata tertinggi dari semua 5 kategori
- **Sistem:** Setiap kategori dimainkan berasingan

### Sistem Pemarkahan
```
Menang 5-0 kategori: 3 mata
Menang 4-1 kategori: 3 mata
Menang 3-2 kategori: 2 mata
Kalah 2-3 kategori: 1 mata
Kalah 1-4 kategori: 0 mata
Kalah 0-5 kategori: 0 mata
```

### Contoh Keputusan
```
Zon 1 vs Zon 2
MS: Zon 1 menang (1-0)
WS: Zon 2 menang (1-1)
MD: Zon 1 menang (2-1)
WD: Zon 1 menang (3-1)
XD: Zon 2 menang (3-2)
Hasil: Zon 1 menang 3-2 kategori
Zon 1: 2 mata
Zon 2: 1 mata
```

---

## 🏓 **5. PING PONG**

### Format Pertandingan
- **Peserta:** 2 zon (head-to-head)
- **Format:** 5 kategori (MS, WS, MD, WD, XD)
- **Pemenang:** Kiraan mata tertinggi dari semua 5 kategori
- **Sistem:** Setiap kategori dimainkan berasingan

### Sistem Pemarkahan
```
Menang 5-0 kategori: 3 mata
Menang 4-1 kategori: 3 mata
Menang 3-2 kategori: 2 mata
Kalah 2-3 kategori: 1 mata
Kalah 1-4 kategori: 0 mata
Kalah 0-5 kategori: 0 mata
```

### Contoh Keputusan
```
Zon 1 vs Zon 2
MS: Zon 1 menang (1-0)
WS: Zon 1 menang (2-0)
MD: Zon 2 menang (2-1)
WD: Zon 1 menang (3-1)
XD: Zon 2 menang (3-2)
Hasil: Zon 1 menang 3-2 kategori
Zon 1: 2 mata
Zon 2: 1 mata
```

---

## 🥅 **6. BOLA JARING**

### Format Pertandingan
- **Peserta:** 2 zon (head-to-head)
- **Format:** Goal-based dengan point system
- **Durasi:** 4 x 15 minit

### Sistem Pemarkahan
```
Menang: 3 mata
Seri: 1 mata
Kalah: 0 mata
```

### Contoh Keputusan
```
Zon 1 vs Zon 2
Skor: 35-28
Zon 1: 3 mata (MENANG)
Zon 2: 0 mata
```

---

## 🎭 **7. SUKAN RAKYAT**

### Format Pertandingan
- **Peserta:** SEMUA ZON terlibat
- **Format:** Multiple traditional games
- **Pemenang:** Zon dengan mata tertinggi

### Sistem Pemarkahan
```
Kedudukan 1: 10 mata
Kedudukan 2: 8 mata
Kedudukan 3: 6 mata
Kedudukan 4: 4 mata
Kedudukan 5: 2 mata
Kedudukan 6: 1 mata
```

### Contoh Keputusan
```
Zon 1: 150 mata (10 mata) - JUARA
Zon 2: 145 mata (8 mata)
Zon 3: 140 mata (6 mata)
Zon 4: 135 mata (4 mata)
```

---

## 📊 **JADUAL MATA KESELURUHAN**

### Sistem Ranking Akhir
```
Jumlah mata dari semua sukan:
- Lari-lari: 10/8/6/4/2/1 mata
- Bola Sepak: 3/1/0 mata
- Bola Tampar: 3/2/1/0 mata
- Badminton: 3/2/1/0 mata
- Ping Pong: 3/2/1/0 mata
- Bola Jaring: 3/1/0 mata
- Sukan Rakyat: 10/8/6/4/2/1 mata
```

### Contoh Jadual Akhir
```
Zon 1: 25 mata (JUARA KESELURUHAN)
Zon 2: 22 mata
Zon 3: 18 mata
Zon 4: 15 mata
```

---

## ⚠️ **KES KHAS**

### Walk Over (WO)
- Zon yang tidak hadir: 0 mata
- Zon yang hadir: Mata penuh (3 mata untuk head-to-head, 10 mata untuk group)

### Diskualifikasi
- Zon yang diskualifikasi: 0 mata
- Zon lawan: Mata penuh

### Seri/Draw
- Kedua-dua zon: 1 mata (untuk head-to-head sports)

---

## 🎯 **NOTA PENTING**

1. **Lari-lari & Sukan Rakyat:** Semua zon terlibat, ranking system
2. **Sports lain:** Head-to-head, point system
3. **Mata keseluruhan:** Jumlah dari semua sukan
4. **Pemenang:** Zon dengan mata tertinggi keseluruhan

---

## 💻 **IMPLEMENTATION DALAM SISTEM**

### Database Schema
```sql
-- Jadual untuk rekod keputusan
match_results:
- match_id
- zone_a / zone_b (untuk head-to-head)
- all_zones (untuk group events)
- sport_type
- final_score_a / final_score_b
- points_awarded_a / points_awarded_b
- ranking_position (untuk group events)
- status (completed/ongoing/scheduled)
```

### Frontend Display
```javascript
// Untuk Head-to-Head Sports
"Zon 1: 3 mata (Menang 2-1)"
"Zon 2: 0 mata"

// Untuk Group Sports
"Zon 1: 10 mata (Kedudukan 1 - 04:25.30)"
"Zon 2: 8 mata (Kedudukan 2 - 04:28.15)"
```

### Calculation Logic
```javascript
// Head-to-head: Winner gets points based on margin
// Group events: Points based on final ranking
// Overall: Sum of all sport points
```

---

## 📝 **CONTOH LENGKAP SATU PERTANDINGAN**

### Scenario: Zon 1 vs Zon 2 - Badminton

**Input dalam sistem:**
- Sport: Badminton
- Zon A: 1, Zon B: 2
- Categories won: Zon 1 = 3, Zon 2 = 2
- Final result: Zon 1 menang 3-2

**Output sistem:**
```
Zon 1: 2 mata (Menang 3-2 kategori)
Zon 2: 1 mata (Kalah 2-3 kategori)
```

**Database record:**
```sql
INSERT INTO match_results VALUES (
  match_id: 'BDM001',
  zone_a: 1,
  zone_b: 2,
  sport_type: 'Badminton',
  final_score_a: 3,
  final_score_b: 2,
  points_awarded_a: 2,
  points_awarded_b: 1,
  status: 'completed'
);
```

---

## 🔄 **NEXT STEPS**

1. **Update match-result-modal.tsx** dengan skema yang betul
2. **Implement group event handling** untuk Lari-lari & Sukan Rakyat
3. **Update database schema** untuk support ranking
4. **Create leaderboard calculation** untuk overall standings
5. **Add validation** untuk ensure correct point allocation
