# System Requirements & Specifications - Portal Sukan Intra KKD

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Hardware Requirements](#hardware-requirements)
3. [Software Requirements](#software-requirements)
4. [Network Requirements](#network-requirements)
5. [Browser Compatibility](#browser-compatibility)
6. [Security Requirements](#security-requirements)
7. [Performance Specifications](#performance-specifications)
8. [Scalability Requirements](#scalability-requirements)
9. [Backup & Recovery](#backup--recovery)
10. [Compliance & Standards](#compliance--standards)

---

## 🎯 System Overview

### Application Type
- **Architecture:** Single Page Application (SPA) with RESTful API
- **Frontend:** React 19 + TypeScript
- **Backend:** Laravel 12 PHP Framework
- **Database:** MySQL 8.0+
- **Authentication:** Token-based (Laravel Sanctum)
- **Deployment:** Web-based application for cPanel hosting

### Target Users
- **Primary Users:** 50-100 concurrent users
- **Peak Usage:** 200-300 concurrent users during tournament
- **User Roles:** Superadmin, Admin, Zone users
- **Geographic Distribution:** Malaysia (military bases)

### Core Functionality
- Player registration and management
- Tournament scheduling and results
- Dynamic content management (CMS)
- Real-time statistics and reporting
- Multi-zone management system

---

## 💻 Hardware Requirements

### Server Requirements (Production)

#### Minimum Requirements
- **CPU:** 2 vCPU cores (2.4 GHz)
- **RAM:** 4 GB
- **Storage:** 50 GB SSD
- **Bandwidth:** 100 Mbps
- **Operating System:** Linux (Ubuntu 20.04+ / CentOS 8+)

#### Recommended Requirements
- **CPU:** 4 vCPU cores (2.8 GHz)
- **RAM:** 8 GB
- **Storage:** 100 GB SSD
- **Bandwidth:** 1 Gbps
- **Operating System:** Linux (Ubuntu 22.04 LTS)

#### High-Load Requirements (Peak Tournament)
- **CPU:** 8 vCPU cores (3.2 GHz)
- **RAM:** 16 GB
- **Storage:** 200 GB SSD
- **Bandwidth:** 1 Gbps with CDN
- **Load Balancer:** Recommended for 500+ users

### Development Environment

#### Minimum Local Development
- **CPU:** Intel i5 / AMD Ryzen 5 (4 cores)
- **RAM:** 8 GB
- **Storage:** 20 GB free space
- **Operating System:** Windows 10/11, macOS 10.15+, Linux

#### Recommended Local Development
- **CPU:** Intel i7 / AMD Ryzen 7 (8 cores)
- **RAM:** 16 GB
- **Storage:** 50 GB SSD free space
- **Operating System:** Latest stable versions

### Client Requirements (End Users)

#### Minimum Client Specifications
- **Device:** Desktop, Laptop, Tablet, Smartphone
- **RAM:** 2 GB (mobile), 4 GB (desktop)
- **Storage:** 1 GB free space for browser cache
- **Internet:** 1 Mbps stable connection
- **Screen Resolution:** 320px width minimum

#### Recommended Client Specifications
- **Device:** Modern desktop/laptop/tablet
- **RAM:** 4 GB (mobile), 8 GB (desktop)
- **Storage:** 2 GB free space
- **Internet:** 5 Mbps stable connection
- **Screen Resolution:** 1024px width or higher

---

## 🛠️ Software Requirements

### Server Software Stack

#### Web Server
- **Primary:** Apache 2.4+ or Nginx 1.18+
- **PHP:** 8.2+ with required extensions
- **SSL/TLS:** Certificate required for production
- **HTTP/2:** Recommended for performance

#### Database
- **Primary:** MySQL 8.0+
- **Alternative:** MariaDB 10.6+
- **Storage Engine:** InnoDB
- **Character Set:** utf8mb4
- **Collation:** utf8mb4_unicode_ci

#### PHP Extensions (Required)
```
- BCMath
- Ctype
- cURL
- DOM
- Fileinfo
- Filter
- Hash
- Mbstring
- OpenSSL
- PCRE
- PDO
- Session
- Tokenizer
- XML
- JSON
- GD or Imagick (for image processing)
- Zip
```

#### PHP Configuration
```ini
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 10M
post_max_size = 10M
max_file_uploads = 20
```

### Development Tools

#### Required Tools
- **Composer:** 2.5+ (PHP dependency manager)
- **Node.js:** 18+ LTS
- **NPM:** 9+ or Yarn 1.22+
- **Git:** 2.30+ (version control)

#### Recommended Tools
- **PHP:** 8.3 (latest stable)
- **MySQL:** 8.0.35+ (latest stable)
- **Redis:** 7.0+ (caching, optional)
- **Supervisor:** Process monitoring (production)

### Frontend Build Tools
- **Vite:** 6.0+ (build tool)
- **TypeScript:** 5.7+
- **ESLint:** 9.17+ (code linting)
- **Prettier:** 3.4+ (code formatting)

---

## 🌐 Network Requirements

### Bandwidth Requirements

#### Per User Concurrent Usage
- **Light Usage:** 50 KB/s (browsing, viewing)
- **Medium Usage:** 200 KB/s (form submissions, uploads)
- **Heavy Usage:** 500 KB/s (image uploads, reports)

#### Server Bandwidth Calculation
```
Concurrent Users × Usage Type = Required Bandwidth

Examples:
- 50 users (light) = 50 × 50 KB/s = 2.5 MB/s
- 100 users (medium) = 100 × 200 KB/s = 20 MB/s
- 200 users (mixed) = 200 × 300 KB/s = 60 MB/s
```

### Network Infrastructure

#### Internet Connection
- **Minimum:** 10 Mbps download, 5 Mbps upload
- **Recommended:** 100 Mbps download, 50 Mbps upload
- **Redundancy:** Backup internet connection recommended
- **Latency:** <100ms to target users

#### Internal Network
- **LAN Speed:** 1 Gbps minimum
- **WiFi:** 802.11n (2.4/5 GHz) minimum
- **Firewall:** Required with proper port configuration
- **Load Balancing:** Recommended for high availability

### Port Configuration

#### Required Ports (Inbound)
```
Port 80 (HTTP) - Redirect to HTTPS
Port 443 (HTTPS) - Main application
Port 22 (SSH) - Server administration
Port 3306 (MySQL) - Database (internal only)
```

#### Optional Ports
```
Port 6379 (Redis) - Caching (if used)
Port 25/587 (SMTP) - Email notifications
Port 21/22 (FTP/SFTP) - File transfers
```

---

## 🌍 Browser Compatibility

### Supported Browsers

#### Desktop Browsers (Minimum Versions)
- **Google Chrome:** 90+
- **Mozilla Firefox:** 88+
- **Microsoft Edge:** 90+
- **Safari:** 14+
- **Opera:** 76+

#### Mobile Browsers
- **Chrome Mobile:** 90+
- **Safari Mobile:** 14+
- **Firefox Mobile:** 88+
- **Samsung Internet:** 14+

#### Recommended Browsers
- **Google Chrome:** Latest stable version
- **Mozilla Firefox:** Latest stable version
- **Microsoft Edge:** Latest stable version

### Browser Features Required
- **JavaScript:** ES2020+ support
- **CSS:** Grid and Flexbox support
- **HTML5:** Full support
- **WebAPI:** Fetch, LocalStorage, SessionStorage
- **Security:** TLS 1.2+ support

### Unsupported Browsers
- Internet Explorer (all versions)
- Chrome < 90
- Firefox < 88
- Safari < 14
- Opera < 76

---

## 🔒 Security Requirements

### Authentication & Authorization

#### Password Policy
- **Minimum Length:** 8 characters
- **Complexity:** Letters, numbers, special characters
- **Expiry:** 90 days (configurable)
- **History:** Cannot reuse last 5 passwords
- **Lockout:** 5 failed attempts = 15 minutes lockout

#### Session Management
- **Token Expiry:** 2 hours (configurable)
- **Refresh Token:** 24 hours
- **Concurrent Sessions:** 3 maximum per user
- **Secure Cookies:** HTTPOnly, Secure, SameSite

### Data Protection

#### Encryption
- **Data in Transit:** TLS 1.2+ (HTTPS)
- **Data at Rest:** Database encryption recommended
- **Password Storage:** Bcrypt hashing
- **API Tokens:** Secure random generation

#### File Upload Security
- **File Types:** Whitelist approach
- **File Size:** Maximum 10MB per file
- **Virus Scanning:** Recommended
- **Storage:** Outside web root
- **Access Control:** Authenticated access only

### Network Security

#### SSL/TLS Configuration
```
Minimum TLS Version: 1.2
Cipher Suites: Strong encryption only
HSTS: Enabled
Certificate: Valid SSL certificate required
```

#### Firewall Rules
- Block all unnecessary ports
- Allow only required IP ranges
- DDoS protection recommended
- Rate limiting on API endpoints

### Compliance Requirements

#### Data Privacy
- **Personal Data:** Minimal collection
- **Data Retention:** 7 years (configurable)
- **Data Access:** Role-based access control
- **Data Export:** User data export capability
- **Data Deletion:** Secure data deletion

#### Audit Requirements
- **Access Logs:** All user actions logged
- **System Logs:** Application and system events
- **Log Retention:** 1 year minimum
- **Log Security:** Tamper-proof logging

---

## ⚡ Performance Specifications

### Response Time Requirements

#### Page Load Times
- **Homepage:** < 2 seconds
- **Dashboard:** < 3 seconds
- **Data Tables:** < 2 seconds
- **Form Submissions:** < 1 second
- **File Uploads:** < 5 seconds (per MB)

#### API Response Times
- **Authentication:** < 500ms
- **Data Retrieval:** < 1 second
- **Data Updates:** < 500ms
- **File Operations:** < 2 seconds
- **Reports:** < 5 seconds

### Throughput Requirements

#### Concurrent Users
- **Normal Load:** 50 concurrent users
- **Peak Load:** 200 concurrent users
- **Maximum Load:** 500 concurrent users (with scaling)

#### Database Performance
- **Query Response:** < 100ms (95th percentile)
- **Connection Pool:** 20-50 connections
- **Index Usage:** All queries must use indexes
- **Slow Query Log:** Queries > 1 second logged

### Resource Utilization

#### Server Resources (Normal Load)
- **CPU Usage:** < 70%
- **Memory Usage:** < 80%
- **Disk I/O:** < 80%
- **Network I/O:** < 70%

#### Database Resources
- **CPU Usage:** < 60%
- **Memory Usage:** < 75%
- **Connection Usage:** < 80% of max connections
- **Storage Growth:** < 1GB per month

---

## 📈 Scalability Requirements

### Horizontal Scaling

#### Load Balancing
- **Web Servers:** 2+ servers behind load balancer
- **Database:** Master-slave replication
- **File Storage:** Shared storage or CDN
- **Session Storage:** Redis cluster

#### Auto-Scaling Triggers
- **CPU Usage:** > 80% for 5 minutes
- **Memory Usage:** > 85% for 5 minutes
- **Response Time:** > 3 seconds average
- **Error Rate:** > 5% for 2 minutes

### Vertical Scaling

#### Resource Scaling Paths
```
Tier 1: 2 vCPU, 4GB RAM, 50GB SSD
Tier 2: 4 vCPU, 8GB RAM, 100GB SSD
Tier 3: 8 vCPU, 16GB RAM, 200GB SSD
Tier 4: 16 vCPU, 32GB RAM, 500GB SSD
```

#### Database Scaling
- **Read Replicas:** For read-heavy operations
- **Partitioning:** By zone or date ranges
- **Indexing:** Optimized for query patterns
- **Caching:** Redis for frequently accessed data

### Performance Monitoring

#### Key Metrics
- **Response Time:** 95th percentile < 2 seconds
- **Availability:** 99.9% uptime
- **Error Rate:** < 0.1%
- **Throughput:** Requests per second
- **Resource Usage:** CPU, Memory, Disk, Network

#### Monitoring Tools
- **Application:** Laravel Telescope (development)
- **Infrastructure:** Server monitoring tools
- **Database:** MySQL performance schema
- **Logs:** Centralized logging system

---

## 💾 Backup & Recovery

### Backup Strategy

#### Database Backup
- **Frequency:** Daily full backup, hourly incremental
- **Retention:** 30 days online, 1 year archive
- **Location:** Off-site storage required
- **Encryption:** Encrypted backups
- **Testing:** Monthly restore testing

#### File Backup
- **Frequency:** Daily backup of uploaded files
- **Retention:** 90 days online, 1 year archive
- **Location:** Separate from database backups
- **Compression:** Compressed archives
- **Verification:** Backup integrity checks

#### Application Backup
- **Code Repository:** Git-based version control
- **Configuration:** Environment files backed up
- **Dependencies:** Package lock files versioned
- **Documentation:** Version-controlled documentation

### Disaster Recovery

#### Recovery Time Objectives (RTO)
- **Critical Systems:** 4 hours
- **Non-Critical Systems:** 24 hours
- **Data Recovery:** 2 hours
- **Full System Recovery:** 8 hours

#### Recovery Point Objectives (RPO)
- **Database:** 1 hour maximum data loss
- **Files:** 24 hours maximum data loss
- **Configuration:** No data loss (version controlled)

#### Recovery Procedures
1. **Assessment:** Determine scope of failure
2. **Communication:** Notify stakeholders
3. **Recovery:** Execute recovery procedures
4. **Testing:** Verify system functionality
5. **Documentation:** Document lessons learned

---

## 📋 Compliance & Standards

### Technical Standards

#### Web Standards
- **HTML:** HTML5 compliant
- **CSS:** CSS3 with vendor prefixes
- **JavaScript:** ES2020+ standards
- **Accessibility:** WCAG 2.1 AA compliance
- **SEO:** Search engine optimization

#### API Standards
- **REST:** RESTful API design
- **HTTP:** Proper HTTP status codes
- **JSON:** Consistent JSON responses
- **Versioning:** API versioning strategy
- **Documentation:** OpenAPI/Swagger documentation

### Security Standards

#### Industry Standards
- **OWASP:** Top 10 security risks addressed
- **ISO 27001:** Information security management
- **NIST:** Cybersecurity framework
- **PCI DSS:** If handling payment data

#### Government Compliance
- **MAMPU:** Malaysian government IT standards
- **PDPA:** Personal Data Protection Act compliance
- **Cybersecurity:** National cybersecurity guidelines

### Quality Standards

#### Code Quality
- **PSR-12:** PHP coding standards
- **ESLint:** JavaScript code quality
- **SonarQube:** Code quality analysis
- **Unit Testing:** 80%+ code coverage

#### Documentation Standards
- **API Documentation:** Complete and up-to-date
- **User Documentation:** Comprehensive user guides
- **Technical Documentation:** System architecture
- **Process Documentation:** Deployment and maintenance

---

**Portal Sukan Intra Kor Kesihatan DiRaja 2025 - System Requirements**  
Last Updated: July 2025  
Version: 1.0.0
