# Manual Pengguna - Portal Sukan Intra KKD

## 📋 Kandungan

1. [Pengenalan](#pengenalan)
2. [<PERSON><PERSON><PERSON><PERSON>](#memulakan-sistem)
3. [Pengu<PERSON>an Pengguna](#pengurusan-pengguna)
4. [Pen<PERSON><PERSON><PERSON> Peserta](#pengurusan-peserta)
5. [Pengurus<PERSON>n](#pengurusan-perlawanan)
6. [Sistem CMS](#sistem-cms)
7. [<PERSON><PERSON><PERSON>](#tetapan-sistem)
8. [Laporan dan Statistik](#laporan-dan-statistik)
9. [Soalan Lazim](#soalan-lazim)

---

## 🎯 Pengenalan

### Tentang Portal Sukan Intra KKD
Portal Sukan Intra Kor Kesihatan DiRaja 2025 adalah sistem pengurusan tournament sukan yang direka khusus untuk anggota tentera. Sistem ini menyediakan platform lengkap untuk:

- Pendaftaran peserta mengikut zon
- Pengurusan jadual perlawanan
- Pengurusan content website secara dinamik
- Laporan dan statistik prestasi

### Peranan <PERSON>

#### 1. Superadmin
- **<PERSON><PERSON><PERSON>:** Semua fungsi sistem
- **Pengurusan Pengguna:** Tambah, edit, padam pengguna
- **Konfigurasi Sistem:** Tetapan am, logo, favicon
- **Pengurusan Content:** Semua content CMS

#### 2. Admin
- **Akses Terhad:** Fungsi pengurusan tertentu
- **Pengurusan Zon:** Zon yang ditetapkan sahaja
- **Pengurusan Content:** Content tertentu
- **Laporan:** Akses laporan

#### 3. Zone
- **Profil Zon:** Pengurusan profil zon sendiri
- **Peserta:** Pendaftaran peserta zon (jika ada ketua zon)
- **Data Zon:** Lihat data zon sendiri sahaja

---

## 🚀 Memulakan Sistem

### Log Masuk Pertama Kali

1. **Buka Portal**
   - Pergi ke alamat website portal
   - Klik butang "Log Masuk" di header

2. **Masukkan Kredential**
   - **Username:** Seperti yang diberikan oleh admin
   - **Kata Laluan:** `Abcd@1234` (kata laluan lalai)
   - Klik "Log Masuk"

3. **Tukar Kata Laluan**
   - Sistem akan memaksa anda menukar kata laluan
   - Masukkan kata laluan semasa: `Abcd@1234`
   - Masukkan kata laluan baru (minimum 8 aksara)
   - Sahkan kata laluan baru
   - Klik "Kemaskini Kata Laluan"

### Navigasi Dashboard

#### Header Dashboard
- **Logo Portal:** Klik untuk ke homepage
- **Menu Navigasi:** Akses cepat ke fungsi utama
- **Profil Pengguna:** Dropdown dengan pilihan logout

#### Sidebar Menu
- **Dashboard:** Halaman utama dengan statistik
- **Pengurusan Peserta:** Daftar dan urus peserta
- **Pengurusan Perlawanan:** Jadual dan keputusan
- **Profil Zon:** (Untuk pengguna zon sahaja)
- **Laporan:** Statistik dan laporan
- **Tetapan:** Konfigurasi sistem
- **CMS:** Pengurusan content website

---

## 👥 Pengurusan Pengguna

### Melihat Senarai Pengguna

1. **Akses Menu**
   - Klik "Pengurusan Pengguna" di sidebar
   - Atau pergi ke `/dashboard/admin/users`

2. **Fungsi Senarai**
   - **Carian:** Cari pengguna mengikut nama atau username
   - **Penapis:** Tapis mengikut peranan atau zon
   - **Pengurutan:** Susun mengikut nama, peranan, atau tarikh

### Menambah Pengguna Baru

1. **Klik Butang "Tambah Pengguna"**
2. **Isi Maklumat:**
   - **Nama Penuh:** Nama lengkap pengguna
   - **Username:** Nama pengguna untuk log masuk (unik)
   - **Email:** Alamat email (pilihan)
   - **Peranan:** Pilih superadmin/admin/zone
   - **Zon:** Pilih zon (untuk peranan zone)

3. **Simpan**
   - Klik "Simpan"
   - Pengguna akan menerima kata laluan sementara
   - Pengguna mesti tukar kata laluan pada log masuk pertama

### Mengedit Pengguna

1. **Cari Pengguna**
   - Gunakan carian atau navigasi senarai
   - Klik ikon "Edit" di sebelah nama pengguna

2. **Kemaskini Maklumat**
   - Edit maklumat yang diperlukan
   - Tidak boleh tukar username setelah dibuat
   - Klik "Kemaskini"

### Menyahaktifkan Pengguna

1. **Pilih Pengguna**
   - Cari pengguna yang ingin disahaktifkan
   - Klik ikon "Seting" atau "Edit"

2. **Tukar Status**
   - Set "Status" kepada "Tidak Aktif"
   - Klik "Kemaskini"
   - Pengguna tidak akan dapat log masuk

---

## 🏃‍♂️ Pengurusan Peserta

### Melihat Senarai Peserta

1. **Akses Menu Peserta**
   - Klik "Pengurusan Peserta" di sidebar
   - Dashboard akan menunjukkan senarai peserta

2. **Fungsi Carian dan Penapis**
   - **Carian:** Cari mengikut nama peserta
   - **Zon:** Tapis mengikut zon tertentu
   - **Sukan:** Tapis mengikut sukan
   - **Status:** Tapis aktif/tidak aktif

3. **Maklumat Ditunjukkan**
   - Nama peserta
   - No. telefon
   - Zon
   - Sukan yang disertai
   - Status pendaftaran
   - Tarikh pendaftaran

### Mendaftarkan Peserta Baru

#### Prasyarat
- **Ketua Zon:** Zon mesti mempunyai ketua sebelum boleh tambah peserta
- **Akses:** Pengguna mesti mempunyai akses ke zon tersebut

#### Langkah Pendaftaran

1. **Klik "Tambah Peserta"**
   - Butang akan disabled jika zon tiada ketua
   - Modal pendaftaran akan terbuka

2. **Isi Maklumat Peribadi**
   - **Nama Penuh:** Nama lengkap peserta
   - **No. Telefon:** Format Malaysia (contoh: 0123456789)
   - **Email:** Alamat email (pilihan)
   - **Zon:** Pilih zon (auto-selected untuk pengguna zon)

3. **Pilih Sukan**
   - Pilih satu atau lebih sukan
   - Gunakan dropdown untuk memilih
   - Sekurang-kurangnya satu sukan mesti dipilih

4. **Maklumat Ketua Pasukan**
   - **Nama Ketua:** Nama ketua pasukan
   - **No. Telefon Ketua:** No. telefon ketua pasukan

5. **Simpan Pendaftaran**
   - Klik "Daftar Peserta"
   - Sistem akan validate maklumat
   - Peserta akan ditambah ke senarai

### Mengedit Maklumat Peserta

1. **Pilih Peserta**
   - Cari peserta dalam senarai
   - Klik ikon "Edit" di sebelah nama

2. **Kemaskini Maklumat**
   - Edit maklumat yang diperlukan
   - Boleh tukar sukan yang disertai
   - Boleh tukar status (aktif/tidak aktif)

3. **Simpan Perubahan**
   - Klik "Kemaskini"
   - Perubahan akan disimpan

### Memadam Peserta

1. **Pilih Peserta**
   - Cari peserta yang ingin dipadam
   - Klik ikon "Padam"

2. **Sahkan Pemadaman**
   - Sistem akan minta pengesahan
   - Klik "Ya, Padam" untuk mengesahkan
   - Data peserta akan dipadam kekal

### Export Data Peserta

1. **Klik Butang "Export"**
   - Pilih format (Excel/PDF)
   - Pilih data yang ingin diexport

2. **Download Fail**
   - Sistem akan generate fail
   - Fail akan dimuat turun automatik

---

## 🏆 Pengurusan Perlawanan

### Melihat Jadual Perlawanan

1. **Akses Menu Perlawanan**
   - Klik "Pengurusan Perlawanan" di sidebar
   - Senarai perlawanan akan dipaparkan

2. **Penapis Perlawanan**
   - **Sukan:** Tapis mengikut jenis sukan
   - **Status:** Dijadualkan/Sedang Berlangsung/Selesai
   - **Tarikh:** Pilih tarikh tertentu
   - **Zon:** Tapis mengikut zon

3. **Maklumat Perlawanan**
   - Nama sukan dan kategori
   - Zon yang bertanding
   - Tarikh dan masa
   - Venue perlawanan
   - Status perlawanan
   - Skor (jika ada)

### Menjadualkan Perlawanan Baru

1. **Klik "Tambah Perlawanan"**
   - Modal jadual perlawanan akan terbuka

2. **Isi Maklumat Perlawanan**
   - **Sukan:** Pilih jenis sukan
   - **Tajuk:** Nama perlawanan (contoh: "Badminton Lelaki - Pusingan 1")
   - **Zon A:** Pilih zon pertama
   - **Zon B:** Pilih zon kedua
   - **Tarikh:** Pilih tarikh perlawanan
   - **Masa:** Set masa mula
   - **Venue:** Lokasi perlawanan

3. **Simpan Jadual**
   - Klik "Jadualkan Perlawanan"
   - Perlawanan akan ditambah ke senarai

### Mengemaskini Keputusan Perlawanan

1. **Pilih Perlawanan**
   - Cari perlawanan dalam senarai
   - Klik "Kemaskini Skor" atau ikon edit

2. **Masukkan Keputusan**
   - **Skor Zon A:** Mata yang diperolehi
   - **Skor Zon B:** Mata yang diperolehi
   - **Status:** Tukar kepada "Selesai"
   - **Pemenang:** Sistem akan auto-detect atau pilih manual
   - **Nota:** Tambah nota jika perlu

3. **Simpan Keputusan**
   - Klik "Kemaskini Keputusan"
   - Keputusan akan disimpan dan dipaparkan

### Membatalkan Perlawanan

1. **Pilih Perlawanan**
   - Cari perlawanan yang ingin dibatalkan
   - Klik ikon "Edit"

2. **Tukar Status**
   - Set status kepada "Dibatalkan"
   - Tambah nota sebab pembatalan
   - Klik "Kemaskini"

---

## 📝 Sistem CMS

### Pengenalan CMS
Sistem CMS membolehkan admin mengedit semua content website secara dinamik tanpa perlu coding. Content diorganize mengikut halaman dan section.

### Struktur Content

#### Halaman Homepage
- **Hero Section:** Tajuk utama, subtitle, description, countdown
- **Sports Section:** Senarai sukan dan maklumat
- **Statistics Section:** Statistik penyertaan
- **CTA Section:** Call-to-action untuk pendaftaran

#### Halaman Lain
- **Match Schedule:** Content untuk halaman jadual
- **Tournament Results:** Content untuk halaman keputusan
- **Gallery:** Content untuk galeri
- **News:** Content untuk berita
- **Rules:** Content untuk peraturan

### Mengedit Content Homepage

1. **Akses CMS**
   - Klik "CMS" di sidebar
   - Pilih "Page Content"
   - Pilih tab "Homepage"

2. **Edit Hero Section**
   - **Tajuk:** Edit tajuk utama (contoh: "SUKAN INTRA")
   - **Subtitle:** Edit subtitle (contoh: "KOR KESIHATAN DIRAJA 2025")
   - **Description:** Edit penerangan
   - **Countdown Text:** Edit teks countdown
   - **Background Image:** Upload gambar latar belakang

3. **Edit Sports Section**
   - **Tajuk:** Edit tajuk section sukan
   - **Subtitle:** Edit subtitle
   - **Sports Data:** Edit senarai sukan (format JSON)
   - **Background Image:** Upload gambar latar

4. **Edit Statistics Section**
   - **Tajuk:** Edit tajuk statistik
   - **Subtitle:** Edit subtitle
   - **Participants Count:** Edit jumlah peserta
   - **Sports Count:** Edit jumlah sukan
   - **Zones Count:** Edit jumlah zon
   - **Background Image:** Upload gambar latar

5. **Edit CTA Section**
   - **Tajuk:** Edit tajuk call-to-action
   - **Subtitle:** Edit subtitle
   - **Description:** Edit penerangan
   - **Background Image:** Upload gambar latar

### Menguruskan Gambar

#### Upload Gambar Baru
1. **Klik "Upload Image"**
   - Pilih section yang berkaitan
   - Pilih fail gambar (JPG, PNG, GIF)
   - Tambah description
   - Klik "Upload"

2. **Sistem Akan:**
   - Compress gambar automatik
   - Generate thumbnail
   - Simpan dalam folder yang sesuai
   - Return URL untuk digunakan

#### Menggantikan Gambar
1. **Pilih Content**
   - Cari content yang mempunyai gambar
   - Klik "Edit"

2. **Upload Gambar Baru**
   - Klik "Change Image"
   - Pilih gambar baru
   - Gambar lama akan digantikan

### Mengedit Content Halaman Lain

1. **Pilih Halaman**
   - Dalam CMS, pilih tab halaman yang ingin diedit
   - Contoh: "Match Schedule", "Gallery", "News"

2. **Edit Content**
   - Setiap halaman mempunyai structure yang berbeza
   - Edit mengikut field yang disediakan
   - Upload gambar jika perlu

3. **Preview Changes**
   - Klik "Preview" untuk lihat perubahan
   - Buka halaman public untuk verify
   - Klik "Save" untuk simpan

---

## ⚙️ Tetapan Sistem

### Tetapan Am

#### Maklumat Asas
1. **Akses Tetapan**
   - Klik "Tetapan" di sidebar
   - Pilih tab "General"

2. **Edit Maklumat**
   - **Nama Portal:** Nama rasmi portal
   - **Tagline:** Slogan atau tagline
   - **Description:** Penerangan portal
   - **Copyright:** Teks copyright untuk footer

#### Header Configuration
1. **Tajuk Header**
   - **Part 1:** "SUKAN" (warna merah)
   - **Part 2:** "INTRA" (warna biru)
   - **Subtitle:** "KOR KESIHATAN DIRAJA 2025" (warna kuning)

2. **Simpan Perubahan**
   - Klik "Kemaskini Tetapan"
   - Perubahan akan kelihatan di header

### Pengurusan Logo dan Favicon

#### Upload Logo
1. **Pilih Fail Logo**
   - Format: JPG, PNG, GIF
   - Saiz maksimum: 2MB
   - Resolusi disyorkan: 200x200px

2. **Upload dan Simpan**
   - Klik "Upload Logo"
   - Logo akan digunakan di header
   - Logo juga akan auto-generate sebagai favicon

#### Upload Favicon Berasingan
1. **Pilih Fail Favicon**
   - Format: ICO, PNG
   - Saiz maksimum: 1MB
   - Resolusi: 16x16px atau 32x32px

2. **Upload**
   - Klik "Upload Favicon"
   - Favicon akan gantikan yang auto-generated

### Tetapan Zon

#### Mengedit Profil Zon
1. **Akses Profil Zon** (untuk pengguna zon)
   - Klik "Profil Zon" di sidebar

2. **Edit Maklumat**
   - **Nama Zon:** Nama lengkap zon
   - **Description:** Penerangan zon
   - **Warna Utama:** Warna tema zon
   - **Warna Kedua:** Warna sekunder

3. **Upload Logo Zon**
   - Pilih logo khusus untuk zon
   - Logo akan dipaparkan dalam senarai zon

#### Menguruskan Ketua Zon
1. **Lantik Ketua Zon**
   - Pilih pengguna sebagai ketua
   - Masukkan nama penuh ketua
   - Masukkan no. telefon ketua

2. **Kemaskini Maklumat Ketua**
   - Edit nama atau no. telefon
   - Perubahan akan reflect dalam sidebar

---

## 📊 Laporan dan Statistik

### Dashboard Statistik

#### Statistik Utama
- **Jumlah Peserta:** Total peserta berdaftar
- **Jumlah Perlawanan:** Total perlawanan dijadualkan
- **Perlawanan Akan Datang:** Dalam minggu ini
- **Perlawanan Selesai:** Yang telah tamat

#### Statistik Mengikut Zon
- Jumlah peserta setiap zon
- Prestasi perlawanan
- Kedudukan dalam tournament

### Laporan Peserta

1. **Akses Laporan**
   - Klik "Laporan" di sidebar
   - Pilih "Laporan Peserta"

2. **Penapis Laporan**
   - **Zon:** Pilih zon tertentu atau semua
   - **Sukan:** Pilih sukan tertentu
   - **Status:** Aktif/tidak aktif
   - **Tarikh:** Range tarikh pendaftaran

3. **Export Laporan**
   - Pilih format (Excel/PDF)
   - Klik "Export"
   - Fail akan dimuat turun

### Laporan Perlawanan

1. **Statistik Perlawanan**
   - Total perlawanan mengikut sukan
   - Keputusan mengikut zon
   - Prestasi keseluruhan

2. **Jadual Perlawanan**
   - Senarai lengkap semua perlawanan
   - Status dan keputusan
   - Export ke kalendar

### Laporan Prestasi

1. **Kedudukan Zon**
   - Ranking keseluruhan
   - Mata terkumpul
   - Bilangan kemenangan/kekalahan

2. **Prestasi Mengikut Sukan**
   - Pemenang setiap kategori sukan
   - Statistik penyertaan
   - Analisis prestasi

---

## ❓ Soalan Lazim

### Masalah Log Masuk

**S: Saya lupa kata laluan, bagaimana?**
J: Hubungi admin sistem untuk reset kata laluan. Admin boleh set kata laluan baru untuk anda.

**S: Sistem kata "Username atau kata laluan tidak sah"**
J: 
- Pastikan username betul (case-sensitive)
- Pastikan kata laluan betul
- Pastikan akaun anda aktif
- Hubungi admin jika masalah berterusan

**S: Saya tidak boleh tukar kata laluan**
J:
- Pastikan kata laluan semasa betul
- Kata laluan baru mesti minimum 8 aksara
- Pastikan confirmation password sama
- Cuba refresh browser dan cuba lagi

### Masalah Pendaftaran Peserta

**S: Butang "Tambah Peserta" disabled**
J: Zon anda belum mempunyai ketua. Hubungi admin untuk lantik ketua zon terlebih dahulu.

**S: Error "Format no. telefon tidak sah"**
J: Gunakan format Malaysia yang betul:
- 0123456789 (betul)
- +60123456789 (betul)
- 123456789 (salah)

**S: Tidak boleh pilih sukan tertentu**
J: Sukan mungkin tidak aktif atau tidak tersedia untuk zon anda. Hubungi admin.

### Masalah Upload Gambar

**S: Gambar tidak boleh upload**
J:
- Pastikan saiz fail tidak melebihi had (2MB untuk logo, 1MB untuk favicon)
- Pastikan format betul (JPG, PNG, GIF untuk logo; ICO, PNG untuk favicon)
- Pastikan connection internet stabil
- Cuba compress gambar terlebih dahulu

**S: Gambar upload tapi tidak kelihatan**
J:
- Tunggu beberapa minit untuk processing
- Refresh browser (Ctrl+F5)
- Clear browser cache
- Pastikan gambar telah disimpan dalam CMS

### Masalah Prestasi

**S: Website lambat loading**
J:
- Check connection internet
- Clear browser cache
- Tutup tab browser yang tidak perlu
- Hubungi admin jika masalah berterusan

**S: Data tidak update**
J:
- Refresh halaman (F5)
- Clear browser cache
- Logout dan login semula
- Hubungi admin jika data masih tidak update

### Masalah Akses

**S: Saya tidak nampak menu tertentu**
J: Menu mungkin tidak tersedia untuk peranan anda. Setiap peranan mempunyai akses yang berbeza:
- Superadmin: Semua menu
- Admin: Menu terpilih
- Zone: Menu zon sahaja

**S: Error "Insufficient permissions"**
J: Anda tidak mempunyai kebenaran untuk akses fungsi tersebut. Hubungi admin untuk upgrade akses jika perlu.

### Mendapatkan Bantuan

#### Hubungi Support
- **Email:** <EMAIL>
- **Telefon:** 03-12345678
- **Waktu:** 8:00 AM - 5:00 PM (Isnin - Jumaat)

#### Maklumat Yang Perlu Disediakan
- Username anda
- Peranan anda dalam sistem
- Penerangan masalah yang dihadapi
- Screenshot error (jika ada)
- Browser yang digunakan
- Masa masalah berlaku

---

**Portal Sukan Intra Kor Kesihatan DiRaja 2025 - Manual Pengguna**  
Dikemaskini: Julai 2025  
Versi: 1.0.0
