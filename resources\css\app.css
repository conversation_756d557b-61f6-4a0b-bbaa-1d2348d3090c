@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom text shadow utilities */
@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.7), 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-xl {
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.8), 0 4px 8px rgba(0, 0, 0, 0.6);
  }

  /* Ultra strong text shadow for sport cards */
  .text-shadow-ultra {
    text-shadow:
      0 0 10px rgba(0, 0, 0, 1),
      0 0 20px rgba(0, 0, 0, 0.9),
      0 2px 4px rgba(0, 0, 0, 0.8),
      0 4px 8px rgba(0, 0, 0, 0.7),
      0 8px 16px rgba(0, 0, 0, 0.6),
      2px 2px 0px rgba(0, 0, 0, 1),
      -2px -2px 0px rgba(0, 0, 0, 1),
      2px -2px 0px rgba(0, 0, 0, 1),
      -2px 2px 0px rgba(0, 0, 0, 1);
  }

  /* White stroke text */
  .text-stroke-white {
    -webkit-text-stroke: 2px white;
    text-stroke: 2px white;
  }

  /* Enhanced shadow effects */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 25px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(59, 130, 246, 0.3);
  }

  .shadow-sport {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 10px 20px rgba(0, 0, 0, 0.2),
      0 0 30px rgba(251, 191, 36, 0.2);
  }

  /* Animated gradient border */
  .gradient-border-animated {
    background: linear-gradient(45deg, #fbbf24, #ef4444, #3b82f6, #10b981, #fbbf24);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Pulse effect */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }

  @keyframes pulseGlow {
    from {
      box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
    }
    to {
      box-shadow: 0 0 40px rgba(251, 191, 36, 0.6), 0 0 60px rgba(59, 130, 246, 0.3);
    }
  }
}

/* Custom Breakpoints untuk Portal Sukan Intra KKD - handled in tailwind.config.js */

/* Custom responsive breakpoints for xs (475px+) */
@media (min-width: 475px) {
  .xs\:text-xs { font-size: 0.75rem; line-height: 1rem; }
  .xs\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .xs\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .xs\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .xs\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .xs\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .xs\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

  .xs\:px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
  .xs\:px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
  .xs\:px-4 { padding-left: 1rem; padding-right: 1rem; }
  .xs\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

  .xs\:py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
  .xs\:py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
  .xs\:py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .xs\:py-2\.5 { padding-top: 0.625rem; padding-bottom: 0.625rem; }
  .xs\:py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

  .xs\:p-1 { padding: 0.25rem; }
  .xs\:p-1\.5 { padding: 0.375rem; }
  .xs\:p-3 { padding: 0.75rem; }
  .xs\:p-4 { padding: 1rem; }

  .xs\:mt-1 { margin-top: 0.25rem; }
  .xs\:mt-1\.5 { margin-top: 0.375rem; }
  .xs\:mt-3 { margin-top: 0.75rem; }
  .xs\:mt-4 { margin-top: 1rem; }
  .xs\:mt-6 { margin-top: 1.5rem; }
  .xs\:mb-1 { margin-bottom: 0.25rem; }
  .xs\:mb-3 { margin-bottom: 0.75rem; }
  .xs\:mb-4 { margin-bottom: 1rem; }
  .xs\:mb-8 { margin-bottom: 2rem; }
  .xs\:mr-2 { margin-right: 0.5rem; }
  .xs\:ml-2 { margin-left: 0.5rem; }

  .xs\:w-3 { width: 0.75rem; }
  .xs\:w-10 { width: 2.5rem; }
  .xs\:w-12 { width: 3rem; }
  .xs\:w-16 { width: 4rem; }
  .xs\:w-24 { width: 6rem; }

  .xs\:h-3 { height: 0.75rem; }
  .xs\:h-10 { height: 2.5rem; }
  .xs\:h-12 { height: 3rem; }
  .xs\:h-16 { height: 4rem; }
  .xs\:h-24 { height: 6rem; }

  .xs\:space-x-1\.5 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.375rem; }
  .xs\:space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.5rem; }
  .xs\:space-x-3 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.75rem; }
  .xs\:space-y-2 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.5rem; }
  .xs\:space-y-3 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; }

  .xs\:gap-1\.5 { gap: 0.375rem; }
  .xs\:gap-3 { gap: 0.75rem; }
  .xs\:gap-4 { gap: 1rem; }

  .xs\:rounded-lg { border-radius: 0.5rem; }
  .xs\:rounded-xl { border-radius: 0.75rem; }
  .xs\:rounded-2xl { border-radius: 1rem; }

  .xs\:flex-row { flex-direction: row; }

  .xs\:min-h-\[320px\] { min-height: 320px; }
  .xs\:max-w-sm { max-width: 24rem; }
}

/* Theme variables handled in tailwind.config.js and CSS variables below */

/* Tailwind CSS v3 - no need for border compatibility styles */

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    html {
        scroll-behavior: smooth;
    }
}

/* Sidebar improvements - using Tailwind responsive approach */
@layer components {
    /* Ensure sidebar icons are properly sized when collapsed */
    [data-sidebar="sidebar"][data-state="collapsed"] [data-sidebar="menu-button"] {
        @apply justify-center;
    }

    [data-sidebar="sidebar"][data-state="collapsed"] [data-sidebar="menu-button"] > span:last-child {
        @apply sr-only;
    }


}
