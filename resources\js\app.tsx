import '../css/app.css';

import { createRoot } from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { router } from './router/index';
import { initializeTheme } from './hooks/use-appearance';
import { AuthProvider } from './contexts/auth-context';
import { ZoneProvider } from './contexts/zone-context';
import { SettingsProvider } from './contexts/settings-context';

// Axios configuration is handled in lib/axios.ts

// Create a client for TanStack Query with optimized settings for image caching
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 1,
            refetchOnWindowFocus: false,
            staleTime: 5 * 60 * 1000, // 5 minutes default
            gcTime: 30 * 60 * 1000, // 30 minutes garbage collection
        },
    },
});

// Get the root element
const el = document.getElementById('root');
if (el) {
    const root = createRoot(el);

    root.render(
        <QueryClientProvider client={queryClient}>
            <AuthProvider>
                <ZoneProvider>
                    <SettingsProvider>
                        <RouterProvider router={router} />
                        <Toaster position="top-right" />
                    </SettingsProvider>
                </ZoneProvider>
            </AuthProvider>
        </QueryClientProvider>
    );
}

// This will set light / dark mode on load...
initializeTheme();
