import { useEffect } from 'react';
import { useSettings } from '@/contexts/settings-context';

interface DynamicFaviconProps {
    logoUrl?: string;
}

export default function DynamicFavicon({ logoUrl }: DynamicFaviconProps) {
    const { settings } = useSettings();

    const finalLogoUrl = logoUrl || settings?.logo_url;

    useEffect(() => {
        if (!finalLogoUrl) {
            return;
        }

        const faviconUrl = finalLogoUrl.startsWith('/storage/') ? finalLogoUrl : `/storage/${finalLogoUrl}`;

        // Function to update favicon
        const updateFavicon = () => {
            // Remove ALL existing favicon-related links
            const existingLinks = document.querySelectorAll('link[rel*="icon"]');
            existingLinks.forEach(link => {
                link.remove();
            });

            // Add cache busting timestamp
            const timestamp = new Date().getTime();
            const cacheBustedUrl = `${faviconUrl}?v=${timestamp}`;

            // Create multiple favicon formats for better browser support
            const faviconTypes = [
                { rel: 'icon', type: 'image/png', sizes: '32x32' },
                { rel: 'icon', type: 'image/png', sizes: '16x16' },
                { rel: 'shortcut icon', type: 'image/png' },
                { rel: 'apple-touch-icon', sizes: '180x180' },
                { rel: 'apple-touch-icon', sizes: '152x152' },
                { rel: 'apple-touch-icon', sizes: '120x120' },
                { rel: 'apple-touch-icon', sizes: '76x76' }
            ];

            faviconTypes.forEach(({ rel, type, sizes }) => {
                const link = document.createElement('link');
                link.rel = rel;
                if (type) link.type = type;
                if (sizes) link.setAttribute('sizes', sizes);
                link.href = cacheBustedUrl;
                document.head.appendChild(link);
            });
        };

        // Update favicon immediately
        updateFavicon();

        // Also try to force browser refresh by changing the URL slightly
        setTimeout(() => {
            const links = document.querySelectorAll('link[rel*="icon"]');
            links.forEach(link => {
                const newUrl = link.href.replace(/\?v=\d+/, '') + `?v=${Date.now()}`;
                link.href = newUrl;
            });
        }, 100);

    }, [finalLogoUrl]);

    return null;
}
