import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  lazy?: boolean;
  className?: string;
  containerClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
  enableCache?: boolean; // New prop to enable/disable caching
}

/**
 * Optimized image component with lazy loading, fallback, and error handling
 */
export default function OptimizedImage({
  src,
  alt,
  fallbackSrc = '/images/logo.png',
  lazy = true,
  className,
  containerClassName,
  onLoad,
  onError,
  enableCache = true,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Simple approach - just use the original src

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before image comes into view
        threshold: 0.1,
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Determine which source to use - simple approach
  const imageSrc = hasError ? fallbackSrc : src;

  return (
    <div 
      ref={containerRef}
      className={cn('relative overflow-hidden', containerClassName)}
    >
      {/* Loading placeholder */}
      {!isLoaded && isInView && (
        <div className={cn(
          'absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center',
          className
        )}>
          <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse" />
        </div>
      )}

      {/* Actual image */}
      {isInView && (
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy ? 'lazy' : 'eager'}
          {...props}
        />
      )}

      {/* Lazy loading placeholder */}
      {!isInView && (
        <div className={cn(
          'bg-gray-100 flex items-center justify-center',
          className
        )}>
          <div className="w-6 h-6 bg-gray-300 rounded animate-pulse" />
        </div>
      )}
    </div>
  );
}
