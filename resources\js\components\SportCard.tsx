import React from 'react';
import OptimizedImage from '@/components/OptimizedImage';
import { cn } from '@/lib/utils';

interface SportCardProps {
  sportName: string;
  sportIcon: string;
  className?: string;
  showTextOverlay?: boolean;
  textPosition?: 'bottom' | 'center' | 'top';
  textStyle?: 'simple' | 'gradient' | 'shadow';
  iconSize?: 'small' | 'medium' | 'large' | 'xlarge';
  enableCache?: boolean;
  lazy?: boolean;
}

/**
 * SportCard component with text overlay for sport names
 * Displays sport icon with customizable text overlay
 */
export default function SportCard({
  sportName,
  sportIcon,
  className,
  showTextOverlay = true,
  textPosition = 'bottom',
  textStyle = 'gradient',
  iconSize = 'medium',
  enableCache = true,
  lazy = true,
}: SportCardProps) {
  
  // Size configurations
  const sizeClasses = {
    small: 'w-12 h-12 sm:w-16 sm:h-16',
    medium: 'w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24',
    large: 'w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 lg:w-36 lg:h-36',
    xlarge: 'w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 xl:w-56 xl:h-56'
  };

  // Text position classes
  const textPositionClasses = {
    bottom: 'absolute bottom-0 left-0 right-0',
    center: 'absolute inset-0 flex items-center justify-center',
    top: 'absolute top-0 left-0 right-0'
  };

  // Text style classes with ultra contrast
  const textStyleClasses = {
    simple: 'text-white font-bold text-center text-shadow-ultra text-stroke-white',
    gradient: 'bg-gradient-to-r from-yellow-400 via-red-500 to-blue-500 bg-clip-text text-transparent font-black text-center text-shadow-ultra',
    shadow: 'text-white font-black text-center text-shadow-ultra'
  };

  return (
    <div className={cn('group cursor-pointer flex flex-col items-center space-y-3', className)}>
      {/* Gradient Border Container */}
      <div className={cn(
        'relative rounded-full gradient-border-animated p-1',
        'group-hover:scale-110 transition-all duration-500',
        'shadow-sport group-hover:pulse-glow',
        'aspect-square flex-shrink-0',
        sizeClasses[iconSize]
      )}>
        {/* Inner White Circle */}
        <div className="relative w-full h-full rounded-full bg-white overflow-hidden group-hover:bg-gray-50 transition-colors duration-300 aspect-square">
          {/* Perfect Center Container */}
          <div className="absolute inset-0 flex items-center justify-center p-4">
            {/* Sport Icon */}
            <OptimizedImage
              src={sportIcon}
              alt={sportName}
              className="w-full h-full object-contain group-hover:scale-110 transition-transform duration-500"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectPosition: 'center center'
              }}
              enableCache={enableCache}
              lazy={lazy}
              fallbackSrc="/images/logo.png"
            />
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full opacity-80 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-blue-500 rounded-full opacity-60 group-hover:opacity-90 transition-opacity duration-300"></div>
      </div>

      {/* Text Below Circle */}
      {showTextOverlay && (
        <div className="text-center w-full">
          <p className={cn(
            'text-white font-black text-center',
            'text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl 2xl:text-3xl',
            'px-2 py-1',
            'tracking-wide leading-tight',
            'transform group-hover:scale-105 transition-transform duration-300',
            'drop-shadow-2xl',
            'whitespace-nowrap'
          )}
          style={{
            textShadow: '3px 3px 6px rgba(0,0,0,0.9), -2px -2px 4px rgba(0,0,0,0.9), 2px -2px 4px rgba(0,0,0,0.9), -2px 2px 4px rgba(0,0,0,0.9), 0px 0px 8px rgba(0,0,0,0.8)'
          }}>
            {sportName}
          </p>
        </div>
      )}
    </div>
  );
}
