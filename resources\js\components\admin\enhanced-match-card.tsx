import { useState } from 'react';
import api from '@/lib/axios';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Trophy,
    Users,
    Calendar,
    Clock,
    MapPin,
    Play,
    Square,
    Edit,
    Eye,
    MoreVertical,
    CheckCircle,
    XCircle,
    Pause
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { SportLogo, ZoneLogo } from '@/components/ui/logo-components';

interface Match {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    notes?: string;
    bracket_round?: string;
    is_highlighted?: boolean;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
}

interface EnhancedMatchCardProps {
    match: Match;
    onSetResult: (match: Match) => void;
    onEdit: (match: Match) => void;
    onRefresh: () => void;
}

export default function EnhancedMatchCard({ match, onSetResult, onEdit, onRefresh }: EnhancedMatchCardProps) {
    const [isUpdating, setIsUpdating] = useState(false);

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            scheduled: { label: 'Dijadualkan', variant: 'secondary' as const, icon: Calendar },
            ongoing: { label: 'Sedang Berlangsung', variant: 'default' as const, icon: Play },
            completed: { label: 'Selesai', variant: 'default' as const, icon: CheckCircle },
            cancelled: { label: 'Dibatalkan', variant: 'destructive' as const, icon: XCircle },
        };

        const config = statusConfig[status as keyof typeof statusConfig];
        const Icon = config.icon;

        return (
            <Badge variant={config.variant} className="flex items-center gap-1">
                <Icon className="h-3 w-3" />
                {config.label}
            </Badge>
        );
    };

    const updateMatchStatus = async (newStatus: string) => {
        setIsUpdating(true);
        try {
            await api.put(`/admin/sport-matches/${match.id}`, {
                status: newStatus
            });
            toast.success(`Status perlawanan dikemaskini ke ${newStatus}`);
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Ralat mengemas kini status');
        } finally {
            setIsUpdating(false);
        }
    };

    const formatDateTime = (date: string, time: string) => {
        try {
            const matchDate = new Date(date);
            if (isNaN(matchDate.getTime())) {
                return {
                    date: 'Invalid Date',
                    time: String(time || 'N/A')
                };
            }

            const formattedDate = matchDate.toLocaleDateString('ms-MY', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });

            return {
                date: String(formattedDate || 'N/A'),
                time: String(time || 'N/A')
            };
        } catch (error) {
            return {
                date: 'Error formatting date',
                time: String(time || 'N/A')
            };
        }
    };

    const dateTime = formatDateTime(match.match_date, match.match_time);

    const getWinnerDisplay = () => {
        if (match.status !== 'completed' || !match.winner_zone) return null;

        if (match.winner_zone === 'draw') {
            return (
                <div className="text-center py-2">
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
                        SERI
                    </Badge>
                </div>
            );
        }

        const isZoneAWinner = match.winner_zone === match.zone_a;
        return (
            <div className="flex items-center justify-center gap-2 py-2">
                <Trophy className="h-4 w-4 text-yellow-500" />
                <span className="font-semibold text-green-700">
                    Zon {String(match.winner_zone || 'N/A')} Menang
                </span>
            </div>
        );
    };

    return (
        <Card className={`transition-all duration-200 hover:shadow-lg ${
            match.is_highlighted ? 'ring-2 ring-yellow-400 bg-yellow-50' : ''
        } ${match.status === 'ongoing' ? 'ring-2 ring-blue-400 bg-blue-50' : ''}`}>
            <CardContent className="p-4">
                <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                        <div className="space-y-1">
                            <div className="flex items-center gap-2">
                                <div className="flex items-center gap-2">
                                    <SportLogo
                                        sportName={match.sport?.name}
                                        logoUrl={match.sport?.logo_url}
                                        className="w-6 h-6"
                                    />
                                    <Badge variant="outline" className="bg-white">
                                        {String(match.sport?.name || 'Unknown Sport')}
                                    </Badge>
                                </div>
                                {match.bracket_round && (
                                    <Badge variant="secondary" className="text-xs">
                                        {String(match.bracket_round)}
                                    </Badge>
                                )}
                            </div>
                            <h3 className="font-semibold text-lg leading-tight">{String(match.title || 'No Title')}</h3>
                        </div>
                        
                        <div className="flex items-center gap-2">
                            {getStatusBadge(match.status)}
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <MoreVertical className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => onEdit(match)}>
                                        <Edit className="h-4 w-4 mr-2" />
                                        Edit
                                    </DropdownMenuItem>
                                    {match.status === 'scheduled' && (
                                        <DropdownMenuItem 
                                            onClick={() => updateMatchStatus('ongoing')}
                                            disabled={isUpdating}
                                        >
                                            <Play className="h-4 w-4 mr-2" />
                                            Mula Perlawanan
                                        </DropdownMenuItem>
                                    )}
                                    {match.status === 'ongoing' && (
                                        <DropdownMenuItem 
                                            onClick={() => updateMatchStatus('scheduled')}
                                            disabled={isUpdating}
                                        >
                                            <Pause className="h-4 w-4 mr-2" />
                                            Jeda Perlawanan
                                        </DropdownMenuItem>
                                    )}
                                    {match.status !== 'cancelled' && (
                                        <DropdownMenuItem 
                                            onClick={() => updateMatchStatus('cancelled')}
                                            disabled={isUpdating}
                                            className="text-red-600"
                                        >
                                            <XCircle className="h-4 w-4 mr-2" />
                                            Batal Perlawanan
                                        </DropdownMenuItem>
                                    )}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>

                    {/* Teams and Score */}
                    <div className="space-y-3">
                        <div className="grid grid-cols-3 items-center gap-4">
                            {/* Zone A */}
                            <div className="text-center">
                                <div className="p-3 mb-2">
                                    <ZoneLogo zoneCode={match.zone_a} className="w-12 h-12 mx-auto" />
                                </div>
                                {match.status === 'completed' && match.winner_zone === match.zone_a && (
                                    <div className="text-xs font-semibold text-green-700 mb-1">
                                        Menang
                                    </div>
                                )}
                                {match.status === 'completed' && (
                                    <div className="text-2xl font-bold text-blue-600">
                                        {String(match.score_zone_a || 0)}
                                    </div>
                                )}
                            </div>

                            {/* VS */}
                            <div className="text-center">
                                <div className="text-gray-400 font-semibold">VS</div>
                            </div>

                            {/* Zone B */}
                            <div className="text-center">
                                <div className="p-3 mb-2">
                                    <ZoneLogo zoneCode={match.zone_b} className="w-12 h-12 mx-auto" />
                                </div>
                                {match.status === 'completed' && match.winner_zone === match.zone_b && (
                                    <div className="text-xs font-semibold text-green-700 mb-1">
                                        Menang
                                    </div>
                                )}
                                {match.status === 'completed' && (
                                    <div className="text-2xl font-bold text-red-600">
                                        {String(match.score_zone_b || 0)}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Match Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>{String(dateTime.date || 'N/A')}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            <span>{String(dateTime.time || 'N/A')}</span>
                        </div>
                        <div className="flex items-center gap-2 col-span-2">
                            <MapPin className="h-4 w-4" />
                            <span>{String(match.venue || 'N/A')}</span>
                        </div>
                    </div>

                    {/* Notes */}
                    {match.notes && (
                        <div className="bg-gray-50 rounded-lg p-3">
                            <p className="text-sm text-gray-700">{String(match.notes)}</p>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                        {match.status !== 'completed' && match.status !== 'cancelled' && (
                            <Button 
                                onClick={() => onSetResult(match)}
                                className="flex-1"
                                variant={match.status === 'ongoing' ? 'default' : 'outline'}
                            >
                                <Trophy className="h-4 w-4 mr-2" />
                                {match.status === 'ongoing' ? 'Set Keputusan' : 'Set Keputusan'}
                            </Button>
                        )}
                        
                        <Button variant="outline" size="sm" onClick={() => onEdit(match)}>
                            <Eye className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
