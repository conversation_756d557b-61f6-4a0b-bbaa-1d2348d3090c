import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Medal, Award, Target } from 'lucide-react';
import { ZoneLogo } from '@/components/ui/logo-components';
import api from '@/lib/axios';
import toast from 'react-hot-toast';

interface LeaderboardEntry {
    zone_code: string;
    zone_name: string;
    total_points: number;
    head_to_head_points: number;
    group_event_points: number;
    matches_played: number;
}

interface LeaderboardData {
    data: LeaderboardEntry[];
    summary: {
        total_matches: number;
        total_points_awarded: number;
    };
}

export default function Leaderboard() {
    const [leaderboard, setLeaderboard] = useState<LeaderboardData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchLeaderboard();
    }, []);

    const fetchLeaderboard = async () => {
        try {
            setLoading(true);
            const response = await api.get('/admin/sport-matches/leaderboard');
            setLeaderboard(response.data);
        } catch (error: any) {
            toast.error('Gagal memuat leaderboard');
        } finally {
            setLoading(false);
        }
    };

    const getRankIcon = (position: number) => {
        switch (position) {
            case 1:
                return <Trophy className="h-6 w-6 text-yellow-500" />;
            case 2:
                return <Medal className="h-6 w-6 text-gray-400" />;
            case 3:
                return <Award className="h-6 w-6 text-amber-600" />;
            default:
                return <Target className="h-6 w-6 text-gray-500" />;
        }
    };

    const getRankBadgeColor = (position: number) => {
        switch (position) {
            case 1:
                return 'bg-yellow-100 text-yellow-800 border-yellow-300';
            case 2:
                return 'bg-gray-100 text-gray-800 border-gray-300';
            case 3:
                return 'bg-amber-100 text-amber-800 border-amber-300';
            default:
                return 'bg-blue-100 text-blue-800 border-blue-300';
        }
    };

    if (loading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        Kedudukan Keseluruhan
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-500 mt-2">Memuat kedudukan...</p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (!leaderboard) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        Kedudukan Keseluruhan
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8">
                        <p className="text-gray-500">Tiada data kedudukan</p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Target className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                                <p className="text-sm text-gray-600">Jumlah Perlawanan</p>
                                <p className="text-2xl font-bold">{leaderboard.summary.total_matches}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Trophy className="h-5 w-5 text-green-600" />
                            </div>
                            <div>
                                <p className="text-sm text-gray-600">Jumlah Mata</p>
                                <p className="text-2xl font-bold">{leaderboard.summary.total_points_awarded}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <Medal className="h-5 w-5 text-yellow-600" />
                            </div>
                            <div>
                                <p className="text-sm text-gray-600">Juara Semasa</p>
                                <p className="text-2xl font-bold">
                                    {leaderboard.data[0]?.zone_name || 'TBD'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Leaderboard Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        Kedudukan Keseluruhan
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {leaderboard.data.map((entry, index) => (
                            <div
                                key={entry.zone_code}
                                className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                                    index === 0
                                        ? 'bg-yellow-50 border-yellow-200'
                                        : index === 1
                                        ? 'bg-gray-50 border-gray-200'
                                        : index === 2
                                        ? 'bg-amber-50 border-amber-200'
                                        : 'bg-white border-gray-200'
                                }`}
                            >
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center gap-2">
                                        {getRankIcon(index + 1)}
                                        <Badge className={`${getRankBadgeColor(index + 1)} font-bold`}>
                                            #{index + 1}
                                        </Badge>
                                    </div>
                                    
                                    <ZoneLogo zoneCode={entry.zone_code} className="w-12 h-12" />
                                    
                                    <div>
                                        <h3 className="font-semibold text-lg">{entry.zone_name}</h3>
                                        <p className="text-sm text-gray-600">
                                            {entry.matches_played} perlawanan dimainkan
                                        </p>
                                    </div>
                                </div>

                                <div className="text-right">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {entry.total_points} mata
                                    </div>
                                    <div className="text-sm text-gray-600 space-y-1">
                                        <div>Head-to-Head: {entry.head_to_head_points}</div>
                                        <div>Group Events: {entry.group_event_points}</div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
