import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Trophy, Users } from 'lucide-react';
import { toast } from 'react-hot-toast';

// Schema for match settings with group events support
const matchSchema = z.object({
    sport_id: z.string().min(1, 'Sukan diperlukan'),
    title: z.string().min(1, 'Tajuk perlawanan diperlukan'),
    event_type: z.enum(['head_to_head', 'group_event', 'knockout_group', 'category_based']),
    zone_a: z.string().optional(),
    zone_b: z.string().optional(),
    participating_zones: z.array(z.string()).optional(),
    match_date: z.string().min(1, 'Tarikh diperlukan'),
    match_time: z.string().min(1, 'Masa diperlukan'),
    venue: z.string().min(1, 'Tempat diperlukan'),
    status: z.enum(['scheduled', 'ongoing', 'completed', 'cancelled']),
}).refine((data) => {
    // Validate zones based on event type
    if (data.event_type === 'head_to_head' || data.event_type === 'category_based') {
        return data.zone_a && data.zone_b;
    } else if (data.event_type === 'group_event' || data.event_type === 'knockout_group') {
        return data.participating_zones && data.participating_zones.length >= 2;
    }
    return true;
}, {
    message: 'Pilih zon yang sesuai untuk jenis perlawanan',
    path: ['zones']
});

type MatchFormData = z.infer<typeof matchSchema>;

// Simplified form data interface
interface FormData {
    sports: Array<{ id: number; name: string }>;
    zones: Array<{ code: string; name: string }>;
}

interface MatchFormProps {
    match?: any;
    onSuccess: () => void;
    onCancel: () => void;
    onSubmit?: (data: MatchFormData) => void;
    isLoading?: boolean;
}

export default function MatchForm({ match, onSuccess, onCancel, onSubmit: onSubmitProp, isLoading: isLoadingProp }: MatchFormProps) {
    const [formData, setFormData] = useState<FormData | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedZones, setSelectedZones] = useState<string[]>([]);

    // Use parent loading state if provided, otherwise use internal state
    const currentLoading = isLoadingProp !== undefined ? isLoadingProp : isLoading;

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        formState: { errors },
        reset
    } = useForm<MatchFormData>({
        resolver: zodResolver(matchSchema),
        defaultValues: {
            status: 'scheduled',
            event_type: 'head_to_head',
            ...match
        },
        mode: 'onChange' // Enable real-time validation
    });

    const watchedSportId = watch('sport_id');
    const watchedEventType = watch('event_type');
    const watchedZoneA = watch('zone_a');
    const watchedZoneB = watch('zone_b');

    useEffect(() => {
        fetchFormData();
    }, []);

    // Auto-detect event type based on sport
    useEffect(() => {
        if (watchedSportId && formData) {
            const selectedSport = formData.sports.find(s => s.id.toString() === watchedSportId);
            if (selectedSport) {
                // Determine event type based on sport
                let eventType = 'head_to_head';

                if (selectedSport.name === 'Larian 10 x 400m') {
                    eventType = 'knockout_group'; // Knockout system with multiple rounds
                } else if (selectedSport.name === 'Lari-Lari Berganti-ganti') {
                    eventType = 'group_event'; // All zones compete simultaneously
                } else if (selectedSport.name === 'Sukan Rakyat') {
                    eventType = 'group_event'; // All zones compete simultaneously
                } else if (selectedSport.name === 'Badminton' || selectedSport.name === 'Ping Pong') {
                    eventType = 'category_based'; // 5 categories per match
                } else {
                    eventType = 'head_to_head'; // Standard 1v1
                }

                setValue('event_type', eventType);

                // Reset zones when changing sport type
                const isGroupType = eventType === 'group_event' || eventType === 'knockout_group';
                if (isGroupType) {
                    setValue('zone_a', '');
                    setValue('zone_b', '');
                    setSelectedZones([]);
                } else {
                    setValue('participating_zones', []);
                    setSelectedZones([]);
                }
            }
        }
    }, [watchedSportId, formData, setValue]);

    // Auto-generate title for head-to-head matches ONLY if title is empty
    useEffect(() => {
        if (watchedEventType === 'head_to_head' || watchedEventType === 'category_based') {
            if (watchedZoneA && watchedZoneB && formData) {
                const currentTitle = watch('title');
                // Only auto-generate if title is empty or matches the default pattern
                if (!currentTitle || currentTitle.includes(' vs ')) {
                    const zoneAName = formData.zones.find(z => z.code === watchedZoneA)?.name || watchedZoneA;
                    const zoneBName = formData.zones.find(z => z.code === watchedZoneB)?.name || watchedZoneB;
                    setValue('title', `${zoneAName} vs ${zoneBName}`);
                }
            }
        }
    }, [watchedEventType, watchedZoneA, watchedZoneB, formData, setValue, watch]);

    useEffect(() => {
        if (match) {
            reset({
                sport_id: match.sport_id?.toString(),
                title: match.title,
                zone_a: match.zone_a,
                zone_b: match.zone_b,
                match_date: match.match_date,
                match_time: match.match_time,
                venue: match.venue,
                status: match.status,
            });
        }
    }, [match, reset]);

    const fetchFormData = async () => {
        try {
            // Fetch only sports and zones for simplified form
            const [sportsResponse, zonesResponse] = await Promise.all([
                api.get('/admin/sports'),
                api.get('/admin/zones')
            ]);

            setFormData({
                sports: sportsResponse.data,
                zones: zonesResponse.data
            });
        } catch (error) {
            toast.error('Gagal memuat data form');
        }
    };

    const onSubmit = async (data: MatchFormData) => {

        if (onSubmitProp) {
            const payload = {
                ...data,
                sport_id: parseInt(data.sport_id),
                // Ensure proper field handling for different event types
                zone_a: (data.event_type === 'head_to_head' || data.event_type === 'category_based') ? data.zone_a : null,
                zone_b: (data.event_type === 'head_to_head' || data.event_type === 'category_based') ? data.zone_b : null,
                participating_zones: (data.event_type === 'group_event' || data.event_type === 'knockout_group') ? data.participating_zones : null,
            };

            // Let parent handle the API call and success/error handling
            onSubmitProp(payload);
            return;
        }

        try {
            setIsLoading(true);

            const payload = {
                ...data,
                sport_id: parseInt(data.sport_id),
                // Ensure proper field handling for different event types
                zone_a: data.event_type === 'head_to_head' ? data.zone_a : null,
                zone_b: data.event_type === 'head_to_head' ? data.zone_b : null,
                participating_zones: data.event_type === 'group_event' ? data.participating_zones : null,
            };

            if (match) {
                await api.put(`/admin/sport-matches/${match.id}`, payload);
                toast.success('Perlawanan berjaya dikemaskini');
            } else {
                await api.post('/admin/sport-matches', payload);
                toast.success('Perlawanan berjaya ditambah');
            }

            onSuccess();
        } catch (error: any) {
            const message = error.response?.data?.message || 'Gagal menyimpan perlawanan';
            toast.error(message);
        } finally {
            setIsLoading(false);
        }
    };

    if (!formData) {
        return <div className="flex items-center justify-center p-8">Loading...</div>;
    }

    return (
        <form id="match-form" onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Simplified Match Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        Setting Perlawanan
                    </CardTitle>
                    <CardDescription>
                        Tetapan asas untuk perlawanan baru
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Sport Selection */}
                    <div>
                        <Label htmlFor="sport_id">Sukan *</Label>
                        <Select
                            value={watchedSportId}
                            onValueChange={(value) => setValue('sport_id', value)}
                            disabled={currentLoading}
                        >
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Pilih sukan" />
                            </SelectTrigger>
                            <SelectContent>
                                {formData.sports.map((sport) => (
                                    <SelectItem key={sport.id} value={sport.id.toString()}>
                                        {sport.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.sport_id && (
                            <p className="text-sm text-red-600">{errors.sport_id.message}</p>
                        )}

                        {/* Sport Format Information */}
                        {watchedSportId && formData && (() => {
                            const selectedSport = formData.sports.find(s => s.id.toString() === watchedSportId);
                            if (!selectedSport) return null;

                            const getFormatInfo = (sportName: string) => {
                                switch (sportName) {
                                    case 'Larian 10 x 400m':
                                    case 'Lari-Lari Berganti-ganti':
                                        return {
                                            type: 'Knockout System',
                                            format: 'Pusingan Saringan → Separuh Akhir → Akhir',
                                            participants: 'Semua zon (berdasarkan kategori pangkat)',
                                            color: 'bg-orange-50 border-orange-200 text-orange-800'
                                        };
                                    case 'Badminton':
                                    case 'Ping Pong':
                                        return {
                                            type: 'Category-Based',
                                            format: '5 kategori (MS, WS, MD, WD, XD)',
                                            participants: '2 zon head-to-head per kategori',
                                            color: 'bg-purple-50 border-purple-200 text-purple-800'
                                        };
                                    case 'Sukan Rakyat':
                                        return {
                                            type: 'Group Event',
                                            format: 'Semua zon berlumba serentak',
                                            participants: 'Semua zon (pelbagai acara tradisional)',
                                            color: 'bg-green-50 border-green-200 text-green-800'
                                        };
                                    default:
                                        return {
                                            type: 'Head-to-Head',
                                            format: 'Standard 1 vs 1',
                                            participants: '2 zon berhadapan',
                                            color: 'bg-blue-50 border-blue-200 text-blue-800'
                                        };
                                }
                            };

                            const formatInfo = getFormatInfo(selectedSport.name);

                            return (
                                <div className={`mt-3 p-3 rounded-lg border ${formatInfo.color}`}>
                                    <div className="text-sm space-y-1">
                                        <div><strong>Format:</strong> {formatInfo.type}</div>
                                        <div><strong>Sistem:</strong> {formatInfo.format}</div>
                                        <div><strong>Peserta:</strong> {formatInfo.participants}</div>
                                    </div>
                                </div>
                            );
                        })()}
                    </div>

                    {/* Title */}
                    <div>
                        <Label htmlFor="title">Tajuk Perlawanan *</Label>
                        <Input
                            id="title"
                            {...register('title')}
                            placeholder="Contoh: Bola Sepak: Zon A vs Zon B"
                            disabled={currentLoading}
                        />
                        {errors.title && (
                            <p className="text-sm text-red-600">{errors.title.message}</p>
                        )}
                    </div>

                    {/* Zone Selection - Only show after sport is selected */}
                    {watchedSportId && (watchedEventType === 'head_to_head' || watchedEventType === 'category_based') ? (
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="zone_a">Zon A *</Label>
                                <Select
                                    value={watchedZoneA || ''}
                                    onValueChange={(value) => setValue('zone_a', value)}
                                    disabled={currentLoading}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Pilih zon A" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {formData.zones
                                            .filter(zone => zone.code !== watchedZoneB)
                                            .map((zone) => (
                                                <SelectItem key={zone.code} value={zone.code}>
                                                    {zone.name}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                                {errors.zone_a && (
                                    <p className="text-sm text-red-600">{errors.zone_a.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="zone_b">Zon B *</Label>
                                <Select
                                    value={watchedZoneB || ''}
                                    onValueChange={(value) => setValue('zone_b', value)}
                                    disabled={currentLoading}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Pilih zon B" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {formData.zones
                                            .filter(zone => zone.code !== watchedZoneA)
                                            .map((zone) => (
                                                <SelectItem key={zone.code} value={zone.code}>
                                                    {zone.name}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>
                                {errors.zone_b && (
                                    <p className="text-sm text-red-600">{errors.zone_b.message}</p>
                                )}
                            </div>
                        </div>
                    ) : watchedSportId && (watchedEventType === 'group_event' || watchedEventType === 'knockout_group') ? (
                        <div>
                            <Label>Zon Yang Terlibat *</Label>
                            <div className="mt-2 p-4 border rounded-lg bg-blue-50">
                                <div className="flex items-center gap-2 mb-3">
                                    <Users className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm font-medium text-blue-800">
                                        {watchedEventType === 'knockout_group' ? 'Knockout System' : 'Group Event'} - Semua Zon Terlibat
                                    </span>
                                </div>
                                <div className="grid grid-cols-3 gap-2">
                                    {formData.zones.map((zone) => (
                                        <div key={zone.code} className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id={`zone-${zone.code}`}
                                                checked={selectedZones.includes(zone.code)}
                                                onChange={(e) => {
                                                    const newSelected = e.target.checked
                                                        ? [...selectedZones, zone.code]
                                                        : selectedZones.filter(z => z !== zone.code);
                                                    setSelectedZones(newSelected);
                                                    setValue('participating_zones', newSelected);
                                                }}
                                                className="rounded border-gray-300"
                                                disabled={currentLoading}
                                            />
                                            <label htmlFor={`zone-${zone.code}`} className="text-sm">
                                                {zone.name}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                                <p className="text-xs text-blue-600 mt-2">
                                    Pilih semua zon yang akan terlibat dalam acara ini
                                </p>
                            </div>
                            {errors.zones && (
                                <p className="text-sm text-red-600 mt-1">{errors.zones.message}</p>
                            )}
                        </div>
                    ) : (
                        /* Show message when no sport is selected */
                        watchedSportId === '' && (
                            <div className="p-4 border rounded-lg bg-gray-50 text-center">
                                <p className="text-sm text-gray-600">
                                    Pilih sukan terlebih dahulu untuk melihat pilihan zon
                                </p>
                            </div>
                        )
                    )}

                    {/* Date, Time & Venue */}
                    <Separator className="my-4" />

                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="match_date">Tarikh *</Label>
                            <Input
                                id="match_date"
                                type="date"
                                className="w-full"
                                disabled={currentLoading}
                                {...register('match_date')}
                            />
                            {errors.match_date && (
                                <p className="text-sm text-red-600">{errors.match_date.message}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="match_time">Masa *</Label>
                            <Input
                                id="match_time"
                                type="time"
                                className="w-full"
                                disabled={currentLoading}
                                {...register('match_time')}
                            />
                            {errors.match_time && (
                                <p className="text-sm text-red-600">{errors.match_time.message}</p>
                            )}
                        </div>
                    </div>

                    <div>
                        <Label htmlFor="venue">Tempat *</Label>
                        <Input
                            id="venue"
                            className="w-full"
                            disabled={currentLoading}
                            {...register('venue')}
                            placeholder="Contoh: Stadium PU Sendayan"
                        />
                        {errors.venue && (
                            <p className="text-sm text-red-600">{errors.venue.message}</p>
                        )}
                    </div>

                    <div>
                        <Label htmlFor="status">Status</Label>
                        <Select
                            value={watch('status')}
                            onValueChange={(value) => setValue('status', value as any)}
                            disabled={currentLoading}
                        >
                            <SelectTrigger className="w-full">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="scheduled">Dijadualkan</SelectItem>
                                <SelectItem value="ongoing">Sedang Berlangsung</SelectItem>
                                <SelectItem value="completed">Selesai</SelectItem>
                                <SelectItem value="cancelled">Dibatalkan</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Hidden fields */}
                    <input type="hidden" {...register('event_type')} />
                    {watchedEventType === 'group_event' || watchedEventType === 'knockout_group' ? (
                        <input type="hidden" {...register('participating_zones')} />
                    ) : null}
                </CardContent>
            </Card>
        </form>
    );
}
