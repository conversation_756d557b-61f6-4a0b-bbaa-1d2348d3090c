import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Trophy, Users } from 'lucide-react';
import MatchResultForm from './match-result/MatchResultForm';

// Helper function to format date time to Malaysia timezone
const formatMalaysiaDateTime = (dateString: string, timeString: string) => {
    try {
        // Handle different date formats
        let dateTime;
        if (dateString && timeString) {
            // Try different parsing methods
            if (dateString.includes('T')) {
                // Already in ISO format
                dateTime = new Date(dateString);
            } else {
                // Standard date format (YYYY-MM-DD)
                dateTime = new Date(`${dateString} ${timeString}`);
            }
        } else {
            return 'Invalid Date';
        }

        if (isNaN(dateTime.getTime())) {
            return `${dateString} ${timeString}`;
        }

        return dateTime.toLocaleString('ms-MY', {
            timeZone: 'Asia/Kuala_Lumpur',
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    } catch (error) {
        return `${dateString} ${timeString}`;
    }
};

interface MatchResultModalProps {
    isOpen: boolean;
    onClose: () => void;
    match: any;
    onSuccess: () => void;
}

export default function MatchResultModal({ isOpen, onClose, match, onSuccess }: MatchResultModalProps) {
    if (!match) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-[90vw] max-h-[95vh] p-0 flex flex-col">
                {/* Fixed Header */}
                <div className="flex-shrink-0 px-6 py-4 border-b bg-gradient-to-r from-yellow-400 to-yellow-500">
                    <div className="flex items-center gap-3 text-white">
                        <Trophy className="w-6 h-6" />
                        <div>
                            <DialogTitle className="text-white font-bold text-lg">
                                SET KEPUTUSAN PERLAWANAN
                            </DialogTitle>
                            <DialogDescription className="text-yellow-100 text-sm">
                                {match.sport_name?.toUpperCase()} - ZON {match.zone_a} VS ZON {match.zone_b}
                            </DialogDescription>
                        </div>
                    </div>
                </div>

                {/* Match Info */}
                <div className="flex-shrink-0 px-6 py-3 bg-blue-50 border-b">
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Users className="w-4 h-4 text-blue-600" />
                                <span className="text-blue-800 font-medium">
                                    {match.title || `${match.sport_name} - Zon ${match.zone_a} vs Zon ${match.zone_b}`}
                                </span>
                            </div>
                        </div>
                        <div className="text-gray-600">
                            {formatMalaysiaDateTime(match.match_date, match.match_time)} • {match.venue}
                        </div>
                    </div>
                </div>

                {/* Form Content */}
                <div className="flex-1 overflow-hidden">
                    <MatchResultForm 
                        match={match}
                        onSuccess={onSuccess}
                        onClose={onClose}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
