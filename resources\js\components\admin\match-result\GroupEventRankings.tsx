import { UseFormRegister, UseFormWatch, UseFormSetValue } from 'react-hook-form';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ZoneLogo } from '@/components/ui/logo-components';

interface GroupEventRankingsProps {
    match: any;
    register: UseFormRegister<any>;
    watch: UseFormWatch<any>;
    setValue: UseFormSetValue<any>;
    sportRules: any;
}

export default function GroupEventRankings({
    match,
    register,
    watch,
    setValue,
    sportRules
}: GroupEventRankingsProps) {
    if (!match?.participating_zones) return null;

    // Auto-calculate points based on ranking position
    const getPointsForPosition = (position: number): number => {
        const pointsMap: { [key: number]: number } = {
            1: 5, // Johan
            2: 3, // Naib Johan
            3: 1, // Ke-3
        };
        return pointsMap[position] || 0;
    };

    // Handle ranking position change and auto-set points
    const handleRankingChange = (index: number, position: string) => {
        const positionNum = parseInt(position);
        const points = getPointsForPosition(positionNum);

        setValue(`zone_rankings.${index}.ranking_position`, positionNum);
        setValue(`zone_rankings.${index}.points_earned`, points);
        setValue(`zone_rankings.${index}.final_score`, 0); // Set default final_score for validation
    };

    // Get available positions for a specific zone (filter out already selected positions)
    const getAvailablePositions = (currentIndex: number) => {
        const allRankings = watch('zone_rankings') || [];
        const currentPosition = allRankings[currentIndex]?.ranking_position;
        const selectedPositions = allRankings
            .map((ranking: any, index: number) => index !== currentIndex ? ranking?.ranking_position : null)
            .filter((pos: any) => pos !== null && pos !== undefined);

        const allPositions = [
            { value: 1, label: '1st - Johan (5 mata)' },
            { value: 2, label: '2nd - Naib Johan (3 mata)' },
            { value: 3, label: '3rd - Ke-3 (1 mata)' }
        ];

        // Include current position and filter out other selected positions
        return allPositions.filter(pos =>
            pos.value === currentPosition || !selectedPositions.includes(pos.value)
        );
    };

    return (
        <div className="space-y-6">
            {/* Instructions */}
            <div className="text-center py-2">
                <p className="text-sm text-gray-600">
                    Pilih kedudukan untuk setiap zon - mata akan auto-generate berdasarkan kedudukan
                </p>
            </div>

            {/* Zone Rankings Table */}
            <div className="space-y-2">
                {match.participating_zones.map((zoneCode: string, index: number) => (
                    <div key={zoneCode} className="bg-white rounded-lg border border-gray-200 p-3">
                        <div className="grid grid-cols-3 gap-4 items-center w-full">
                            {/* Zone Info */}
                            <div className="flex items-center gap-2 min-w-0">
                                <ZoneLogo zoneCode={zoneCode} className="w-8 h-8" />
                                <span className="font-medium">
                                    {zoneCode === 'BAKAT-KKD' ? 'BAKAT-KKD' : `Zon ${zoneCode}`}
                                </span>
                            </div>

                            {/* Ranking Position */}
                            <div className="min-w-0 flex-1">
                                <Label className="text-xs text-gray-600">Kedudukan</Label>
                                <Select
                                    value={watch(`zone_rankings.${index}.ranking_position`)?.toString() || ''}
                                    onValueChange={(value) => handleRankingChange(index, value)}
                                >
                                    <SelectTrigger className="mt-1 w-full">
                                        <SelectValue placeholder="Pilih kedudukan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {getAvailablePositions(index).map((position) => (
                                            <SelectItem key={position.value} value={position.value.toString()}>
                                                {position.label}
                                            </SelectItem>
                                        ))}
                                        {getAvailablePositions(index).length === 0 && (
                                            <SelectItem value="" disabled>
                                                Semua kedudukan telah dipilih
                                            </SelectItem>
                                        )}
                                    </SelectContent>
                                </Select>
                                {/* Hidden input for form submission */}
                                <Input
                                    type="hidden"
                                    {...register(`zone_rankings.${index}.ranking_position`, {
                                        valueAsNumber: true
                                    })}
                                />
                            </div>



                            {/* Points Earned - Auto Generated */}
                            <div className="text-center min-w-0">
                                <Label className="text-xs text-gray-600">Mata Kejuaraan</Label>
                                <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded text-sm font-medium text-center">
                                    {watch(`zone_rankings.${index}.points_earned`) || '-'} mata
                                </div>
                                {/* Hidden input for form submission */}
                                <Input
                                    type="hidden"
                                    {...register(`zone_rankings.${index}.points_earned`, {
                                        valueAsNumber: true
                                    })}
                                />
                            </div>
                        </div>

                        {/* Hidden fields for form submission */}
                        <Input
                            type="hidden"
                            value={zoneCode}
                            {...register(`zone_rankings.${index}.zone_code`)}
                        />
                        <Input
                            type="hidden"
                            value={0}
                            {...register(`zone_rankings.${index}.final_score`, {
                                valueAsNumber: true
                            })}
                        />
                    </div>
                ))}
            </div>

            {/* Points System Info */}
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h4 className="font-semibold text-blue-800 mb-2">Sistem Mata Kejuaraan</h4>
                <div className="grid grid-cols-3 gap-2 text-sm text-blue-700">
                    <div>1st: 5 mata</div>
                    <div>2nd: 3 mata</div>
                    <div>3rd: 1 mata</div>
                </div>
            </div>
        </div>
    );
}
