import { UseFormRegister, UseFormWatch, UseFormSetValue } from 'react-hook-form';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ZoneLogo } from '@/components/ui/logo-components';

interface GroupEventRankingsProps {
    match: any;
    register: UseFormRegister<any>;
    watch: UseFormWatch<any>;
    setValue: UseFormSetValue<any>;
    sportRules: any;
}

export default function GroupEventRankings({
    match,
    register,
    watch,
    setValue,
    sportRules
}: GroupEventRankingsProps) {
    if (!match?.participating_zones) return null;

    // Auto-calculate points based on ranking position
    const getPointsForPosition = (position: number): number => {
        const pointsMap: { [key: number]: number } = {
            1: 10, // 1st place
            2: 8,  // 2nd place
            3: 6,  // 3rd place
            4: 4,  // 4th place
            5: 2,  // 5th place
            6: 1   // 6th place
        };
        return pointsMap[position] || 0;
    };

    // Handle ranking position change and auto-set points
    const handleRankingChange = (index: number, position: string) => {
        const positionNum = parseInt(position);
        const points = getPointsForPosition(positionNum);

        setValue(`zone_rankings.${index}.ranking_position`, positionNum);
        setValue(`zone_rankings.${index}.points_earned`, points);
        setValue(`zone_rankings.${index}.final_score`, 0); // Set default final_score for validation
    };

    return (
        <div className="space-y-6">
            {/* Instructions */}
            <div className="text-center py-2">
                <p className="text-sm text-gray-600">
                    Pilih kedudukan untuk setiap zon - mata akan auto-generate berdasarkan kedudukan
                </p>
            </div>

            {/* Zone Rankings Table */}
            <div className="space-y-2">
                {match.participating_zones.map((zoneCode: string, index: number) => (
                    <div key={zoneCode} className="bg-white rounded-lg border border-gray-200 p-3">
                        <div className="grid grid-cols-3 gap-4 items-center w-full">
                            {/* Zone Info */}
                            <div className="flex items-center gap-2 min-w-0">
                                <ZoneLogo zoneCode={zoneCode} className="w-8 h-8" />
                                <span className="font-medium">
                                    {zoneCode === 'BAKAT-KKD' ? 'BAKAT-KKD' : `Zon ${zoneCode}`}
                                </span>
                            </div>

                            {/* Ranking Position */}
                            <div className="min-w-0 flex-1">
                                <Label className="text-xs text-gray-600">Kedudukan</Label>
                                <Select
                                    value={watch(`zone_rankings.${index}.ranking_position`)?.toString() || ''}
                                    onValueChange={(value) => handleRankingChange(index, value)}
                                >
                                    <SelectTrigger className="mt-1 w-full">
                                        <SelectValue placeholder="Pilih kedudukan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">1st (10 mata)</SelectItem>
                                        <SelectItem value="2">2nd (8 mata)</SelectItem>
                                        <SelectItem value="3">3rd (6 mata)</SelectItem>
                                        <SelectItem value="4">4th (4 mata)</SelectItem>
                                        <SelectItem value="5">5th (2 mata)</SelectItem>
                                        <SelectItem value="6">6th (1 mata)</SelectItem>
                                    </SelectContent>
                                </Select>
                                {/* Hidden input for form submission */}
                                <Input
                                    type="hidden"
                                    {...register(`zone_rankings.${index}.ranking_position`, {
                                        valueAsNumber: true
                                    })}
                                />
                            </div>



                            {/* Points Earned - Auto Generated */}
                            <div className="text-center min-w-0">
                                <Label className="text-xs text-gray-600">Mata Kejuaraan</Label>
                                <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded text-sm font-medium text-center">
                                    {watch(`zone_rankings.${index}.points_earned`) || '-'} mata
                                </div>
                                {/* Hidden input for form submission */}
                                <Input
                                    type="hidden"
                                    {...register(`zone_rankings.${index}.points_earned`, {
                                        valueAsNumber: true
                                    })}
                                />
                            </div>
                        </div>

                        {/* Hidden fields for form submission */}
                        <Input
                            type="hidden"
                            value={zoneCode}
                            {...register(`zone_rankings.${index}.zone_code`)}
                        />
                        <Input
                            type="hidden"
                            value={0}
                            {...register(`zone_rankings.${index}.final_score`, {
                                valueAsNumber: true
                            })}
                        />
                    </div>
                ))}
            </div>

            {/* Points System Info */}
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h4 className="font-semibold text-blue-800 mb-2">Sistem Mata Kejuaraan</h4>
                <div className="grid grid-cols-3 gap-2 text-sm text-blue-700">
                    <div>1st: 10 mata</div>
                    <div>2nd: 8 mata</div>
                    <div>3rd: 6 mata</div>
                    <div>4th: 4 mata</div>
                    <div>5th: 2 mata</div>
                    <div>6th: 1 mata</div>
                </div>
            </div>
        </div>
    );
}
