import { UseFormRegister, UseFormSetValue } from 'react-hook-form';
import { Label } from '@/components/ui/label';
import { ZoneLogo } from '@/components/ui/logo-components';

interface HeadToHeadScoreInputProps {
    match: any;
    register: UseFormRegister<any>;
    setValue: UseFormSetValue<any>;
    selectedWinner: string;
    isPointBasedSport: boolean;
}

// All head-to-head sports should use manual score input
// because exact scores (like 21-11, 15-21) are important for records
const isWinnerSelectionSport = (sportName: string) => {
    return false; // Always use manual input for all sports
};

// Generate realistic scores based on sport and winner
const generateScoresForSport = (sportName: string, winner: string, zoneA: string, zoneB: string) => {
    const isZoneAWinner = winner === String(zoneA);
    const isDraw = winner === 'draw';

    switch (sportName) {
        case 'Bola Sepak':
            if (isDraw) return { scoreA: 1, scoreB: 1 };
            return isZoneAWinner ? { scoreA: 2, scoreB: 1 } : { scoreA: 1, scoreB: 2 };

        case 'Bola Tampar':
            if (isDraw) return { scoreA: 1, scoreB: 1 }; // Rare in volleyball
            return isZoneAWinner ? { scoreA: 2, scoreB: 1 } : { scoreA: 1, scoreB: 2 };

        case 'Ping Pong':
        case 'Badminton':
            if (isDraw) return { scoreA: 2, scoreB: 2 }; // Best of 5, 2-2 draw
            return isZoneAWinner ? { scoreA: 3, scoreB: 1 } : { scoreA: 1, scoreB: 3 };

        case 'Bola Jaring':
            if (isDraw) return { scoreA: 45, scoreB: 45 };
            return isZoneAWinner ? { scoreA: 52, scoreB: 38 } : { scoreA: 38, scoreB: 52 };

        default:
            if (isDraw) return { scoreA: 1, scoreB: 1 };
            return isZoneAWinner ? { scoreA: 1, scoreB: 0 } : { scoreA: 0, scoreB: 1 };
    }
};

// Get sport-specific labels and limits
const getSportScoreLabel = (sportName: string) => {
    switch (sportName) {
        case 'Bola Sepak': return 'Gol';
        case 'Bola Tampar': return 'Mata (per set)';
        case 'Bola Jaring': return 'Mata';
        case 'Ping Pong': return 'Mata (per game)';
        case 'Badminton': return 'Mata (per game)';
        default: return 'Mata';
    }
};

const getSportMaxScore = (sportName: string) => {
    switch (sportName) {
        case 'Bola Sepak': return 10;
        case 'Bola Tampar': return 30; // Volleyball can go to 25+ points
        case 'Bola Jaring': return 100;
        case 'Ping Pong': return 30; // Table tennis can go to 21+ points
        case 'Badminton': return 30; // Badminton can go to 21+ points
        default: return 100;
    }
};

const getSportPlaceholder = (sportName: string) => {
    switch (sportName) {
        case 'Bola Sepak': return 'Gol (contoh: 2)';
        case 'Bola Tampar': return 'Mata (contoh: 25)';
        case 'Bola Jaring': return 'Mata (contoh: 45)';
        case 'Ping Pong': return 'Mata (contoh: 21)';
        case 'Badminton': return 'Mata (contoh: 21)';
        default: return 'Mata (contoh: 21)';
    }
};

const getSportExample = (sportName: string) => {
    switch (sportName) {
        case 'Bola Sepak': return 'Zon A: 2 gol, Zon B: 1 gol';
        case 'Bola Tampar': return 'Zon A: 25 mata, Zon B: 23 mata';
        case 'Bola Jaring': return 'Zon A: 52 mata, Zon B: 48 mata';
        case 'Ping Pong': return 'Zon A: 21 mata, Zon B: 19 mata';
        case 'Badminton': return 'Zon A: 21 mata, Zon B: 18 mata';
        default: return 'Zon A: 21 mata, Zon B: 19 mata';
    }
};

export default function HeadToHeadScoreInput({
    match,
    register,
    setValue,
    selectedWinner,
    isPointBasedSport
}: HeadToHeadScoreInputProps) {
    if (!match) return null;

    const useWinnerSelection = isWinnerSelectionSport(match.sport_name);

    return (
        <div className="space-y-6">


            {/* Score Input Section */}
            {!useWinnerSelection ? (
                <div className="grid grid-cols-2 gap-6">
                    {/* Zone A Score Input */}
                    <div className="text-center">
                        <ZoneLogo zoneCode={String(match.zone_a)} className="w-24 h-24 mx-auto mb-3" />
                        <Label className="text-base font-bold text-gray-800 block mb-2">
                            ZON {match.zone_a}
                        </Label>
                        <input
                            type="number"
                            min="0"
                            max={getSportMaxScore(match.sport_name)}
                            placeholder={getSportPlaceholder(match.sport_name)}
                            className="w-full text-center text-xl font-bold h-12 border-2 border-blue-300 focus:border-blue-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md px-3"
                            {...register('score_zone_a', {
                                valueAsNumber: true
                            })}
                        />
                        <div className="text-xs text-gray-500 mt-1">{getSportScoreLabel(match.sport_name)}</div>
                    </div>

                    {/* Zone B Score Input */}
                    <div className="text-center">
                        <ZoneLogo zoneCode={String(match.zone_b)} className="w-24 h-24 mx-auto mb-3" />
                        <Label className="text-base font-bold text-gray-800 block mb-2">
                            ZON {match.zone_b}
                        </Label>
                        <input
                            type="number"
                            min="0"
                            max={getSportMaxScore(match.sport_name)}
                            placeholder={getSportPlaceholder(match.sport_name)}
                            className="w-full text-center text-xl font-bold h-12 border-2 border-blue-300 focus:border-blue-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md px-3"
                            {...register('score_zone_b', {
                                valueAsNumber: true
                            })}
                        />
                        <div className="text-xs text-gray-500 mt-1">{getSportScoreLabel(match.sport_name)}</div>
                    </div>
                </div>
            ) : (
                /* Winner Selection for Category-based Sports */
                <div className="space-y-4">
                    <div className="text-center">
                        <h3 className="text-lg font-semibold mb-4">Pilih Pemenang</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <button
                                type="button"
                                onClick={() => {
                                    setValue('winner_zone', String(match.zone_a));
                                    const scores = generateScoresForSport(match.sport_name, String(match.zone_a), match.zone_a, match.zone_b);
                                    setValue('score_zone_a', scores.scoreA);
                                    setValue('score_zone_b', scores.scoreB);
                                }}
                                className={`p-4 rounded-lg border-2 transition-all ${
                                    selectedWinner === String(match.zone_a)
                                        ? 'border-green-500 bg-green-50'
                                        : 'border-gray-300 hover:border-gray-400'
                                }`}
                            >
                                <ZoneLogo zoneCode={String(match.zone_a)} className="w-16 h-16 mx-auto mb-2" />
                                <div className="font-semibold">ZON {match.zone_a}</div>
                            </button>

                            <button
                                type="button"
                                onClick={() => {
                                    setValue('winner_zone', String(match.zone_b));
                                    const scores = generateScoresForSport(match.sport_name, String(match.zone_b), match.zone_a, match.zone_b);
                                    setValue('score_zone_a', scores.scoreA);
                                    setValue('score_zone_b', scores.scoreB);
                                }}
                                className={`p-4 rounded-lg border-2 transition-all ${
                                    selectedWinner === String(match.zone_b)
                                        ? 'border-green-500 bg-green-50'
                                        : 'border-gray-300 hover:border-gray-400'
                                }`}
                            >
                                <ZoneLogo zoneCode={String(match.zone_b)} className="w-16 h-16 mx-auto mb-2" />
                                <div className="font-semibold">ZON {match.zone_b}</div>
                            </button>
                        </div>

                        <button
                            type="button"
                            onClick={() => {
                                setValue('winner_zone', 'draw');
                                const scores = generateScoresForSport(match.sport_name, 'draw', match.zone_a, match.zone_b);
                                setValue('score_zone_a', scores.scoreA);
                                setValue('score_zone_b', scores.scoreB);
                            }}
                            className={`mt-4 px-6 py-2 rounded-lg border-2 transition-all ${
                                selectedWinner === 'draw'
                                    ? 'border-yellow-500 bg-yellow-50'
                                    : 'border-gray-300 hover:border-gray-400'
                            }`}
                        >
                            Seri
                        </button>
                    </div>
                </div>
            )}

            {/* Winner Display */}
            {selectedWinner && selectedWinner !== 'draw' && (
                <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="text-green-800 font-semibold">
                        🏆 Pemenang: ZON {selectedWinner}
                    </div>
                    <div className="text-sm text-green-600 mt-1">
                        Mata kejuaraan: 3 mata
                    </div>
                </div>
            )}

            {selectedWinner === 'draw' && (
                <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="text-yellow-800 font-semibold">
                        🤝 Keputusan Seri
                    </div>
                    <div className="text-sm text-yellow-600 mt-1">
                        Mata kejuaraan: 1 mata setiap zon
                    </div>
                </div>
            )}
        </div>
    );
}
