import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';

interface SetBasedScoreInputProps {
    match: any;
    register: any;
    setValue: any;
    selectedWinner: string;
}

interface SetScore {
    zone_a: number;
    zone_b: number;
}

export default function SetBasedScoreInput({
    match,
    register,
    setValue,
    selectedWinner
}: SetBasedScoreInputProps) {
    // Different initial sets based on sport
    const getInitialSets = () => {
        switch (match.sport_name) {
            case 'Bola Jaring':
                // Netball has 4 quarters
                return [
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 }
                ];
            case 'Bola Sepak':
                // Football has 2 halves
                return [
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 }
                ];
            case 'Ping Pong':
            case 'Badminton':
                // Best of 3 games minimum
                return [
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 }
                ];
            case 'Bola Tampar':
            default:
                // Volleyball has minimum 2 sets
                return [
                    { zone_a: 0, zone_b: 0 },
                    { zone_a: 0, zone_b: 0 }
                ];
        }
    };

    const [sets, setSets] = useState<SetScore[]>(getInitialSets());

    // Calculate total sets/quarters won by each zone
    const calculateSetsWon = (sets: SetScore[]) => {
        switch (match.sport_name) {
            case 'Bola Jaring':
                // For netball, calculate total points across all quarters
                let zoneATotal = 0;
                let zoneBTotal = 0;

                sets.forEach(set => {
                    zoneATotal += set.zone_a;
                    zoneBTotal += set.zone_b;
                });

                return { zoneASets: zoneATotal, zoneBSets: zoneBTotal };

            case 'Bola Sepak':
                // For football, calculate total goals across all halves
                let zoneAGoals = 0;
                let zoneBGoals = 0;

                sets.forEach(set => {
                    zoneAGoals += set.zone_a;
                    zoneBGoals += set.zone_b;
                });

                return { zoneASets: zoneAGoals, zoneBSets: zoneBGoals };

            case 'Ping Pong':
            case 'Badminton':
            case 'Bola Tampar':
            default:
                // For set-based sports, calculate sets won
                let zoneASets = 0;
                let zoneBSets = 0;

                sets.forEach(set => {
                    if (set.zone_a > set.zone_b) {
                        zoneASets++;
                    } else if (set.zone_b > set.zone_a) {
                        zoneBSets++;
                    }
                });

                return { zoneASets, zoneBSets };
        }
    };

    // Update form values when sets change
    useEffect(() => {
        switch (match.sport_name) {
            case 'Bola Jaring':
                // For netball, calculate total points across all quarters
                const totalZoneA = sets.reduce((sum, set) => sum + set.zone_a, 0);
                const totalZoneB = sets.reduce((sum, set) => sum + set.zone_b, 0);

                setValue('score_zone_a', totalZoneA, { shouldValidate: true });
                setValue('score_zone_b', totalZoneB, { shouldValidate: true });
                setValue('points_zone_a', totalZoneA, { shouldValidate: true });
                setValue('points_zone_b', totalZoneB, { shouldValidate: true });

                // Determine winner based on total points
                if (totalZoneA > totalZoneB) {
                    setValue('winner_zone', match.zone_a, { shouldValidate: true });
                } else if (totalZoneB > totalZoneA) {
                    setValue('winner_zone', match.zone_b, { shouldValidate: true });
                } else {
                    setValue('winner_zone', 'draw', { shouldValidate: true });
                }
                break;

            case 'Bola Sepak':
                // For football, calculate total goals across both halves
                const totalGoalsA = sets.reduce((sum, set) => sum + set.zone_a, 0);
                const totalGoalsB = sets.reduce((sum, set) => sum + set.zone_b, 0);

                setValue('score_zone_a', totalGoalsA, { shouldValidate: true });
                setValue('score_zone_b', totalGoalsB, { shouldValidate: true });

                // Determine winner based on total goals
                if (totalGoalsA > totalGoalsB) {
                    setValue('winner_zone', match.zone_a, { shouldValidate: true });
                } else if (totalGoalsB > totalGoalsA) {
                    setValue('winner_zone', match.zone_b, { shouldValidate: true });
                } else {
                    setValue('winner_zone', 'draw', { shouldValidate: true });
                }
                break;

            case 'Ping Pong':
            case 'Badminton':
            case 'Bola Tampar':
            default:
                // For set-based sports, calculate sets/games won
                const { zoneASets, zoneBSets } = calculateSetsWon(sets);

                setValue('score_zone_a', zoneASets, { shouldValidate: true });
                setValue('score_zone_b', zoneBSets, { shouldValidate: true });

                // Determine winner based on sets won
                if (zoneASets > zoneBSets) {
                    setValue('winner_zone', match.zone_a, { shouldValidate: true });
                } else if (zoneBSets > zoneASets) {
                    setValue('winner_zone', match.zone_b, { shouldValidate: true });
                } else {
                    setValue('winner_zone', 'draw', { shouldValidate: true });
                }
                break;
        }

        // Store detailed scores
        const detailedScoresData = JSON.stringify({ sets });
        setValue('detailed_scores', detailedScoresData, { shouldValidate: true });
    }, [sets, setValue, match.zone_a, match.zone_b, match.sport_name]);

    const addSet = () => {
        const sportRules = getSportRules();
        if (sportRules.fixedSets || sets.length >= sportRules.maxSets) {
            return;
        }
        setSets([...sets, { zone_a: 0, zone_b: 0 }]);
    };

    const removeSet = (index: number) => {
        const sportRules = getSportRules();
        if (sportRules.fixedSets || sets.length <= sportRules.minSets) {
            return;
        }
        setSets(sets.filter((_, i) => i !== index));
    };

    // Get sport-specific rules
    const getSportRules = () => {
        switch (match.sport_name) {
            case 'Bola Jaring':
                return { fixedSets: true, minSets: 4, maxSets: 4 };
            case 'Bola Sepak':
                return { fixedSets: true, minSets: 2, maxSets: 2 };
            case 'Ping Pong':
            case 'Badminton':
                return { fixedSets: false, minSets: 3, maxSets: 5 };
            case 'Bola Tampar':
            default:
                return { fixedSets: false, minSets: 2, maxSets: 5 };
        }
    };

    const updateSet = (index: number, zone: 'zone_a' | 'zone_b', value: number) => {
        const newSets = [...sets];
        newSets[index][zone] = value;
        setSets(newSets);
    };

    const { zoneASets, zoneBSets } = calculateSetsWon(sets);

    // Get sport-specific labels
    const getSportLabels = () => {
        switch (match.sport_name) {
            case 'Bola Jaring':
                return {
                    title: 'Keputusan Quarter Bola Jaring',
                    description: 'Masukkan mata untuk setiap quarter (contoh: 15-12, 18-14, 20-16, 17-13)',
                    unitName: 'Quarter',
                    maxScore: 50
                };
            case 'Bola Sepak':
                return {
                    title: 'Keputusan Separuh Masa Bola Sepak',
                    description: 'Masukkan gol untuk setiap separuh masa (contoh: 1-0, 2-1)',
                    unitName: 'Separuh Masa',
                    maxScore: 10
                };
            case 'Ping Pong':
                return {
                    title: 'Keputusan Game Ping Pong',
                    description: 'Masukkan mata untuk setiap game (contoh: 11-9, 11-7, 11-5)',
                    unitName: 'Game',
                    maxScore: 30
                };
            case 'Badminton':
                return {
                    title: 'Keputusan Game Badminton',
                    description: 'Masukkan mata untuk setiap game (contoh: 21-18, 21-15, 21-12)',
                    unitName: 'Game',
                    maxScore: 30
                };
            case 'Bola Tampar':
            default:
                return {
                    title: 'Keputusan Set Bola Tampar',
                    description: 'Masukkan mata untuk setiap set (contoh: 25-23, 21-25, 15-13)',
                    unitName: 'Set',
                    maxScore: 50
                };
        }
    };

    const labels = getSportLabels();

    return (
        <div className="space-y-6">
            <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-800">
                    {labels.title}
                </h3>
                <p className="text-sm text-gray-600">
                    {labels.description}
                </p>
            </div>

            {/* Sets Input */}
            <div className="space-y-4">
                {sets.map((set, index) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                            <Label className="font-medium">{labels.unitName} {index + 1}</Label>
                            {match.sport_name !== 'Bola Jaring' && sets.length > 2 && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeSet(index)}
                                    className="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            )}
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                            <div className="text-center">
                                <Label className="text-sm font-medium text-blue-600">
                                    ZON {match.zone_a}
                                </Label>
                                <Input
                                    type="number"
                                    min="0"
                                    max={labels.maxScore}
                                    value={set.zone_a}
                                    onChange={(e) => updateSet(index, 'zone_a', parseInt(e.target.value) || 0)}
                                    className="text-center text-lg font-bold mt-1"
                                    placeholder="0"
                                />
                            </div>

                            <div className="text-center">
                                <Label className="text-sm font-medium text-orange-600">
                                    ZON {match.zone_b}
                                </Label>
                                <Input
                                    type="number"
                                    min="0"
                                    max={labels.maxScore}
                                    value={set.zone_b}
                                    onChange={(e) => updateSet(index, 'zone_b', parseInt(e.target.value) || 0)}
                                    className="text-center text-lg font-bold mt-1"
                                    placeholder="0"
                                />
                            </div>
                        </div>

                        {/* Set/Quarter Winner Indicator */}
                        <div className="mt-2 text-center">
                            {match.sport_name === 'Bola Jaring' ? (
                                // For netball, just show the scores
                                <span className="text-sm text-gray-600">
                                    {set.zone_a} - {set.zone_b}
                                </span>
                            ) : match.sport_name === 'Bola Sepak' ? (
                                // For football, just show the half scores
                                <span className="text-sm text-gray-600">
                                    {set.zone_a} - {set.zone_b}
                                </span>
                            ) : (
                                // For set-based sports (volleyball, badminton, ping pong), show set winner
                                set.zone_a > set.zone_b ? (
                                    <span className="text-sm font-medium text-blue-600">
                                        ✓ Zon {match.zone_a} menang set ini
                                    </span>
                                ) : set.zone_b > set.zone_a ? (
                                    <span className="text-sm font-medium text-orange-600">
                                        ✓ Zon {match.zone_b} menang set ini
                                    </span>
                                ) : (
                                    <span className="text-sm text-gray-500">
                                        Set belum selesai
                                    </span>
                                )
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* Add Set Button - only for set-based sports */}
            {match.sport_name !== 'Bola Jaring' && match.sport_name !== 'Bola Sepak' && sets.length < 5 && (
                <div className="text-center">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={addSet}
                        className="flex items-center gap-2"
                    >
                        <Plus className="h-4 w-4" />
                        Tambah Set
                    </Button>
                </div>
            )}

            {/* Match Summary */}
            <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-semibold text-center mb-3">Ringkasan Perlawanan</h4>
                <div className="grid grid-cols-2 gap-4 text-center">
                    <div className={`p-3 rounded ${zoneASets > zoneBSets ? 'bg-green-100 border-2 border-green-300' : 'bg-gray-100'}`}>
                        <div className="text-sm text-gray-600">ZON {match.zone_a}</div>
                        <div className="text-2xl font-bold text-blue-600">{zoneASets}</div>
                        <div className="text-xs text-gray-500">
                            {match.sport_name === 'Bola Jaring' ? 'jumlah mata' : 'set menang'}
                        </div>
                    </div>
                    <div className={`p-3 rounded ${zoneBSets > zoneASets ? 'bg-green-100 border-2 border-green-300' : 'bg-gray-100'}`}>
                        <div className="text-sm text-gray-600">ZON {match.zone_b}</div>
                        <div className="text-2xl font-bold text-orange-600">{zoneBSets}</div>
                        <div className="text-xs text-gray-500">
                            {match.sport_name === 'Bola Jaring' ? 'jumlah mata' : 'set menang'}
                        </div>
                    </div>
                </div>
                
                {/* Winner Display */}
                <div className="mt-3 text-center">
                    {zoneASets > zoneBSets ? (
                        <div className="text-green-700 font-semibold">
                            🏆 Zon {match.zone_a} Menang!
                        </div>
                    ) : zoneBSets > zoneASets ? (
                        <div className="text-green-700 font-semibold">
                            🏆 Zon {match.zone_b} Menang!
                        </div>
                    ) : (
                        <div className="text-gray-600">
                            Perlawanan masih berlangsung
                        </div>
                    )}
                </div>
            </div>

            {/* Hidden inputs for form submission */}
            <input type="hidden" {...register('score_zone_a')} />
            <input type="hidden" {...register('score_zone_b')} />
            <input type="hidden" {...register('points_zone_a')} />
            <input type="hidden" {...register('points_zone_b')} />
            <input type="hidden" {...register('winner_zone')} />
        </div>
    );
}
