import { UseFormSetValue } from 'react-hook-form';
import { Button } from '@/components/ui/button';

interface WalkoverSectionProps {
    match: any;
    setValue: UseFormSetValue<any>;
    isWalkover: boolean;
    setIsWalkover: (value: boolean) => void;
    selectedWinner: string;
    setSelectedWinner: (value: string) => void;
}

export default function WalkoverSection({ 
    match, 
    setValue, 
    isWalkover, 
    setIsWalkover,
    selectedWinner,
    setSelectedWinner
}: WalkoverSectionProps) {
    if (!match) return null;

    const handleWalkoverSelect = (winner: string) => {
        setSelectedWinner(winner);
        setValue('winner_zone', winner);
        setValue('score_zone_a', winner === String(match.zone_a) ? 1 : 0);
        setValue('score_zone_b', winner === String(match.zone_b) ? 1 : 0);
        setValue('points_zone_a', winner === String(match.zone_a) ? 3 : 0);
        setValue('points_zone_b', winner === String(match.zone_b) ? 3 : 0);
        setValue('notes', `Walkover - ${winner === String(match.zone_a) ? `Zon ${match.zone_a}` : `Zon ${match.zone_b}`} menang`);
    };

    const handleResetWalkover = () => {
        setIsWalkover(false);
        setSelectedWinner('');
        setValue('winner_zone', '');
        setValue('score_zone_a', 0);
        setValue('score_zone_b', 0);
        setValue('points_zone_a', 0);
        setValue('points_zone_b', 0);
        setValue('notes', '');
    };

    return (
        <div className="space-y-4">
            {/* Walkover Winner Display */}
            {isWalkover && selectedWinner && (
                <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                    <div className="flex items-center justify-center gap-2 text-red-800 font-semibold mb-2">
                        <span className="text-lg">⚠️</span>
                        WALKOVER - {selectedWinner === String(match.zone_a) ? `ZON ${match.zone_a}` : `ZON ${match.zone_b}`} MENANG
                    </div>
                    <div className="text-center text-sm text-red-600">
                        {selectedWinner === String(match.zone_a) ? `Zon ${match.zone_b}` : `Zon ${match.zone_a}`} tidak hadir
                    </div>
                </div>
            )}

            {/* Walkover Options */}
            <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                <div className="flex items-center justify-center gap-2 text-orange-800 font-semibold mb-3">
                    <span className="text-lg">⚠️</span>
                    Tidak Hadir (Walk Over)
                </div>
                <div className="text-center text-sm text-orange-600 mb-4">
                    Klik jika salah satu zon tidak hadir untuk perlawanan
                </div>
                
                <div className="flex gap-3 justify-center">
                    <Button
                        type="button"
                        variant={selectedWinner === String(match.zone_a) && isWalkover ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                            setIsWalkover(true);
                            handleWalkoverSelect(String(match.zone_a));
                        }}
                        className={selectedWinner === String(match.zone_a) && isWalkover ? "bg-red-600 hover:bg-red-700" : "border-red-300 text-red-700 hover:bg-red-50"}
                    >
                        Zon {match.zone_a} Tidak Hadir
                    </Button>
                    
                    <Button
                        type="button"
                        variant={selectedWinner === String(match.zone_b) && isWalkover ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                            setIsWalkover(true);
                            handleWalkoverSelect(String(match.zone_b));
                        }}
                        className={selectedWinner === String(match.zone_b) && isWalkover ? "bg-red-600 hover:bg-red-700" : "border-red-300 text-red-700 hover:bg-red-50"}
                    >
                        Zon {match.zone_b} Tidak Hadir
                    </Button>
                </div>

                {/* Reset Button */}
                {isWalkover && (
                    <div className="mt-4 text-center">
                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleResetWalkover}
                            className="text-gray-600 border-gray-300 hover:bg-gray-50"
                        >
                            🔄 Reset
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
}
