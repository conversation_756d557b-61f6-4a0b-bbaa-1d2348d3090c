import { Loader2, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogOverlay, DialogPortal } from '@/components/ui/dialog';
import { Match } from '@/types/matches';

interface MatchDeleteDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    match: Match | null;
    isDeleting: boolean;
}

export default function MatchDeleteDialog({ 
    isOpen, 
    onClose, 
    onConfirm, 
    match, 
    isDeleting 
}: MatchDeleteDialogProps) {
    if (!match) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogPortal>
                <DialogOverlay className="bg-black/50" />
                <DialogContent className="sm:max-w-md">
                    <div className="flex items-center justify-between p-6 border-b">
                        <h2 className="text-lg font-semibold">Padam Perlawanan</h2>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            disabled={isDeleting}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>

                    <div className="p-6">
                        <p className="text-sm text-gray-600 mb-4">
                            Adakah anda pasti ingin memadam perlawanan ini?
                        </p>
                        <div className="bg-gray-50 p-3 rounded-lg mb-4">
                            <p className="font-medium">{match.title}</p>
                            <p className="text-sm text-gray-600">
                                {match.zone_a} vs {match.zone_b}
                            </p>
                            <p className="text-sm text-gray-600">
                                {match.match_date} - {match.venue}
                            </p>
                        </div>
                        <p className="text-sm text-red-600">
                            Tindakan ini tidak boleh dibatalkan.
                        </p>
                    </div>

                    <div className="flex gap-3 justify-end p-6 border-t">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            disabled={isDeleting}
                        >
                            Batal
                        </Button>
                        <Button
                            onClick={onConfirm}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Memadamkan...
                                </>
                            ) : (
                                'Padam'
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </DialogPortal>
        </Dialog>
    );
}
