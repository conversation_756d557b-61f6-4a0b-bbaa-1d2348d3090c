import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MatchFilters } from '@/types/matches';

interface MatchesFiltersProps {
    filters: MatchFilters;
    onFiltersChange: (filters: Partial<MatchFilters>) => void;
    uniqueSports: string[];
    uniqueVenues: string[];
    uniqueZones: string[];
}

export default function MatchesFilters({ 
    filters, 
    onFiltersChange, 
    uniqueSports, 
    uniqueVenues, 
    uniqueZones 
}: MatchesFiltersProps) {
    return (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                            placeholder="<PERSON><PERSON> perlawanan, sukan, atau zon..."
                            value={filters.searchTerm}
                            onChange={(e) => onFiltersChange({ searchTerm: e.target.value })}
                            className="pl-10"
                        />
                    </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                    <Select 
                        value={filters.statusFilter} 
                        onValueChange={(value) => onFiltersChange({ statusFilter: value })}
                    >
                        <SelectTrigger className="w-full sm:w-40">
                            <SelectValue placeholder="Semua Status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Semua Status</SelectItem>
                            <SelectItem value="scheduled">Dijadualkan</SelectItem>
                            <SelectItem value="ongoing">Sedang Berlangsung</SelectItem>
                            <SelectItem value="completed">Selesai</SelectItem>
                            <SelectItem value="cancelled">Dibatalkan</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Select 
                        value={filters.sportFilter} 
                        onValueChange={(value) => onFiltersChange({ sportFilter: value })}
                    >
                        <SelectTrigger className="w-full sm:w-40">
                            <SelectValue placeholder="Semua Sukan" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Semua Sukan</SelectItem>
                            {uniqueSports.map((sport) => (
                                <SelectItem key={sport} value={sport}>
                                    {sport}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    
                    <Select 
                        value={filters.dateFilter} 
                        onValueChange={(value) => onFiltersChange({ dateFilter: value })}
                    >
                        <SelectTrigger className="w-full sm:w-40">
                            <SelectValue placeholder="Semua Tarikh" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Semua Tarikh</SelectItem>
                            <SelectItem value="today">Hari Ini</SelectItem>
                            <SelectItem value="tomorrow">Esok</SelectItem>
                            <SelectItem value="this_week">Minggu Ini</SelectItem>
                            <SelectItem value="next_week">Minggu Depan</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Select 
                        value={filters.venueFilter} 
                        onValueChange={(value) => onFiltersChange({ venueFilter: value })}
                    >
                        <SelectTrigger className="w-full sm:w-40">
                            <SelectValue placeholder="Semua Tempat" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Semua Tempat</SelectItem>
                            {uniqueVenues.map((venue) => (
                                <SelectItem key={venue} value={venue}>
                                    {venue}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    
                    <Select 
                        value={filters.zoneFilter} 
                        onValueChange={(value) => onFiltersChange({ zoneFilter: value })}
                    >
                        <SelectTrigger className="w-full sm:w-40">
                            <SelectValue placeholder="Semua Zon" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Semua Zon</SelectItem>
                            {uniqueZones.map((zone) => (
                                <SelectItem key={zone} value={zone}>
                                    Zon {zone}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>
        </div>
    );
}
