import { Calendar, Trophy, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MatchStats } from '@/types/matches';

interface MatchesStatsCardsProps {
    stats: MatchStats;
}

export default function MatchesStatsCards({ stats }: MatchesStatsCardsProps) {
    return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                        Akan Datang
                    </CardTitle>
                    <Calendar className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{String(stats.upcomingCount || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                        Perlawanan dijadualkan
                    </p>
                </CardContent>
            </Card>
            
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                        Sedang Berlangsung
                    </CardTitle>
                    <Trophy className="h-4 w-4 text-yellow-600" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{String(stats.ongoingCount || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                        Perlawanan aktif
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                        Selesai
                    </CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{String(stats.completedCount || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                        Keputusan direkod
                    </p>
                </CardContent>
            </Card>
            
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                        Jumlah Perlawanan
                    </CardTitle>
                    <Trophy className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">{String(stats.totalMatches || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                        Semua perlawanan
                    </p>
                </CardContent>
            </Card>
        </div>
    );
}
