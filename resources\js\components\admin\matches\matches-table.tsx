import { MapPin, Trophy, Edit, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { SportLogo } from '@/components/ui/logo-components';
import { Match } from '@/types/matches';

interface MatchesTableProps {
    matches: Match[];
    onSetResult: (match: Match) => void;
    onEditMatch: (match: Match) => void;
    onDeleteMatch: (match: Match) => void;
    userRole?: string;
}

export default function MatchesTable({ 
    matches, 
    onSetResult, 
    onEditMatch, 
    onDeleteMatch, 
    userRole 
}: MatchesTableProps) {
    const formatDateTime = (date: string, time: string) => {
        try {
            if (!date) return { date: 'N/A', time: 'N/A' };
            
            const matchDate = new Date(date);
            const formattedDate = matchDate.toLocaleDateString('ms-MY', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
            
            return {
                date: formattedDate,
                time: time || 'N/A'
            };
        } catch (error) {
            return { date: 'N/A', time: 'N/A' };
        }
    };

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            scheduled: { label: 'Dijadualkan', className: 'bg-blue-100 text-blue-800' },
            ongoing: { label: 'Sedang Berlangsung', className: 'bg-yellow-100 text-yellow-800' },
            completed: { label: 'Selesai', className: 'bg-green-100 text-green-800' },
            cancelled: { label: 'Dibatalkan', className: 'bg-red-100 text-red-800' }
        };
        
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.scheduled;
        
        return (
            <Badge className={config.className}>
                {config.label}
            </Badge>
        );
    };

    return (
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Tarikh & Masa</TableHead>
                    <TableHead>Perlawanan</TableHead>
                    <TableHead>Perlawanan</TableHead>
                    <TableHead>Tempat</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Tindakan</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {matches.map((match: Match) => {
                    const dateTime = formatDateTime(match.match_date, match.match_time);
                    return (
                        <TableRow
                            key={match.id}
                            className={match.is_highlighted ? 'bg-yellow-50 border-yellow-200' : ''}
                        >
                            <TableCell>
                                <div className="space-y-1">
                                    <div className="text-sm font-medium">
                                        {String(dateTime.date || 'N/A')}
                                    </div>
                                    <div className="text-xs text-gray-600">
                                        {String(dateTime.time || 'N/A')}
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-1">
                                    <div className="font-semibold text-gray-900">{String(match.title || 'N/A')}</div>
                                    <div className="flex items-center gap-2">
                                        <SportLogo
                                            sportName={String(match.sport?.name || '')}
                                            logoUrl={match.sport?.logo_url}
                                            className="w-4 h-4"
                                        />
                                        <span className="text-sm text-blue-600">{String(match.sport?.name || 'Unknown Sport')}</span>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell className="text-center">
                                <div className="space-y-1 flex flex-col items-center justify-center">
                                    {match.special_teams ? (
                                        <div className="font-medium text-purple-700">
                                            {String(match.special_teams)}
                                        </div>
                                    ) : match.event_type === 'group_event' || match.event_type === 'knockout_group' ? (
                                        <div className="text-center flex flex-col items-center justify-center">
                                            <div className="font-medium text-purple-700 mb-2">
                                                Knockout System
                                            </div>
                                            <div className="space-y-1">
                                                {(() => {
                                                    try {
                                                        if (!match.participating_zones) {
                                                            return <span className="text-gray-400">Group Event</span>;
                                                        }

                                                        const zones = typeof match.participating_zones === 'string'
                                                            ? JSON.parse(match.participating_zones)
                                                            : match.participating_zones;

                                                        if (!Array.isArray(zones) || zones.length === 0) {
                                                            return <span className="text-gray-400">Group Event</span>;
                                                        }

                                                        return (
                                                            <div className="flex flex-col gap-2">
                                                                {/* Single Maskot Icon */}
                                                                <div className="flex justify-center items-center">
                                                                    <div className="relative flex justify-center items-center">
                                                                        <img
                                                                            src="/images/maskot.png"
                                                                            alt="Maskot"
                                                                            className="w-16 h-16 object-contain mx-auto"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    } catch (e) {
                                                        return <span className="text-gray-400">Group Event</span>;
                                                    }
                                                })()}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center gap-4">
                                            {/* Check if this is a placeholder match */}
                                            {(match.zone_a === match.zone_b ||
                                              match.zone_a === 'TBD' ||
                                              match.zone_b === 'TBD' ||
                                              match.zone_a === 'W1' ||
                                              match.zone_b === 'W2' ||
                                              match.zone_a === 'W3' ||
                                              match.zone_b === 'W4' ||
                                              match.zone_a === 'Y' ||
                                              match.zone_b === 'Z' ||
                                              (match.bracket_round && (
                                                  match.bracket_round.toLowerCase().includes('final') ||
                                                  match.bracket_round.toLowerCase().includes('semi') ||
                                                  match.bracket_round.toLowerCase().includes('third')
                                              ))) ? (
                                                <div className="flex items-center justify-center gap-4 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                                                    <div className="text-center">
                                                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                                            <span className="text-gray-500 font-bold text-sm">
                                                                {match.zone_a === 'W1' ? 'W1' :
                                                                 match.zone_a === 'W2' ? 'W2' :
                                                                 match.zone_a === 'W3' ? 'W3' :
                                                                 match.zone_a === 'W4' ? 'W4' :
                                                                 match.zone_a === 'Y' ? 'Y' :
                                                                 'TBD'}
                                                            </span>
                                                        </div>
                                                        <span className="text-xs text-gray-500">
                                                            {match.zone_a.startsWith('W') ? 'Pemenang' :
                                                             match.zone_a === 'Y' || match.zone_a === 'Z' ? 'Peserta' :
                                                             'Menunggu'}
                                                        </span>
                                                    </div>
                                                    <span className="text-gray-400 text-lg font-bold">VS</span>
                                                    <div className="text-center">
                                                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                                            <span className="text-gray-500 font-bold text-sm">
                                                                {match.zone_b === 'W1' ? 'W1' :
                                                                 match.zone_b === 'W2' ? 'W2' :
                                                                 match.zone_b === 'W3' ? 'W3' :
                                                                 match.zone_b === 'W4' ? 'W4' :
                                                                 match.zone_b === 'Z' ? 'Z' :
                                                                 'TBD'}
                                                            </span>
                                                        </div>
                                                        <span className="text-xs text-gray-500">
                                                            {match.zone_b.startsWith('W') ? 'Pemenang' :
                                                             match.zone_b === 'Y' || match.zone_b === 'Z' ? 'Peserta' :
                                                             'Menunggu'}
                                                        </span>
                                                    </div>
                                                </div>
                                            ) : (
                                                <>
                                                    <div className="flex items-center justify-center">
                                                        <img
                                                            src="/images/maskot.png"
                                                            alt="Maskot"
                                                            className="w-16 h-16 object-contain mx-auto"
                                                        />
                                                    </div>
                                                </>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </TableCell>
                            <TableCell>
                                <div className="space-y-1">
                                    <div className="flex items-center gap-1">
                                        <MapPin className="h-3 w-3 text-gray-400" />
                                        <span className="text-sm">{String(match.venue || 'N/A')}</span>
                                    </div>
                                    {match.court_field && (
                                        <div className="text-xs text-gray-500">
                                            {String(match.court_field)}
                                        </div>
                                    )}
                                </div>
                            </TableCell>
                            <TableCell>
                                {getStatusBadge(match.status)}
                                {match.bracket_round && (
                                    <div className="mt-1">
                                        <Badge variant="outline" className="text-xs">
                                            {String(match.bracket_round)}
                                        </Badge>
                                    </div>
                                )}
                            </TableCell>
                            <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-2">
                                    {match.status === 'completed' ? (
                                        <div className="text-xs text-green-600 font-medium">
                                            Keputusan Direkod
                                        </div>
                                    ) : match.status !== 'cancelled' && (
                                        <Button
                                            size="sm"
                                            onClick={() => onSetResult(match)}
                                            className="bg-green-600 hover:bg-green-700 text-xs px-2"
                                        >
                                            <Trophy className="h-3 w-3 sm:mr-1" />
                                            <span className="hidden sm:inline">Set Keputusan</span>
                                        </Button>
                                    )}
                                    {(userRole === 'superadmin' || userRole === 'admin') && (
                                        <>
                                            <Button variant="outline" size="sm" onClick={() => onEditMatch(match)} className="px-2">
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onDeleteMatch(match)}
                                                className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </TableCell>
                        </TableRow>
                    );
                })}
            </TableBody>
        </Table>
    );
}
