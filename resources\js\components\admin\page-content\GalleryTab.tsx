import React from 'react';
import { GallerySectionEditor } from './sections';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface GalleryTabProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

const GalleryTab: React.FC<GalleryTabProps> = ({
    content,
    onUpdate
}) => {
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium">Gallery Content</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage all content for gallery page
                    </p>
                </div>
            </div>

            {/* Section Editors */}
            <div className="space-y-6">
                {/* Gallery Hero Section Editor */}
                <GallerySectionEditor
                    content={content || []}
                    onUpdate={onUpdate || (() => {})}
                />
            </div>
        </div>
    );
};

export default GalleryTab;
