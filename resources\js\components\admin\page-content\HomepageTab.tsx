import React from 'react';
import {
    HeroSectionEditor,
    SportsSectionEditor,
    CTASectionEditor
} from './sections';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface HomepageTabProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

const HomepageTab: React.FC<HomepageTabProps> = ({
    content,
    onUpdate
}) => {

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium">Homepage Content</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage all content for homepage sections
                    </p>
                </div>
            </div>

            {/* Section Editors */}
            <div className="space-y-6">
                {/* Hero Section Editor */}
                <HeroSectionEditor
                    content={content}
                    onUpdate={onUpdate || (() => {})}
                />

                {/* Sports Section Editor */}
                <SportsSectionEditor
                    content={content}
                    onUpdate={onUpdate || (() => {})}
                />

                {/* CTA Section Editor */}
                <CTASectionEditor
                    content={content}
                    onUpdate={onUpdate || (() => {})}
                />
            </div>
        </div>
    );
};

export default HomepageTab;
