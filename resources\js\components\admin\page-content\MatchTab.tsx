import React from 'react';
import { MatchScheduleSectionEditor } from './sections';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface MatchTabProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

const MatchTab: React.FC<MatchTabProps> = ({
    content,
    onUpdate
}) => {
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium">Match Schedule Content</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage all content for match schedule page
                    </p>
                </div>
            </div>

            {/* Section Editors */}
            <div className="space-y-6">
                {/* Match Schedule Hero Section Editor */}
                <MatchScheduleSectionEditor
                    content={content || []}
                    onUpdate={onUpdate || (() => {})}
                />
            </div>
        </div>
    );
};

export default MatchTab;
