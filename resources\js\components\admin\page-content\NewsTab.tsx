import React from 'react';
import { NewsSectionEditor } from './sections';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface NewsTabProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

const NewsTab: React.FC<NewsTabProps> = ({
    content,
    onUpdate
}) => {
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium">News Content</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage all content for news page
                    </p>
                </div>
            </div>

            {/* Section Editors */}
            <div className="space-y-6">
                {/* News Hero Section Editor */}
                <NewsSectionEditor
                    content={content || []}
                    onUpdate={onUpdate || (() => {})}
                />
            </div>
        </div>
    );
};

export default NewsTab;
