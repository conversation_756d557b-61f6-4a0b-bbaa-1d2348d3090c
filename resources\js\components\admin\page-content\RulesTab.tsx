import React from 'react';
import { RulesSectionEditor } from './sections';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

interface RulesTabProps {
    content: ContentItem[];
    onUpdate?: () => void;
}

const RulesTab: React.FC<RulesTabProps> = ({
    content,
    onUpdate
}) => {
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-medium">Rules Content</h3>
                    <p className="text-sm text-muted-foreground">
                        Manage all content for rules page
                    </p>
                </div>
            </div>

            {/* Section Editors */}
            <div className="space-y-6">
                {/* Rules Hero Section Editor */}
                <RulesSectionEditor
                    content={content || []}
                    onUpdate={onUpdate || (() => {})}
                />
            </div>
        </div>
    );
};

export default RulesTab;
