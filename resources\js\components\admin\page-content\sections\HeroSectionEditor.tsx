import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Save, Image, Upload, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
}

interface HeroSectionEditorProps {
    content: ContentItem[];
    onUpdate: () => void;
}

interface HeroFormData {
    title: string;
    subtitle: string;
    description: string;
    countdown_text: string;
    countdown_date: string;
    background_image: string;
}

const HeroSectionEditor: React.FC<HeroSectionEditorProps> = ({ content, onUpdate }) => {
    const [formData, setFormData] = useState<HeroFormData>({
        title: '',
        subtitle: '',
        description: '',
        countdown_text: '',
        countdown_date: '',
        background_image: ''
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);

    // Initialize form data
    useEffect(() => {
        // Get hero content
        const heroContent = content.filter(item => item.page === 'homepage' && item.section === 'hero');

        const getContentValue = (key: string) => {
            const item = heroContent.find(item => item.content_key === key);
            return item?.content_value || '';
        };

        setFormData({
            title: getContentValue('title'),
            subtitle: getContentValue('subtitle'),
            description: getContentValue('description'),
            countdown_text: getContentValue('countdown_text'),
            countdown_date: extractDateFromCountdown(getContentValue('countdown_text')),
            background_image: getContentValue('background_image')
        });
    }, [content]);

    // Extract date from countdown text (e.g., "21 - 25 JULAI 2025 | PU SENDAYAN" -> "2025-07-21")
    const extractDateFromCountdown = (countdownText: string): string => {
        const match = countdownText.match(/(\d{1,2})\s*-\s*\d{1,2}\s+\w+\s+(\d{4})/);
        if (match) {
            const day = match[1].padStart(2, '0');
            const year = match[2];
            // Assume July for "JULAI"
            return `${year}-07-${day}`;
        }
        return '2025-07-21'; // Default
    };

    // Format date for countdown text
    const formatCountdownText = (date: string): string => {
        if (!date) return formData.countdown_text;
        
        const dateObj = new Date(date);
        const day = dateObj.getDate();
        const month = dateObj.toLocaleDateString('ms-MY', { month: 'long' }).toUpperCase();
        const year = dateObj.getFullYear();
        
        // Assuming 5-day event
        const endDay = day + 4;
        return `${day} - ${endDay} ${month} ${year} | PU SENDAYAN`;
    };

    const handleInputChange = (key: keyof HeroFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [key]: value,
            // Auto-update countdown_text when date changes
            ...(key === 'countdown_date' ? { countdown_text: formatCountdownText(value) } : {})
        }));
    };

    const handleSave = async () => {
        setIsLoading(true);
        try {
            // Get hero content
            const heroContent = content.filter(item => item.page === 'homepage' && item.section === 'hero');

            // Update each field
            const updates = [
                { key: 'title', value: formData.title },
                { key: 'subtitle', value: formData.subtitle },
                { key: 'description', value: formData.description },
                { key: 'countdown_text', value: formData.countdown_text },
                { key: 'background_image', value: formData.background_image }
            ];

            for (const update of updates) {
                const existingItem = heroContent.find(item => item.content_key === update.key);
                if (existingItem) {
                    await api.put(`/cms/page-content/${existingItem.id}`, {
                        content_value: update.value
                    });
                }
            }

            toast.success('Hero section updated successfully!');
            onUpdate();
        } catch (error) {
            toast.error('Failed to update hero section');
        } finally {
            setIsLoading(false);
        }
    };



    // Function to compress image on client-side
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            // Determine output format based on original file type
            const originalType = file.type;
            const isPng = originalType === 'image/png';
            const outputType = isPng ? 'image/png' : 'image/jpeg';

            img.onload = () => {
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;

                // For PNG with transparency, fill with transparent background
                if (isPng && ctx) {
                    ctx.clearRect(0, 0, width, height);
                }

                // Draw and compress
                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: outputType,
                            lastModified: Date.now()
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, outputType, isPng ? 1.0 : quality); // PNG uses lossless compression
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                // Show loading state
                toast.loading('Compressing image...');

                // Compress image before setting
                const compressedFile = await compressImage(file);

                // Show compression result
                const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
                const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);

                toast.dismiss();
                toast.success(`Image compressed: ${originalSizeMB}MB → ${compressedSizeMB}MB`);

                setImageFile(compressedFile);
            } catch (error) {
                toast.dismiss();
                toast.error('Failed to compress image');

            }
        }
    };

    const handleImageSave = async () => {
        setIsUploading(true);
        try {
            let finalImageUrl = '';

            if (imageFile) {
                // Upload and process file
                const formData = new FormData();
                formData.append('image', imageFile);
                formData.append('section', 'hero');
                formData.append('width', '1920');
                formData.append('height', '1080');
                formData.append('quality', '80');

                const response = await api.post('/cms/upload-image', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });

                if (response.data.success) {
                    finalImageUrl = response.data.data.url;
                    toast.success(`Image uploaded! Size: ${response.data.data.file_size_kb}KB`);
                } else {
                    throw new Error(response.data.message);
                }
            }

            if (finalImageUrl) {
                // Update local state
                setFormData(prev => ({ ...prev, background_image: finalImageUrl }));

                // Save to database immediately
                const heroContent = content.filter(item => item.page === 'homepage' && item.section === 'hero');
                const backgroundImageItem = heroContent.find(item => item.content_key === 'background_image');

                if (backgroundImageItem) {
                    await api.put(`/cms/page-content/${backgroundImageItem.id}`, {
                        content_value: finalImageUrl
                    });
                    toast.success('Background image saved to database!');
                    onUpdate(); // Refresh parent component
                }

                setIsImageModalOpen(false);
                setImageFile(null);
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to process image');
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    Hero Section
                    <Badge variant="outline">4 fields</Badge>
                </CardTitle>
                <CardDescription>
                    Edit hero section content including title, subtitle, description, countdown, and background image
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Text Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) => handleInputChange('title', e.target.value)}
                            placeholder="SUKAN INTRA"
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="subtitle">Subtitle</Label>
                        <Input
                            id="subtitle"
                            value={formData.subtitle}
                            onChange={(e) => handleInputChange('subtitle', e.target.value)}
                            placeholder="KOR KESIHATAN DIRAJA 2025"
                        />
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Platform terpusat untuk menguruskan semua aktiviti sukan intra-zon..."
                        rows={3}
                    />
                </div>

                {/* Date and Countdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="countdown_date">Event Start Date</Label>
                        <Input
                            id="countdown_date"
                            type="date"
                            value={formData.countdown_date}
                            onChange={(e) => handleInputChange('countdown_date', e.target.value)}
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="countdown_text">Countdown Text (Auto-generated)</Label>
                        <Input
                            id="countdown_text"
                            value={formData.countdown_text}
                            onChange={(e) => handleInputChange('countdown_text', e.target.value)}
                            placeholder="21 - 25 JULAI 2025 | PU SENDAYAN"
                        />
                    </div>
                </div>

                {/* Background Image */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <Label className="text-lg font-semibold">Background Image</Label>
                        <Dialog open={isImageModalOpen} onOpenChange={setIsImageModalOpen}>
                            <DialogTrigger asChild>
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Image className="h-4 w-4" />
                                    Change Background
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                    <DialogTitle>Change Background Image</DialogTitle>
                                    <DialogDescription>
                                        Upload an image file. Images will be automatically optimized and compressed.
                                    </DialogDescription>
                                </DialogHeader>

                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="image-file">Upload Image</Label>
                                        <Input
                                            id="image-file"
                                            type="file"
                                            accept="image/*"
                                            onChange={handleFileChange}
                                        />
                                        <p className="text-sm text-muted-foreground">
                                            Recommended: 1920x1080px or larger. Images will be compressed automatically.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setIsImageModalOpen(false)}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleImageSave} disabled={!imageFile || isUploading}>
                                        {isUploading ? (
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        ) : (
                                            <Upload className="w-4 h-4 mr-2" />
                                        )}
                                        {isUploading ? 'Uploading...' : 'Save Image'}
                                    </Button>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>

                    {formData.background_image && (
                        <div className="border rounded-lg p-2">
                            <img
                                src={formData.background_image}
                                alt="Current background"
                                className="w-full h-32 object-cover rounded"
                            />
                            <p className="text-sm text-gray-500 mt-2">Current background image</p>
                        </div>
                    )}
                </div>

                {/* Save Button */}
                <div className="flex justify-end pt-4">
                    <Button onClick={handleSave} disabled={isLoading} className="flex items-center gap-2">
                        {isLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <Save className="h-4 w-4" />
                        )}
                        {isLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};

export default HeroSectionEditor;
