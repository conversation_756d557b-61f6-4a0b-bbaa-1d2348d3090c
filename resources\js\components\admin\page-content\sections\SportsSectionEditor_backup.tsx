import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Save, Image, Link, Upload, Loader2, Plus, Edit, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';

interface ContentItem {
    id: number;
    page: string;
    section: string;
    content_key: string;
    content_value: string;
    content_type: string;
    is_active: boolean;
    sort_order: number;
}

interface SportsSectionEditorProps {
    content: ContentItem[];
    onUpdate: () => void;
}

interface SportsFormData {
    title: string;
    subtitle: string;
    background_image: string;
}

interface Sport {
    id: string;
    name: string;
    icon: string;
    description: string;
    participants: string;
    venue: string;
}

interface SportFormData {
    name: string;
    icon: string;
    description: string;
    participants: string;
    venue: string;
}

const SportsSectionEditor: React.FC<SportsSectionEditorProps> = ({ content, onUpdate }) => {
    const [formData, setFormData] = useState<SportsFormData>({
        title: '',
        subtitle: '',
        background_image: ''
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [imageMethod, setImageMethod] = useState<'url' | 'upload'>('url');
    const [imageUrl, setImageUrl] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>('');

    // Sports management state
    const [sports, setSports] = useState<Sport[]>([]);
    const [isSportModalOpen, setIsSportModalOpen] = useState(false);
    const [editingSport, setEditingSport] = useState<Sport | null>(null);
    const [sportFormData, setSportFormData] = useState<SportFormData>({
        name: '',
        icon: '',
        description: '',
        participants: '',
        venue: ''
    });

    // Initialize form data
    useEffect(() => {
        // Get sports content
        const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');

        const getContentValue = (key: string) => {
            const item = sportsContent.find(item => item.content_key === key);
            return item?.content_value || '';
        };

        setFormData({
            title: getContentValue('title'),
            subtitle: getContentValue('subtitle'),
            background_image: getContentValue('background_image')
        });

        setImagePreview(getContentValue('background_image'));

        // Load sports data
        const sportsData = getContentValue('sports_data');
        if (sportsData) {
            try {
                const parsedSports = JSON.parse(sportsData);
                setSports(parsedSports);
            } catch (error) {

                setSports([]);
            }
        } else {
            // Initialize with default sports if no data exists
            const defaultSports = [
                {
                    id: 'football',
                    name: 'Bola Sepak',
                    icon: '/images/bolasepakpsd.png',
                    description: 'Pertandingan bola sepak antara zon dengan format 11 vs 11 pemain.',
                    participants: '22 pemain per perlawanan',
                    venue: 'Padang Bola Sepak Utama'
                },
                {
                    id: 'netball',
                    name: 'Bola Jaring',
                    icon: '/images/netballpsd.png',
                    description: 'Sukan bola jaring yang dimainkan oleh dua pasukan dengan 7 pemain setiap satu.',
                    participants: '7 pemain per pasukan',
                    venue: 'Gelanggang Bola Jaring'
                }
            ];
            setSports(defaultSports);
        }
    }, [content]);

    const handleInputChange = (key: keyof SportsFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSave = async () => {
        setIsLoading(true);
        try {
            // Get sports content
            const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');

            const updates = [
                { key: 'title', value: formData.title },
                { key: 'subtitle', value: formData.subtitle },
                { key: 'background_image', value: formData.background_image }
            ];

            for (const update of updates) {
                const existingItem = sportsContent.find(item => item.content_key === update.key);
                if (existingItem) {
                    await api.put(`/cms/page-content/${existingItem.id}`, {
                        content_value: update.value
                    });
                }
            }

            toast.success('Sports section updated successfully!');
            onUpdate();
        } catch (error) {
            toast.error('Failed to update sports section');
        } finally {
            setIsLoading(false);
        }
    };

    // Sports management functions
    const saveSportsData = async (updatedSports: Sport[]) => {
        try {
            const sportsContent = content.filter(item => item.page === 'homepage' && item.section === 'sports');
            const sportsDataItem = sportsContent.find(item => item.content_key === 'sports_data');
            
            const sportsDataValue = JSON.stringify(updatedSports);
            
            if (sportsDataItem) {
                await api.put(`/cms/page-content/${sportsDataItem.id}`, {
                    content_value: sportsDataValue
                });
            } else {
                // Create new sports_data entry
                await api.post('/cms/page-content', {
                    page: 'homepage',
                    section: 'sports',
                    content_key: 'sports_data',
                    content_value: sportsDataValue,
                    content_type: 'json',
                    description: 'Sports carousel data'
                });
            }
            
            setSports(updatedSports);
            onUpdate();
        } catch (error) {
            throw error;
        }
    };

    const handleAddSport = () => {
        setEditingSport(null);
        setSportFormData({
            name: '',
            icon: '',
            description: '',
            participants: '',
            venue: ''
        });
        setIsSportModalOpen(true);
    };

    const handleEditSport = (sport: Sport) => {
        setEditingSport(sport);
        setSportFormData({
            name: sport.name,
            icon: sport.icon,
            description: sport.description,
            participants: sport.participants,
            venue: sport.venue
        });
        setIsSportModalOpen(true);
    };

    const handleDeleteSport = async (sportId: string) => {
        if (confirm('Are you sure you want to delete this sport?')) {
            try {
                const updatedSports = sports.filter(sport => sport.id !== sportId);
                await saveSportsData(updatedSports);
                toast.success('Sport deleted successfully!');
            } catch (error) {
                toast.error('Failed to delete sport');
            }
        }
    };

    const handleSaveSport = async () => {
        try {
            setIsLoading(true);
            
            const sportData = {
                ...sportFormData,
                id: editingSport?.id || `sport_${Date.now()}`
            };
            
            let updatedSports;
            if (editingSport) {
                updatedSports = sports.map(sport => 
                    sport.id === editingSport.id ? sportData : sport
                );
            } else {
                updatedSports = [...sports, sportData];
            }
            
            await saveSportsData(updatedSports);
            
            toast.success(editingSport ? 'Sport updated successfully!' : 'Sport added successfully!');
            setIsSportModalOpen(false);
        } catch (error) {
            toast.error('Failed to save sport');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        Sports Categories Section
                        <Badge variant="outline">{sports.length} sports</Badge>
                    </CardTitle>
                    <CardDescription>
                        Edit sports categories section content including title, subtitle, background image, and manage sports carousel items
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Text Fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="sports-title">Title</Label>
                            <Input
                                id="sports-title"
                                value={formData.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                placeholder="KATEGORI SUKAN"
                            />
                        </div>
                        
                        <div className="space-y-2">
                            <Label htmlFor="sports-subtitle">Subtitle</Label>
                            <Textarea
                                id="sports-subtitle"
                                value={formData.subtitle}
                                onChange={(e) => handleInputChange('subtitle', e.target.value)}
                                placeholder="7 kategori sukan yang akan dipertandingkan..."
                                rows={2}
                            />
                        </div>
                    </div>

                    {/* Sports Management */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <Label className="text-lg font-semibold">Sports Carousel Items</Label>
                            <Button onClick={handleAddSport} className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Add Sport
                            </Button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {sports.map((sport) => (
                                <Card key={sport.id} className="relative">
                                    <CardContent className="p-4">
                                        <div className="flex items-start gap-3">
                                            {sport.icon && (
                                                <img
                                                    src={sport.icon}
                                                    alt={sport.name}
                                                    className="w-12 h-12 object-cover rounded"
                                                />
                                            )}
                                            <div className="flex-1 min-w-0">
                                                <h4 className="font-semibold text-sm truncate">{sport.name}</h4>
                                                <p className="text-xs text-gray-600 line-clamp-2">{sport.description}</p>
                                                <p className="text-xs text-gray-500 mt-1">{sport.participants}</p>
                                            </div>
                                        </div>
                                        
                                        <div className="flex justify-end gap-1 mt-3">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleEditSport(sport)}
                                                className="h-8 w-8 p-0"
                                            >
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleDeleteSport(sport.id)}
                                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                        
                        {sports.length === 0 && (
                            <div className="text-center py-8 text-gray-500">
                                <p>No sports added yet. Click "Add Sport" to get started.</p>
                            </div>
                        )}
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end pt-4">
                        <Button onClick={handleSave} disabled={isLoading} className="flex items-center gap-2">
                            {isLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                <Save className="h-4 w-4" />
                            )}
                            {isLoading ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Sport Add/Edit Modal */}
            <Dialog open={isSportModalOpen} onOpenChange={setIsSportModalOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {editingSport ? 'Edit Sport' : 'Add New Sport'}
                        </DialogTitle>
                        <DialogDescription>
                            {editingSport ? 'Update sport information' : 'Add a new sport to the carousel'}
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4">
                        {/* Sport Name */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-name">Sport Name</Label>
                            <Input
                                id="sport-name"
                                value={sportFormData.name}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="e.g., Bola Sepak"
                            />
                        </div>
                        
                        {/* Sport Description */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-description">Description</Label>
                            <Textarea
                                id="sport-description"
                                value={sportFormData.description}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, description: e.target.value }))}
                                placeholder="Brief description of the sport..."
                                rows={3}
                            />
                        </div>
                        
                        {/* Participants */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-participants">Participants</Label>
                            <Input
                                id="sport-participants"
                                value={sportFormData.participants}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, participants: e.target.value }))}
                                placeholder="e.g., 22 pemain per perlawanan"
                            />
                        </div>
                        
                        {/* Venue */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-venue">Venue</Label>
                            <Input
                                id="sport-venue"
                                value={sportFormData.venue}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, venue: e.target.value }))}
                                placeholder="e.g., Padang Bola Sepak Utama"
                            />
                        </div>
                        
                        {/* Sport Icon URL */}
                        <div className="space-y-2">
                            <Label htmlFor="sport-icon">Icon URL</Label>
                            <Input
                                id="sport-icon"
                                value={sportFormData.icon}
                                onChange={(e) => setSportFormData(prev => ({ ...prev, icon: e.target.value }))}
                                placeholder="https://example.com/icon.png"
                            />
                        </div>
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-4">
                        <Button variant="outline" onClick={() => setIsSportModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleSaveSport} disabled={isLoading}>
                            {isLoading ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    {editingSport ? 'Update Sport' : 'Add Sport'}
                                </>
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default SportsSectionEditor;
