import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface Sport {
    id: number;
    name: string;
}

interface AddedPlayer {
    name: string;
    email?: string;
    phone: string;
    zone: string;
    sports: number[];
    status: string;
}

interface AddedPlayersTableProps {
    addedPlayers: AddedPlayer[];
    sports: Sport[];
}

export default function AddedPlayersTable({ addedPlayers, sports }: AddedPlayersTableProps) {
    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Peserta Yang <PERSON></h3>
                <Badge variant="secondary">{addedPlayers.length} peserta</Badge>
            </div>

            <div className="border rounded-lg overflow-hidden">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Nama</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>No. Telefon</TableHead>
                            <TableHead>Zon</TableHead>
                            <TableHead>Sukan</TableHead>
                            <TableHead>Status</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {addedPlayers.length > 0 ? (
                            addedPlayers.map((player, index) => (
                                <TableRow key={`${player.name}-${player.phone}-${index}`}>
                                    <TableCell className="font-medium">{player.name}</TableCell>
                                    <TableCell>{player.email || '-'}</TableCell>
                                    <TableCell>{player.phone}</TableCell>
                                    <TableCell>
                                        <Badge variant="outline">
                                            {player.zone}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        {player.sports.map((sportId: number) => {
                                            const sport = sports.find(s => s.id === sportId);
                                            return sport ? (
                                                <Badge key={sportId} variant="secondary" className="mr-1">
                                                    {sport.name}
                                                </Badge>
                                            ) : null;
                                        })}
                                    </TableCell>
                                    <TableCell>
                                        <Badge 
                                            variant={player.status === 'active' ? 'default' : 'secondary'}
                                            className={player.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                                        >
                                            {player.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                                        </Badge>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={6} className="text-center text-gray-500 py-8">
                                    Belum ada peserta yang dimasukkan. Isi borang di atas untuk menambah peserta.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
