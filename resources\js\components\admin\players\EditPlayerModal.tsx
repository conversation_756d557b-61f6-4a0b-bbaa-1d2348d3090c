import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';
import { z } from 'zod';

interface Player {
    id: number;
    name: string;
    phone: string;
    status: string;
    created_at: string;
}

interface EditPlayerModalProps {
    player: Player | null;
    isOpen: boolean;
    onClose: () => void;
    onRefresh: () => void;
}

// Zod validation schema
const playerSchema = z.object({
    name: z.string()
        .min(1, '<PERSON><PERSON> diperlukan')
        .min(2, 'Nama mestilah sekurang-kurangnya 2 aksara'),
    player_type: z.enum(['main', 'substitute']),
    rank_category_id: z.number({
        required_error: 'Kategori pangkat diperlukan'
    }).min(1, 'Sila pilih kategori pangkat')
});

export default function EditPlayerModal({ player, isOpen, onClose, onRefresh }: EditPlayerModalProps) {
    const [loading, setLoading] = useState(false);
    const [playerForm, setPlayerForm] = useState({
        name: '',
        player_type: 'main' as 'main' | 'substitute',
        rank_category_id: 0
    });
    const [rankCategories, setRankCategories] = useState<any[]>([]);
    const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

    // Fetch rank categories
    const fetchRankCategories = async () => {
        try {
            const response = await api.get('/admin/rank-categories');
            setRankCategories(response.data || []);
        } catch (error) {
            setRankCategories([]); // Set empty array on error
            toast.error('Gagal memuat kategori pangkat');
        }
    };

    // Set form data when player changes
    useEffect(() => {
        if (player) {
            setPlayerForm({
                name: player.name,
                player_type: player.player_type || 'main',
                rank_category_id: player.rank_category_id || 0
            });
        }
    }, [player]);

    // Fetch rank categories when modal opens
    useEffect(() => {
        if (isOpen) {
            fetchRankCategories();
        }
    }, [isOpen]);

    const validateForm = () => {
        try {
            playerSchema.parse(playerForm);
            setValidationErrors({});
            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const errors: { [key: string]: string } = {};
                error.issues.forEach((err) => {
                    if (err.path[0]) {
                        errors[err.path[0] as string] = err.message;
                    }
                });
                setValidationErrors(errors);
            }
            return false;
        }
    };

    const handleSave = async () => {
        if (!validateForm() || !player) return;

        setLoading(true);
        try {
            await api.put(`/admin/players/${player.id}`, {
                name: playerForm.name.trim(),
                player_type: playerForm.player_type,
                rank_category_id: playerForm.rank_category_id,
            });

            toast.success('Pemain berjaya dikemaskini');
            await onRefresh();
            onClose();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal mengemaskini pemain');
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async () => {
        if (!player) return;

        if (!confirm('Adakah anda pasti ingin memadamkan pemain ini?')) {
            return;
        }

        setLoading(true);
        try {
            await api.delete(`/admin/players/${player.id}`);
            toast.success('Pemain berjaya dipadamkan');
            onRefresh();
            onClose();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal memadamkan pemain');
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setPlayerForm({ name: '', player_type: 'main', rank_category_id: 0 });
        setValidationErrors({});
        onClose();
    };

    if (!player) return null;

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-3">
                        <Edit className="w-6 h-6 text-blue-600" />
                        Edit Pemain
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="edit_name">Nama Penuh *</Label>
                        <Input
                            id="edit_name"
                            value={playerForm.name}
                            onChange={(e) => setPlayerForm(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Nama penuh pemain"
                            className={validationErrors.name ? 'border-red-500' : ''}
                        />
                        {validationErrors.name && (
                            <p className="text-sm text-red-600">{validationErrors.name}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="edit_rank_category">Kategori Pangkat *</Label>
                        <Select
                            value={playerForm.rank_category_id > 0 ? playerForm.rank_category_id.toString() : ""}
                            onValueChange={(value) => setPlayerForm(prev => ({ ...prev, rank_category_id: parseInt(value) }))}
                        >
                            <SelectTrigger className={`w-full ${validationErrors.rank_category_id ? 'border-red-500' : ''}`}>
                                <SelectValue placeholder="Pilih kategori pangkat" />
                            </SelectTrigger>
                            <SelectContent>
                                {rankCategories.map((category) => (
                                    <SelectItem key={category.id} value={category.id.toString()}>
                                        {category.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {validationErrors.rank_category_id && (
                            <p className="text-sm text-red-600">{validationErrors.rank_category_id}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="edit_player_type">Jenis Pemain *</Label>
                        <Select
                            value={playerForm.player_type}
                            onValueChange={(value: 'main' | 'substitute') => 
                                setPlayerForm(prev => ({ ...prev, player_type: value }))
                            }
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Pilih jenis pemain" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="main">Pemain Utama</SelectItem>
                                <SelectItem value="substitute">Pemain Simpanan</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Player Info */}
                    <div className="space-y-2">
                        <Label>Maklumat Pemain</Label>
                        <div className="p-3 bg-gray-50 rounded-lg space-y-1">
                            <p className="text-sm"><strong>Status:</strong> 
                                <Badge variant="outline" className="ml-2">
                                    {player.status}
                                </Badge>
                            </p>
                            <p className="text-sm"><strong>Didaftarkan:</strong> {new Date(player.created_at).toLocaleDateString('ms-MY')}</p>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="flex justify-between pt-4 border-t">
                    <Button 
                        variant="outline" 
                        onClick={handleDelete}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700"
                    >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Padam
                    </Button>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={handleClose}>
                            Batal
                        </Button>
                        <Button onClick={handleSave} disabled={loading}>
                            {loading ? 'Menyimpan...' : 'Simpan'}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
