import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle, RotateCcw, X, Save, Loader2 } from 'lucide-react';

interface Sport {
    id: number;
    name: string;
}

interface User {
    role: string;
    zone?: string;
}

interface FormData {
    name: string;
    email: string;
    phone: string;
    zone: string;
    sports: number[];
    status: string;
    team_leader_name: string;
    team_leader_phone: string;
}

interface ValidationErrors {
    [key: string]: string;
}

interface PlayerFormProps {
    formData: FormData;
    validationErrors: ValidationErrors;
    sports: Sport[];
    user: User | null;
    editingPlayer: any;
    isSubmitting: boolean;
    addedPlayers: any[];
    handleInputChange: (field: string, value: any) => void;
    handleSubmit: (e: React.FormEvent) => void;
    handleBatchSave: () => void;
    resetForm: () => void;
    closeModal: () => void;
}

export default function PlayerForm({
    formData,
    validationErrors,
    sports,
    user,
    editingPlayer,
    isSubmitting,
    addedPlayers,
    handleInputChange,
    handleSubmit,
    handleBatchSave,
    resetForm,
    closeModal
}: PlayerFormProps) {
    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* Sports Selection - First Priority */}
            <div className="space-y-4 bg-blue-50 p-4 rounded-lg border-2 border-blue-200">
                <h3 className="text-lg font-medium text-blue-800">Pilih Sukan Terlebih Dahulu</h3>
                <p className="text-sm text-blue-600">Pilih sukan yang ingin didaftarkan, kemudian masukkan maklumat peserta</p>

                <div className="space-y-2">
                    <Label>Sukan Disertai *</Label>
                    <select
                        value={formData.sports.length > 0 ? formData.sports[0].toString() : ''}
                        onChange={(e) => {
                            const sportId = parseInt(e.target.value);
                            if (sportId) {
                                handleInputChange('sports', [sportId]);
                            }
                        }}
                        className={`flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${validationErrors.sports ? 'border-red-500' : ''}`}
                    >
                        <option value="">Pilih sukan untuk didaftarkan</option>
                        {sports.map((sport) => (
                            <option key={sport.id} value={sport.id.toString()}>
                                {sport.name}
                            </option>
                        ))}
                    </select>
                    {validationErrors.sports && (
                        <p className="text-sm text-red-500">{validationErrors.sports}</p>
                    )}
                    {formData.sports.length > 0 && (
                        <div className="flex items-center gap-2 text-sm text-green-700 bg-green-50 p-2 rounded">
                            <CheckCircle className="h-4 w-4" />
                            Sukan Dipilih: {sports.find(s => s.id === formData.sports[0])?.name}
                        </div>
                    )}
                </div>
            </div>

            {/* Player Information */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">Maklumat Peserta</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">Nama Penuh *</Label>
                        <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            placeholder="Masukkan nama penuh"
                            className={validationErrors.name ? 'border-red-500' : ''}
                        />
                        {validationErrors.name && (
                            <p className="text-sm text-red-500">{validationErrors.name}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email">Email (Pilihan)</Label>
                        <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            placeholder="<EMAIL> (pilihan)"
                            className={validationErrors.email ? 'border-red-500' : ''}
                        />
                        {validationErrors.email && (
                            <p className="text-sm text-red-500">{validationErrors.email}</p>
                        )}
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="phone">No. Telefon *</Label>
                    <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="012345678"
                        className={validationErrors.phone ? 'border-red-500' : ''}
                    />
                    {validationErrors.phone && (
                        <p className="text-sm text-red-500">{validationErrors.phone}</p>
                    )}
                </div>
            </div>

            {/* Zone & Status */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">Zon & Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label>Zon *</Label>
                        <select
                            value={formData.zone}
                            onChange={(e) => handleInputChange('zone', e.target.value)}
                            disabled={user?.role === 'zone'}
                            className={`flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${validationErrors.zone ? 'border-red-500' : ''}`}
                        >
                            <option value="">Pilih zon</option>
                            <option value="Zon A">Zon A</option>
                            <option value="Zon B">Zon B</option>
                            <option value="Zon C">Zon C</option>
                        </select>
                        {validationErrors.zone && (
                            <p className="text-sm text-red-500">{validationErrors.zone}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label>Status</Label>
                        <select
                            value={formData.status}
                            onChange={(e) => handleInputChange('status', e.target.value)}
                            className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            <option value="">Pilih status</option>
                            <option value="active">Aktif</option>
                            <option value="inactive">Tidak Aktif</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Team Leader Information */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">Maklumat Ketua Pasukan</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="team_leader_name">Nama Ketua Pasukan</Label>
                        <Input
                            id="team_leader_name"
                            value={formData.team_leader_name}
                            onChange={(e) => handleInputChange('team_leader_name', e.target.value)}
                            placeholder="Nama ketua pasukan"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="team_leader_phone">No. Telefon Ketua Pasukan</Label>
                        <Input
                            id="team_leader_phone"
                            value={formData.team_leader_phone}
                            onChange={(e) => handleInputChange('team_leader_phone', e.target.value)}
                            placeholder="012345678"
                        />
                    </div>
                </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-between pt-4 border-t">
                <div className="flex gap-2">
                    {!editingPlayer && (
                        <Button
                            type="button"
                            variant="outline"
                            onClick={resetForm}
                            disabled={isSubmitting}
                        >
                            <RotateCcw className="w-4 h-4 mr-2" />
                            Reset Form
                        </Button>
                    )}
                </div>
                <div className="flex gap-2">
                    {!editingPlayer && addedPlayers.length > 0 && (
                        <Button
                            type="button"
                            onClick={handleBatchSave}
                            disabled={isSubmitting}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            {isSubmitting ? (
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                                <Save className="w-4 h-4 mr-2" />
                            )}
                            Simpan Semua ({addedPlayers.length})
                        </Button>
                    )}
                    <Button type="button" variant="outline" onClick={closeModal} disabled={isSubmitting}>
                        <X className="w-4 h-4 mr-2" />
                        {editingPlayer ? 'Batal' : 'Tutup'}
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                            <Save className="w-4 h-4 mr-2" />
                        )}
                        {editingPlayer ? 'Kemaskini' : 'Simpan & Tambah Lagi'}
                    </Button>
                </div>
            </div>
        </form>
    );
}
