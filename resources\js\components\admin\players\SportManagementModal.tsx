import React, { useState, useEffect } from 'react';
import { <PERSON>alog, DialogHeader, DialogTitle, DialogOverlay, DialogPortal } from '@/components/ui/dialog';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { XIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserCheck, UserPlus, Edit, Trash2, Users, Crown, ChevronDown, ChevronUp } from 'lucide-react';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';
import EditPlayerModal from './EditPlayerModal';
import { useAuth } from '@/contexts/auth-context';

// Zod validation schema for zone-based player form
const playerFormSchema = z.object({
    name: z.string()
        .min(1, 'Nama diperlukan')
        .min(2, 'Nama mestilah sekurang-kurangnya 2 aksara')
        .max(255, 'Nama tidak boleh melebihi 255 aksara'),
    player_type: z.enum(['main', 'substitute'], {
        required_error: 'Jenis pemain diperlukan'
    }),
    rank_category_id: z.number({
        required_error: 'Kategori pangkat diperlukan'
    }).min(1, 'Sila pilih kategori pangkat')
});

// Zod validation schema for rank-based player form
const rankPlayerFormSchema = z.object({
    name: z.string()
        .min(1, 'Nama diperlukan')
        .min(2, 'Nama mestilah sekurang-kurangnya 2 aksara')
        .max(255, 'Nama tidak boleh melebihi 255 aksara'),
    rank_category_id: z.number({
        required_error: 'Kategori pangkat diperlukan'
    }).min(1, 'Sila pilih kategori pangkat')
});

// Custom wide dialog content for sports management
function WideDialogContent({
    className,
    children,
    ...props
}: React.ComponentProps<typeof DialogPrimitive.Content>) {
    return (
        <DialogPortal>
            <DialogOverlay />
            <DialogPrimitive.Content
                className={cn(
                    "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 flex flex-col w-full max-w-[95vw] sm:max-w-[90vw] lg:max-w-[85vw] xl:max-w-[80vw] max-h-[95vh] translate-x-[-50%] translate-y-[-50%] rounded-lg border shadow-lg duration-200",
                    className
                )}
                {...props}
            >
                {children}
                <DialogPrimitive.Close className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
                    <XIcon />
                    <span className="sr-only">Close</span>
                </DialogPrimitive.Close>
            </DialogPrimitive.Content>
        </DialogPortal>
    );
}

interface RankCategory {
    id: number;
    name: string;
    code: string;
    players_count: number;
    team_leader_name?: string | null;
    team_leader_phone?: string | null;
    team_leader_contact?: string | null;
    has_leader: boolean;
}

interface SportTeam {
    id: number;
    name: string;
    icon: string | null;
    sport_type: 'zone_based' | 'rank_based';
    sport_team?: {
        id: number;
        team_leader_name: string;
        team_leader_phone: string;
        team_leader_contact: string | null;
    } | null;
    team_leader_name?: string | null;
    main_players_count?: number;
    substitute_players_count?: number;
    rank_categories?: RankCategory[];
    total_players: number;
    can_manage: boolean;
}

interface Player {
    id: number;
    name: string;
    phone: string;
    status: string;
    player_type: 'main' | 'substitute';
    rank_category_id?: number;
    rank_category?: {
        id: number;
        name: string;
        code: string;
    };
    created_at: string;
}

interface SportDetails {
    sport: {
        id: number;
        name: string;
        icon: string | null;
        required_players: number;
        max_substitute_players: number;
    };
    team_leader: {
        id: number;
        name: string;
        phone: string;
        contact: string | null;
    } | null;
    main_players: Player[];
    substitute_players: Player[];
}

interface SportManagementModalProps {
    sport: SportTeam | null;
    isOpen: boolean;
    onClose: () => void;
    onRefresh: () => void;
}

export default function SportManagementModal({ sport, isOpen, onClose, onRefresh }: SportManagementModalProps) {
    const { user } = useAuth();
    const [loading, setLoading] = useState(false);
    const [sportDetails, setSportDetails] = useState<SportDetails | null>(null);
    const [activeTab, setActiveTab] = useState('team-leader');
    const [isEditingLeader, setIsEditingLeader] = useState(false);
    const [isEditPlayerModalOpen, setIsEditPlayerModalOpen] = useState(false);
    const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
    const [isDeletePlayerModalOpen, setIsDeletePlayerModalOpen] = useState(false);
    const [playerToDelete, setPlayerToDelete] = useState<Player | null>(null);
    const [showMainPlayerForm, setShowMainPlayerForm] = useState(false);
    const [showSubstitutePlayerForm, setShowSubstitutePlayerForm] = useState(false);
    const [leaderForm, setLeaderForm] = useState({
        team_leader_name: '',
        team_leader_phone: '',
        team_leader_contact: '',
    });
    const [playerForm, setPlayerForm] = useState({
        name: '',
        player_type: 'main' as 'main' | 'substitute',
        rank_category_id: 0
    });
    const [rankPlayerForm, setRankPlayerForm] = useState({
        name: '',
        rank_category_id: 0
    });
    const [rankCategories, setRankCategories] = useState<any[]>([]);
    const [selectedRankCategory, setSelectedRankCategory] = useState<number | null>(null);
    const [rankCategoryPlayers, setRankCategoryPlayers] = useState<{ [key: number]: Player[] }>({});
    const [editingRankCategoryLeader, setEditingRankCategoryLeader] = useState<number | null>(null);
    const [rankCategoryLeaderForm, setRankCategoryLeaderForm] = useState({
        team_leader_name: '',
        team_leader_phone: '',
        team_leader_contact: '',
    });
    const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

    // Fetch rank categories
    const fetchRankCategories = async () => {
        try {
            const response = await api.get('/admin/rank-categories');
            setRankCategories(response.data || []);
        } catch (error) {
            setRankCategories([]); // Set empty array on error
            toast.error('Gagal memuat kategori pangkat');
        }
    };

    // Fetch sport details when modal opens and clear form state
    useEffect(() => {
        if (isOpen && sport) {
            // Always fetch rank categories for dropdown
            fetchRankCategories();

            if (sport.sport_type === 'rank_based') {
                fetchRankCategoryPlayers();
                setActiveTab('rank-categories');
            } else {
                fetchSportDetails();
                setActiveTab('team-leader');
            }

            // Clear all form states when modal opens or sport changes
            setPlayerForm({ name: '', player_type: 'main', rank_category_id: 0 });
            setRankPlayerForm({ name: '', rank_category_id: 0 });
            setLeaderForm({
                team_leader_name: '',
                team_leader_phone: '',
                team_leader_contact: '',
            });
            setRankCategoryLeaderForm({
                team_leader_name: '',
                team_leader_phone: '',
                team_leader_contact: '',
            });
            setValidationErrors({});
            setShowMainPlayerForm(false);
            setShowSubstitutePlayerForm(false);
            setIsEditingLeader(false);
            setEditingRankCategoryLeader(null);
            setSelectedRankCategory(null);
        }
    }, [isOpen, sport]);

    const fetchSportDetails = async () => {
        if (!sport) return;

        setLoading(true);
        try {
            // For rank-based sports, we don't need to fetch sport details
            // as they don't have zone-based team leaders
            if (sport.sport_type === 'rank_based') {
                setSportDetails(null);
                setLoading(false);
                return;
            }

            const response = await api.get(`/admin/sport-teams/${sport.id}/details`);
            setSportDetails(response.data);

            // Set leader form data if leader exists
            if (response.data.team_leader) {
                setLeaderForm({
                    team_leader_name: response.data.team_leader.name,
                    team_leader_phone: response.data.team_leader.phone,
                    team_leader_contact: response.data.team_leader.contact || '',
                });
            }
        } catch (error) {
            toast.error('Gagal memuat data pasukan');
        } finally {
            setLoading(false);
        }
    };



    const fetchRankCategoryPlayers = async () => {
        if (!sport) return;

        try {
            const response = await api.get(`/admin/players?sport_id=${sport.id}&rank_based=true`);
            const playersData = response.data.data || [];

            // Group players by rank category
            const groupedPlayers: { [key: number]: Player[] } = {};
            playersData.forEach((player: any) => {
                // Check if player has rank_category and get the ID
                const rankCategoryId = player.rank_category?.id || player.rank_category_id;

                if (rankCategoryId) {
                    if (!groupedPlayers[rankCategoryId]) {
                        groupedPlayers[rankCategoryId] = [];
                    }

                    // Transform player data to include rank_category_id for consistency
                    const transformedPlayer = {
                        ...player,
                        rank_category_id: rankCategoryId
                    };

                    groupedPlayers[rankCategoryId].push(transformedPlayer);
                }
            });

            setRankCategoryPlayers(groupedPlayers);
        } catch (error) {
            toast.error('Gagal memuat data peserta');
        }
    };

    const handleSaveLeader = async () => {
        if (!sport || !leaderForm.team_leader_name || !leaderForm.team_leader_phone) {
            toast.error('Sila lengkapkan maklumat ketua pasukan');
            return;
        }

        setLoading(true);
        try {
            const payload = {
                zone_code: sportDetails?.team_leader?.id ? undefined : user?.zone, // Use current user's zone
                sport_id: sport.id,
                ...leaderForm,
            };

            if (sportDetails?.team_leader?.id) {
                // Update existing team leader
                await api.put(`/admin/sport-teams/${sportDetails.team_leader.id}`, leaderForm);
            } else {
                // Create new team leader
                await api.post('/admin/sport-teams', payload);
            }

            toast.success('Ketua pasukan berjaya disimpan');
            setIsEditingLeader(false);
            fetchSportDetails();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal menyimpan ketua pasukan');
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteLeader = async () => {
        if (!sportDetails?.team_leader?.id) return;

        if (!confirm('Adakah anda pasti ingin memadamkan ketua pasukan? Semua peserta dalam pasukan ini akan turut dipadamkan.')) {
            return;
        }

        setLoading(true);
        try {
            await api.delete(`/admin/sport-teams/${sportDetails.team_leader.id}`);
            toast.success('Ketua pasukan berjaya dipadamkan');
            fetchSportDetails();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal memadamkan ketua pasukan');
        } finally {
            setLoading(false);
        }
    };

    const handleDeletePlayer = async () => {
        if (!playerToDelete) return;

        setLoading(true);
        try {
            await api.delete(`/admin/players/${playerToDelete.id}`);
            toast.success('Pemain berjaya dipadamkan');
            setIsDeletePlayerModalOpen(false);
            setPlayerToDelete(null);
            fetchSportDetails();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal memadamkan pemain');
        } finally {
            setLoading(false);
        }
    };

    const validatePlayerForm = () => {
        try {
            playerFormSchema.parse(playerForm);
            setValidationErrors({});
            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const errors: { [key: string]: string } = {};
                error.issues.forEach((err) => {
                    if (err.path) {
                        errors[err.path[0]] = err.message;
                    }
                });
                setValidationErrors(errors);
            }
            return false;
        }
    };

    const validateRankPlayerForm = () => {
        try {
            rankPlayerFormSchema.parse(rankPlayerForm);
            setValidationErrors({});
            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const errors: { [key: string]: string } = {};
                error.issues.forEach((err) => {
                    if (err.path) {
                        errors[err.path[0]] = err.message;
                    }
                });
                setValidationErrors(errors);
            }
            return false;
        }
    };

    const handleSaveRankPlayer = async () => {
        if (!validateRankPlayerForm()) {
            return;
        }

        if (!sport) {
            toast.error('Data sukan tidak dijumpai');
            return;
        }

        setLoading(true);
        try {
            await api.post('/admin/players/rank-based', {
                sport_id: sport.id,
                name: rankPlayerForm.name.trim(),
                rank_category_id: rankPlayerForm.rank_category_id
            });

            toast.success('Peserta berjaya ditambah');
            setRankPlayerForm({ name: '', rank_category_id: 0 });
            fetchRankCategoryPlayers();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal menambah peserta');
        } finally {
            setLoading(false);
        }
    };

    const handleSaveRankCategoryLeader = async () => {
        if (!sport || !editingRankCategoryLeader) {
            toast.error('Data tidak lengkap');
            return;
        }

        if (!rankCategoryLeaderForm.team_leader_name || !rankCategoryLeaderForm.team_leader_phone) {
            toast.error('Sila lengkapkan nama dan no. telefon ketua pasukan');
            return;
        }

        setLoading(true);
        try {
            // Check if leader already exists
            const category = rankCategories.find(cat => cat.id === editingRankCategoryLeader);

            if (category?.has_leader) {
                // Update existing leader - need to find the team ID first
                const existingTeam = await api.get(`/admin/rank-category-teams/${sport.id}/${editingRankCategoryLeader}`);
                await api.put(`/admin/rank-category-teams/${existingTeam.data.id}`, rankCategoryLeaderForm);
            } else {
                // Create new leader
                await api.post('/admin/rank-category-teams', {
                    sport_id: sport.id,
                    rank_category_id: editingRankCategoryLeader,
                    ...rankCategoryLeaderForm,
                });
            }

            toast.success('Ketua pasukan berjaya disimpan');
            setEditingRankCategoryLeader(null);
            setRankCategoryLeaderForm({
                team_leader_name: '',
                team_leader_phone: '',
                team_leader_contact: '',
            });
            // Refresh both rank categories and parent data
            fetchRankCategories();
            fetchRankCategoryPlayers();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal menyimpan ketua pasukan');
        } finally {
            setLoading(false);
        }
    };

    const handleSavePlayer = async () => {
        if (!validatePlayerForm()) {
            return;
        }

        if (!sportDetails?.team_leader) {
            toast.error('Ketua pasukan perlu dilantik terlebih dahulu');
            return;
        }

        setLoading(true);
        try {
            await api.post('/admin/players', {
                sport_team_id: sportDetails.team_leader.id,
                name: playerForm.name.trim(),
                player_type: playerForm.player_type,
                rank_category_id: playerForm.rank_category_id
            });

            toast.success('Pemain berjaya ditambah');
            setPlayerForm({ name: '', player_type: 'main', rank_category_id: 0 });
            setShowMainPlayerForm(false);
            setShowSubstitutePlayerForm(false);
            fetchSportDetails();
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal menambah pemain');
        } finally {
            setLoading(false);
        }
    };

    const handleCancelAddPlayer = () => {
        setPlayerForm({ name: '', player_type: 'main', rank_category_id: 0 });
        setValidationErrors({});
        setShowMainPlayerForm(false);
        setShowSubstitutePlayerForm(false);
    };

    if (!sport) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <WideDialogContent>
                {/* Fixed Header */}
                <div className="flex-shrink-0 p-6 pb-0 border-b">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-3">
                            {sport.icon ? (
                                <img src={sport.icon} alt={sport.name} className="w-8 h-8 object-contain" />
                            ) : (
                                <Users className="w-8 h-8 text-gray-500" />
                            )}
                            Pengurusan {sport.name}
                        </DialogTitle>
                    </DialogHeader>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto p-6" style={{ maxHeight: 'calc(95vh - 120px)' }}>
                    {loading && !sportDetails ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    ) : (
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                        {sport?.sport_type === 'rank_based' ? (
                            <TabsList className="grid w-full grid-cols-1">
                                <TabsTrigger value="rank-categories" className="flex items-center gap-2">
                                    <Users className="w-4 h-4" />
                                    Kategori Pangkat ({sport?.total_players || 0} peserta)
                                </TabsTrigger>
                            </TabsList>
                        ) : (
                            <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="team-leader" className="flex items-center gap-2">
                                    <Crown className="w-4 h-4" />
                                    Ketua Pasukan
                                </TabsTrigger>
                                <TabsTrigger value="main-players" className="flex items-center gap-2">
                                    <Users className="w-4 h-4" />
                                    Pemain Utama ({sportDetails?.main_players.length || 0})
                                </TabsTrigger>
                                <TabsTrigger value="substitute-players" className="flex items-center gap-2">
                                    <UserPlus className="w-4 h-4" />
                                    Pemain Simpanan ({sportDetails?.substitute_players.length || 0})
                                </TabsTrigger>
                            </TabsList>
                        )}

                        {/* Rank Categories Tab */}
                        <TabsContent value="rank-categories" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pengurusan Peserta Berdasarkan Kategori Pangkat</CardTitle>
                                    <CardDescription>
                                        Sukan {sport?.name} menggunakan sistem kategori pangkat. Pilih kategori untuk menguruskan peserta.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Add Player Form */}
                                    <div className="border rounded-lg p-4 bg-blue-50">
                                        <h4 className="font-medium text-blue-900 mb-3">Tambah Peserta Baru</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                                            <div>
                                                <Label htmlFor="rank-player-name">Nama Peserta</Label>
                                                <Input
                                                    id="rank-player-name"
                                                    value={rankPlayerForm.name}
                                                    onChange={(e) => setRankPlayerForm(prev => ({ ...prev, name: e.target.value }))}
                                                    placeholder="Nama penuh"
                                                    className={validationErrors.name ? 'border-red-500' : ''}
                                                />
                                                {validationErrors.name && (
                                                    <p className="text-red-500 text-xs mt-1">{validationErrors.name}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="rank-category">Kategori Pangkat</Label>
                                                <select
                                                    id="rank-category"
                                                    value={rankPlayerForm.rank_category_id}
                                                    onChange={(e) => setRankPlayerForm(prev => ({ ...prev, rank_category_id: parseInt(e.target.value) }))}
                                                    className={`w-full px-3 py-2 border rounded-md ${validationErrors.rank_category_id ? 'border-red-500' : 'border-gray-300'}`}
                                                >
                                                    <option value={0}>Pilih kategori</option>
                                                    {rankCategories.map(category => (
                                                        <option key={category.id} value={category.id}>
                                                            {category.name}
                                                        </option>
                                                    ))}
                                                </select>
                                                {validationErrors.rank_category_id && (
                                                    <p className="text-red-500 text-xs mt-1">{validationErrors.rank_category_id}</p>
                                                )}
                                            </div>
                                            <div className="flex items-end">
                                                <Button
                                                    onClick={handleSaveRankPlayer}
                                                    disabled={loading}
                                                    className="w-full"
                                                >
                                                    {loading ? 'Menyimpan...' : 'Tambah Peserta'}
                                                </Button>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Categories Table */}
                                    <div className="border rounded-lg overflow-hidden">
                                        <table className="w-full">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Kategori Pangkat</th>
                                                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Ketua Pasukan</th>
                                                    <th className="px-4 py-3 text-center text-sm font-medium text-gray-900">Peserta</th>
                                                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-900">Tindakan</th>
                                                </tr>
                                            </thead>
                                            <tbody className="divide-y divide-gray-200">
                                                {rankCategories.map(category => {
                                                    const categoryPlayers = rankCategoryPlayers[category.id] || [];
                                                    return (
                                                        <React.Fragment key={category.id}>
                                                            <tr className="hover:bg-gray-50">
                                                            {/* Category Name */}
                                                            <td className="px-4 py-3">
                                                                <div className="font-medium text-gray-900">{category.name}</div>
                                                                <div className="text-sm text-gray-500">{category.code}</div>
                                                            </td>

                                                            {/* Team Leader */}
                                                            <td className="px-4 py-3">
                                                                {category.has_leader ? (
                                                                    <div>
                                                                        <div className="font-medium text-green-700">{category.team_leader_name}</div>
                                                                        <div className="text-sm text-gray-500">📞 {category.team_leader_phone}</div>
                                                                    </div>
                                                                ) : (
                                                                    <span className="text-orange-600 text-sm">⚠️ Belum dilantik</span>
                                                                )}
                                                            </td>

                                                            {/* Players Count */}
                                                            <td className="px-4 py-3 text-center">
                                                                <button
                                                                    onClick={() => setSelectedRankCategory(selectedRankCategory === category.id ? null : category.id)}
                                                                    className="inline-flex items-center gap-1 hover:bg-blue-50 rounded-md px-2 py-1 transition-colors"
                                                                >
                                                                    <Badge variant="outline" className="text-blue-600">
                                                                        {categoryPlayers.length}
                                                                    </Badge>
                                                                    {selectedRankCategory === category.id ? (
                                                                        <ChevronUp className="w-4 h-4 text-blue-600" />
                                                                    ) : (
                                                                        <ChevronDown className="w-4 h-4 text-blue-600" />
                                                                    )}
                                                                </button>
                                                            </td>

                                                            {/* Actions */}
                                                            <td className="px-4 py-3 text-right">
                                                                <div className="flex items-center justify-end gap-2">
                                                                {category.has_leader ? (
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => {
                                                                            setEditingRankCategoryLeader(category.id);
                                                                            setRankCategoryLeaderForm({
                                                                                team_leader_name: category.team_leader_name || '',
                                                                                team_leader_phone: category.team_leader_phone || '',
                                                                                team_leader_contact: category.team_leader_contact || '',
                                                                            });
                                                                        }}
                                                                    >
                                                                        <Edit className="w-4 h-4" />
                                                                    </Button>
                                                                ) : (
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => {
                                                                            setEditingRankCategoryLeader(category.id);
                                                                            setRankCategoryLeaderForm({
                                                                                team_leader_name: '',
                                                                                team_leader_phone: '',
                                                                                team_leader_contact: '',
                                                                            });
                                                                        }}
                                                                        className="text-blue-600"
                                                                    >
                                                                        <UserPlus className="w-4 h-4 mr-1" />
                                                                        Lantik
                                                                    </Button>
                                                                )}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {/* Rank Category Leader Form Row */}
                                                        {editingRankCategoryLeader === category.id && (
                                                            <tr className="bg-blue-50">
                                                                <td colSpan={4} className="px-4 py-4">
                                                                    <div className="space-y-4">
                                                                        <h4 className="font-medium text-blue-900">
                                                                            {category.has_leader ? 'Edit' : 'Lantik'} Ketua Pasukan - {category.name}
                                                                        </h4>
                                                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                            <div className="space-y-2">
                                                                                <Label htmlFor="rank_leader_name">Nama Penuh *</Label>
                                                                                <Input
                                                                                    id="rank_leader_name"
                                                                                    value={rankCategoryLeaderForm.team_leader_name}
                                                                                    onChange={(e) => setRankCategoryLeaderForm(prev => ({
                                                                                        ...prev,
                                                                                        team_leader_name: e.target.value
                                                                                    }))}
                                                                                    placeholder="Nama penuh ketua pasukan"
                                                                                />
                                                                            </div>
                                                                            <div className="space-y-2">
                                                                                <Label htmlFor="rank_leader_phone">No. Telefon *</Label>
                                                                                <Input
                                                                                    id="rank_leader_phone"
                                                                                    value={rankCategoryLeaderForm.team_leader_phone}
                                                                                    onChange={(e) => setRankCategoryLeaderForm(prev => ({
                                                                                        ...prev,
                                                                                        team_leader_phone: e.target.value
                                                                                    }))}
                                                                                    placeholder="012-3456789"
                                                                                />
                                                                            </div>
                                                                            <div className="space-y-2">
                                                                                <Label htmlFor="rank_leader_contact">Maklumat Hubungan</Label>
                                                                                <Input
                                                                                    id="rank_leader_contact"
                                                                                    value={rankCategoryLeaderForm.team_leader_contact}
                                                                                    onChange={(e) => setRankCategoryLeaderForm(prev => ({
                                                                                        ...prev,
                                                                                        team_leader_contact: e.target.value
                                                                                    }))}
                                                                                    placeholder="Email atau maklumat hubungan lain"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                        <div className="flex justify-end gap-2">
                                                                            <Button
                                                                                variant="outline"
                                                                                onClick={() => {
                                                                                    setEditingRankCategoryLeader(null);
                                                                                    setRankCategoryLeaderForm({
                                                                                        team_leader_name: '',
                                                                                        team_leader_phone: '',
                                                                                        team_leader_contact: '',
                                                                                    });
                                                                                }}
                                                                                size="sm"
                                                                            >
                                                                                Batal
                                                                            </Button>
                                                                            <Button
                                                                                onClick={handleSaveRankCategoryLeader}
                                                                                disabled={loading}
                                                                                size="sm"
                                                                            >
                                                                                {loading ? 'Menyimpan...' : 'Simpan'}
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        )}

                                                        {/* Players List Expanded Row */}
                                                        {selectedRankCategory === category.id && (
                                                            <tr className="bg-gray-50">
                                                                <td colSpan={4} className="px-4 py-4">
                                                                    <div className="space-y-4">
                                                                        <div className="flex items-center justify-between">
                                                                            <h4 className="font-medium text-gray-900">
                                                                                Senarai Peserta - {category.name}
                                                                            </h4>
                                                                            <span className="text-sm text-gray-500">
                                                                                {categoryPlayers.length} peserta
                                                                            </span>
                                                                        </div>

                                                                        {categoryPlayers.length > 0 ? (
                                                                            <div className="space-y-2">
                                                                                {categoryPlayers.map((player, index) => (
                                                                                    <div key={player.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                                                                                        <div className="flex items-center gap-3">
                                                                                            <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                                                                                                {index + 1}
                                                                                            </span>
                                                                                            <div>
                                                                                                <p className="font-medium text-gray-900">{player.name}</p>
                                                                                                <p className="text-sm text-gray-600">
                                                                                                    {player.rank_category ? player.rank_category.name : 'Tiada kategori pangkat'}
                                                                                                </p>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div className="flex items-center gap-2">
                                                                                            <Badge variant="outline" className="text-green-600 border-green-200">
                                                                                                {player.status}
                                                                                            </Badge>
                                                                                            <Button
                                                                                                variant="outline"
                                                                                                size="sm"
                                                                                                onClick={() => {
                                                                                                    setSelectedPlayer(player);
                                                                                                    setIsEditPlayerModalOpen(true);
                                                                                                }}
                                                                                            >
                                                                                                <Edit className="w-4 h-4" />
                                                                                            </Button>
                                                                                            <Button
                                                                                                variant="outline"
                                                                                                size="sm"
                                                                                                onClick={() => {
                                                                                                    setPlayerToDelete(player);
                                                                                                    setIsDeletePlayerModalOpen(true);
                                                                                                }}
                                                                                                className="text-red-600 hover:text-red-700"
                                                                                            >
                                                                                                <Trash2 className="w-4 h-4" />
                                                                                            </Button>
                                                                                        </div>
                                                                                    </div>
                                                                                ))}
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-center py-8 text-gray-500">
                                                                                <Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                                                                                <p>Belum ada peserta dalam kategori ini</p>
                                                                                <p className="text-sm">Tambah peserta menggunakan borang di atas</p>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        )}
                                                        </React.Fragment>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Team Leader Tab */}
                        <TabsContent value="team-leader" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Maklumat Ketua Pasukan</CardTitle>
                                    <CardDescription>
                                        Ketua pasukan bertanggungjawab mengurus dan memimpin pasukan {sport.name}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {sportDetails?.team_leader && !isEditingLeader ? (
                                        <div className="space-y-4">
                                            <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                                                <UserCheck className="w-8 h-8 text-green-600" />
                                                <div className="flex-1">
                                                    <h3 className="font-medium text-green-900">{sportDetails.team_leader.name}</h3>
                                                    <p className="text-sm text-green-700">📞 {sportDetails.team_leader.phone}</p>
                                                    {sportDetails.team_leader.contact && (
                                                        <p className="text-sm text-green-700">✉️ {sportDetails.team_leader.contact}</p>
                                                    )}
                                                </div>
                                                <div className="flex gap-2">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => setIsEditingLeader(true)}
                                                    >
                                                        <Edit className="w-4 h-4 mr-2" />
                                                        Edit
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={handleDeleteLeader}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="w-4 h-4 mr-2" />
                                                        Padam
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ) : !isEditingLeader && !sportDetails?.team_leader ? (
                                        <div className="text-center py-8">
                                            <UserPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-500 mb-4">Belum ada ketua pasukan dilantik</p>
                                            <Button
                                                onClick={() => setIsEditingLeader(true)}
                                                className="bg-blue-600 hover:bg-blue-700"
                                            >
                                                <UserPlus className="w-4 h-4 mr-2" />
                                                Lantik Ketua Pasukan
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="team_leader_name">Nama Penuh *</Label>
                                                    <Input
                                                        id="team_leader_name"
                                                        value={leaderForm.team_leader_name}
                                                        onChange={(e) => setLeaderForm(prev => ({ ...prev, team_leader_name: e.target.value }))}
                                                        placeholder="Nama ketua pasukan"
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="team_leader_phone">No. Telefon *</Label>
                                                    <Input
                                                        id="team_leader_phone"
                                                        value={leaderForm.team_leader_phone}
                                                        onChange={(e) => setLeaderForm(prev => ({ ...prev, team_leader_phone: e.target.value }))}
                                                        placeholder="012-3456789"
                                                    />
                                                </div>
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="team_leader_contact">Maklumat Tambahan</Label>
                                                <Input
                                                    id="team_leader_contact"
                                                    value={leaderForm.team_leader_contact}
                                                    onChange={(e) => setLeaderForm(prev => ({ ...prev, team_leader_contact: e.target.value }))}
                                                    placeholder="Email atau maklumat hubungan lain"
                                                />
                                            </div>
                                            <div className="flex justify-end gap-2">
                                                <Button variant="outline" onClick={() => {
                                                    setIsEditingLeader(false);
                                                    // Reset form to original data if leader exists, otherwise clear
                                                    if (sportDetails?.team_leader) {
                                                        setLeaderForm({
                                                            team_leader_name: sportDetails.team_leader.name,
                                                            team_leader_phone: sportDetails.team_leader.phone,
                                                            team_leader_contact: sportDetails.team_leader.contact || '',
                                                        });
                                                    } else {
                                                        setLeaderForm({
                                                            team_leader_name: '',
                                                            team_leader_phone: '',
                                                            team_leader_contact: '',
                                                        });
                                                    }
                                                }}>
                                                    Batal
                                                </Button>
                                                <Button onClick={handleSaveLeader} disabled={loading}>
                                                    {loading ? 'Menyimpan...' : 'Simpan'}
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Main Players Tab */}
                        <TabsContent value="main-players" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Pemain Utama</CardTitle>
                                            <CardDescription>
                                                Senarai pemain utama untuk {sport.name} ({sportDetails?.main_players.length || 0})
                                            </CardDescription>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            disabled={!sportDetails?.team_leader}
                                            onClick={() => {
                                                setPlayerForm({ name: '', player_type: 'main', rank_category_id: 0 });
                                                setValidationErrors({});
                                                setShowMainPlayerForm(true);
                                                setShowSubstitutePlayerForm(false);
                                            }}
                                        >
                                            <UserPlus className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {/* Add Player Form */}
                                    {showMainPlayerForm && (
                                        <div className="mb-4 p-4 border rounded-lg bg-gray-50">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="player_name">Nama Pemain *</Label>
                                                    <Input
                                                        id="player_name"
                                                        value={playerForm.name}
                                                        onChange={(e) => setPlayerForm(prev => ({ ...prev, name: e.target.value }))}
                                                        placeholder="Nama penuh pemain"
                                                        className={validationErrors.name ? 'border-red-500' : ''}
                                                    />
                                                    {validationErrors.name && (
                                                        <p className="text-sm text-red-600">{validationErrors.name}</p>
                                                    )}
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="player_rank_category">Kategori Pangkat *</Label>
                                                    <Select
                                                        value={playerForm.rank_category_id > 0 ? playerForm.rank_category_id.toString() : ""}
                                                        onValueChange={(value) => setPlayerForm(prev => ({ ...prev, rank_category_id: parseInt(value) }))}
                                                    >
                                                        <SelectTrigger className={`w-full ${validationErrors.rank_category_id ? 'border-red-500' : ''}`}>
                                                            <SelectValue placeholder="Pilih kategori pangkat" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {rankCategories.map((category) => (
                                                                <SelectItem key={category.id} value={category.id.toString()}>
                                                                    {category.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {validationErrors.rank_category_id && (
                                                        <p className="text-sm text-red-600">{validationErrors.rank_category_id}</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-2">
                                                <Button variant="outline" onClick={handleCancelAddPlayer}>
                                                    Batal
                                                </Button>
                                                <Button onClick={handleSavePlayer} disabled={loading}>
                                                    {loading ? 'Menyimpan...' : 'Simpan'}
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                    {sportDetails?.main_players.length ? (
                                        <div className="space-y-2">
                                            {sportDetails.main_players.map((player) => (
                                                <div key={player.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{player.name}</p>
                                                        <p className="text-sm text-gray-600">
                                                            {player.rank_category ? player.rank_category.name : 'Tiada kategori pangkat'}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="outline">{player.status}</Badge>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedPlayer(player);
                                                                setIsEditPlayerModalOpen(true);
                                                            }}
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setPlayerToDelete(player);
                                                                setIsDeletePlayerModalOpen(true);
                                                            }}
                                                            className="text-red-600 hover:text-red-700"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-500">Belum ada pemain utama didaftarkan</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Substitute Players Tab */}
                        <TabsContent value="substitute-players" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Pemain Simpanan</CardTitle>
                                            <CardDescription>
                                                Senarai pemain simpanan untuk {sport.name} ({sportDetails?.substitute_players.length || 0})
                                            </CardDescription>
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            disabled={!sportDetails?.team_leader}
                                            onClick={() => {
                                                setPlayerForm({ name: '', player_type: 'substitute', rank_category_id: 0 });
                                                setValidationErrors({});
                                                setShowSubstitutePlayerForm(true);
                                                setShowMainPlayerForm(false);
                                            }}
                                        >
                                            <UserPlus className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {/* Add Player Form */}
                                    {showSubstitutePlayerForm && (
                                        <div className="mb-4 p-4 border rounded-lg bg-gray-50">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="substitute_player_name">Nama Pemain *</Label>
                                                    <Input
                                                        id="substitute_player_name"
                                                        value={playerForm.name}
                                                        onChange={(e) => setPlayerForm(prev => ({ ...prev, name: e.target.value }))}
                                                        placeholder="Nama penuh pemain"
                                                        className={validationErrors.name ? 'border-red-500' : ''}
                                                    />
                                                    {validationErrors.name && (
                                                        <p className="text-sm text-red-600">{validationErrors.name}</p>
                                                    )}
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="substitute_player_rank_category">Kategori Pangkat *</Label>
                                                    <Select
                                                        value={playerForm.rank_category_id > 0 ? playerForm.rank_category_id.toString() : ""}
                                                        onValueChange={(value) => setPlayerForm(prev => ({ ...prev, rank_category_id: parseInt(value) }))}
                                                    >
                                                        <SelectTrigger className={`w-full ${validationErrors.rank_category_id ? 'border-red-500' : ''}`}>
                                                            <SelectValue placeholder="Pilih kategori pangkat" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {rankCategories.map((category) => (
                                                                <SelectItem key={category.id} value={category.id.toString()}>
                                                                    {category.name}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                    {validationErrors.rank_category_id && (
                                                        <p className="text-sm text-red-600">{validationErrors.rank_category_id}</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-2">
                                                <Button variant="outline" onClick={handleCancelAddPlayer}>
                                                    Batal
                                                </Button>
                                                <Button onClick={handleSavePlayer} disabled={loading}>
                                                    {loading ? 'Menyimpan...' : 'Simpan'}
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {sportDetails?.substitute_players.length ? (
                                        <div className="space-y-2">
                                            {sportDetails.substitute_players.map((player) => (
                                                <div key={player.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{player.name}</p>
                                                        <p className="text-sm text-gray-600">
                                                            {player.rank_category ? player.rank_category.name : 'Tiada kategori pangkat'}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="outline">{player.status}</Badge>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedPlayer(player);
                                                                setIsEditPlayerModalOpen(true);
                                                            }}
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setPlayerToDelete(player);
                                                                setIsDeletePlayerModalOpen(true);
                                                            }}
                                                            className="text-red-600 hover:text-red-700"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <UserPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-500">Belum ada pemain simpanan didaftarkan</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                )}
                </div>
            </WideDialogContent>

            {/* Edit Player Modal */}
            <EditPlayerModal
                player={selectedPlayer}
                isOpen={isEditPlayerModalOpen}
                onClose={() => {
                    setIsEditPlayerModalOpen(false);
                    setSelectedPlayer(null);
                }}
                onRefresh={async () => {
                    await fetchSportDetails();
                    await onRefresh();
                }}
            />

            {/* Delete Player Confirmation Modal */}
            <Dialog open={isDeletePlayerModalOpen} onOpenChange={setIsDeletePlayerModalOpen}>
                <DialogOverlay />
                <DialogPortal>
                    <DialogPrimitive.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-md translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg">
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-3">
                                <Trash2 className="w-6 h-6 text-red-600" />
                                Padam Pemain
                            </DialogTitle>
                        </DialogHeader>

                        <div className="space-y-4">
                            <p className="text-sm text-gray-600">
                                Adakah anda pasti ingin memadamkan pemain <strong>{playerToDelete?.name}</strong>?
                                Tindakan ini tidak boleh dibatalkan.
                            </p>

                            <div className="flex justify-end gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setIsDeletePlayerModalOpen(false);
                                        setPlayerToDelete(null);
                                    }}
                                    disabled={loading}
                                >
                                    Batal
                                </Button>
                                <Button
                                    variant="destructive"
                                    onClick={handleDeletePlayer}
                                    disabled={loading}
                                >
                                    {loading ? 'Memadamkan...' : 'Padam'}
                                </Button>
                            </div>
                        </div>

                        <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                            <XIcon className="h-4 w-4" />
                            <span className="sr-only">Close</span>
                        </DialogPrimitive.Close>
                    </DialogPrimitive.Content>
                </DialogPortal>
            </Dialog>
        </Dialog>
    );
}
