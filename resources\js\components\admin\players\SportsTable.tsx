import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Users, UserCheck, UserX, Settings, Trash2, Loader2 } from 'lucide-react';
import api from '@/lib/axios';
import { toast } from 'react-hot-toast';

interface RankCategory {
    id: number;
    name: string;
    code: string;
    players_count: number;
}

interface SportTeam {
    id: number;
    name: string;
    icon: string | null;
    sport_type: 'zone_based' | 'rank_based';
    sport_team?: {
        id: number;
        team_leader_name: string;
        team_leader_phone: string;
        team_leader_contact: string | null;
    } | null;
    team_leader_name?: string | null;
    main_players_count?: number;
    substitute_players_count?: number;
    rank_categories?: RankCategory[];
    total_players: number;
    can_manage: boolean;
}

interface SportsTableProps {
    sports: SportTeam[];
    loading: boolean;
    onManageSport: (sport: SportTeam) => void;
    onRefresh: () => void;
}

export default function SportsTable({ sports, loading, onManageSport, onRefresh }: SportsTableProps) {
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [sportToDelete, setSportToDelete] = useState<SportTeam | null>(null);
    const [deleting, setDeleting] = useState(false);
    const getStatusColor = (count: number) => {
        if (count > 0) return 'text-green-600 bg-green-50';
        return 'text-gray-600 bg-gray-50';
    };

    const getTeamLeaderStatus = (teamLeaderName: string | null) => {
        if (teamLeaderName) {
            return (
                <div className="flex items-center gap-2">
                    <UserCheck className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium">{teamLeaderName}</span>
                </div>
            );
        }
        return (
            <div className="flex items-center gap-2">
                <UserX className="w-4 h-4 text-red-600" />
                <span className="text-sm text-red-600">Belum dilantik</span>
            </div>
        );
    };

    const handleDeleteClick = (sport: SportTeam) => {
        if (!sport.sport_team) {
            toast.error('Zon belum menyertai sukan ini');
            return;
        }
        setSportToDelete(sport);
        setIsDeleteModalOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!sportToDelete || !sportToDelete.sport_team) return;

        setDeleting(true);
        try {
            await api.delete(`/admin/sport-teams/${sportToDelete.sport_team.id}`);
            toast.success('Penyertaan sukan berjaya dipadamkan');
            setIsDeleteModalOpen(false);
            setSportToDelete(null);
            onRefresh();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal memadamkan penyertaan sukan');
        } finally {
            setDeleting(false);
        }
    };

    const handleDeleteCancel = () => {
        setIsDeleteModalOpen(false);
        setSportToDelete(null);
    };

    if (loading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Senarai Sukan</CardTitle>
                    <CardDescription>Urus pasukan dan peserta mengikut sukan</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <>
        <Card>
            <CardHeader>
                <CardTitle>Senarai Sukan</CardTitle>
                <CardDescription>Urus pasukan dan peserta mengikut sukan</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left py-3 px-4 font-medium text-gray-900">Sukan</th>
                                <th className="text-left py-3 px-4 font-medium text-gray-900">Ketua Pasukan</th>
                                <th className="text-center py-3 px-4 font-medium text-gray-900">Pemain Utama</th>
                                <th className="text-center py-3 px-4 font-medium text-gray-900">Pemain Simpanan</th>
                                <th className="text-center py-3 px-4 font-medium text-gray-900">Tindakan</th>
                            </tr>
                        </thead>
                        <tbody>
                            {sports.map((sport) => (
                                <tr key={sport.id} className="border-b hover:bg-gray-50">
                                    {/* Sport Name & Icon */}
                                    <td className="py-4 px-4">
                                        <div className="flex items-center gap-3">
                                            {sport.icon ? (
                                                <img
                                                    src={sport.icon}
                                                    alt={sport.name}
                                                    className="w-8 h-8 object-contain"
                                                />
                                            ) : (
                                                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                                    <Users className="w-4 h-4 text-gray-500" />
                                                </div>
                                            )}
                                            <span className="font-medium text-gray-900">{sport.name}</span>
                                        </div>
                                    </td>

                                    {/* Team Leader / Categories */}
                                    <td className="py-4 px-4">
                                        {sport.sport_type === 'rank_based' ? (
                                            <Badge variant="secondary" className="text-blue-600 bg-blue-50">
                                                {sport.rank_categories?.length || 0} Kategori Pangkat
                                            </Badge>
                                        ) : (
                                            getTeamLeaderStatus(sport.team_leader_name)
                                        )}
                                    </td>

                                    {/* Main Players / Categories Count */}
                                    <td className="py-4 px-4 text-center">
                                        {sport.sport_type === 'rank_based' ? (
                                            <Badge variant="outline" className="text-purple-600 bg-purple-50">
                                                Berdasarkan Pangkat
                                            </Badge>
                                        ) : (
                                            <Badge
                                                variant="outline"
                                                className={getStatusColor(sport.main_players_count || 0)}
                                            >
                                                {sport.main_players_count || 0}
                                            </Badge>
                                        )}
                                    </td>

                                    {/* Substitute Players */}
                                    <td className="py-4 px-4 text-center">
                                        {sport.sport_type === 'rank_based' ? (
                                            <Badge variant="outline" className="text-gray-500">
                                                -
                                            </Badge>
                                        ) : (
                                            <Badge
                                                variant="outline"
                                                className={getStatusColor(sport.substitute_players_count || 0)}
                                            >
                                                {sport.substitute_players_count || 0}
                                            </Badge>
                                        )}
                                    </td>

                                    {/* Actions */}
                                    <td className="py-4 px-4 text-center">
                                        <div className="flex items-center justify-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onManageSport(sport)}
                                                disabled={!sport.can_manage}
                                            >
                                                <Settings className="w-4 h-4 mr-2" />
                                                Urus
                                            </Button>
                                            {sport.sport_team && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleDeleteClick(sport)}
                                                    disabled={!sport.can_manage}
                                                    className="text-red-600 hover:text-red-700 hover:border-red-300"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {sports.length === 0 && (
                    <div className="text-center py-8">
                        <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">Tiada data sukan dijumpai</p>
                    </div>
                )}
            </CardContent>
        </Card>

        {/* Delete Confirmation Modal */}
        <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Padam Penyertaan Sukan</DialogTitle>
                    <DialogDescription>
                        Adakah anda pasti ingin memadamkan penyertaan zon dalam sukan "{sportToDelete?.name}"?
                        Semua ketua pasukan dan peserta dalam sukan ini akan dipadamkan.
                        Tindakan ini tidak boleh dibatalkan.
                    </DialogDescription>
                </DialogHeader>

                <div className="flex justify-end gap-2 pt-4">
                    <Button
                        variant="outline"
                        onClick={handleDeleteCancel}
                        disabled={deleting}
                    >
                        Batal
                    </Button>
                    <Button
                        variant="destructive"
                        onClick={handleDeleteConfirm}
                        disabled={deleting}
                    >
                        {deleting ? (
                            <>
                                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                Memadamkan...
                            </>
                        ) : (
                            <>
                                <Trash2 className="w-4 h-4 mr-2" />
                                Padam
                            </>
                        )}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
        </>
    );
}
