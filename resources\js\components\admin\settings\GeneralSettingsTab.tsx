import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-hot-toast';
import { Upload, Image as ImageIcon } from 'lucide-react';
import api from '@/lib/axios';

interface GeneralSettingsTabProps {
    settings: any;
    onSettingsUpdate?: () => void;
}

export default function GeneralSettingsTab({ settings, onSettingsUpdate }: GeneralSettingsTabProps) {
    const [logoPreview, setLogoPreview] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState<any>({});
    const [formData, setFormData] = useState({
        site_name: '',
        site_tagline: '',
        site_description: '',
        header_title_part1: 'SUKAN',
        header_title_part2: 'INTRA',
        header_subtitle: 'KOR KESIHATAN DIRAJA 2025',
        copyright_text: '',
        logo: null as File | null,
    });

    // Update formData when settings prop changes
    useEffect(() => {
        setFormData({
            site_name: settings.site_name || '',
            site_tagline: settings.site_tagline || '',
            site_description: settings.site_description || '',
            header_title_part1: settings.header_title_part1 || 'SUKAN',
            header_title_part2: settings.header_title_part2 || 'INTRA',
            header_subtitle: settings.header_subtitle || 'KOR KESIHATAN DIRAJA 2025',
            copyright_text: settings.copyright_text || '',
            logo: null,
        });
    }, [settings]);

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        // Clear error for this field
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setFormData(prev => ({
                ...prev,
                logo: file
            }));

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setLogoPreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setErrors({});

        try {
            const formDataToSend = new FormData();
            Object.entries(formData).forEach(([key, value]) => {
                if (value !== null) {
                    formDataToSend.append(key, value);
                }
            });

            const response = await api.post('/admin/settings/general', formDataToSend, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.status === 200) {
                toast.success('Tetapan umum berjaya dikemaskini');
                // Call parent callback to refresh settings
                if (onSettingsUpdate) {
                    onSettingsUpdate();
                }
                // Reset logo preview
                setLogoPreview(null);
                // Reset logo file in form
                setFormData(prev => ({ ...prev, logo: null }));
            }
        } catch (error: any) {
            if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            } else {
                toast.error('Ralat berlaku semasa menyimpan tetapan');
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Tetapan Umum</CardTitle>
                <CardDescription>
                    Konfigurasi maklumat asas portal sukan
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <Label htmlFor="site_name">Nama Portal</Label>
                            <Input
                                id="site_name"
                                value={formData.site_name}
                                onChange={(e) => handleInputChange('site_name', e.target.value)}
                                placeholder="Portal Sukan Intra KKD"
                            />
                            {errors.site_name && (
                                <p className="text-sm text-red-600">{errors.site_name}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="site_tagline">Tagline</Label>
                            <Input
                                id="site_tagline"
                                value={formData.site_tagline}
                                onChange={(e) => handleInputChange('site_tagline', e.target.value)}
                                placeholder="Sukan Intra Kor Kesihatan DiRaja 2025"
                            />
                            {errors.site_tagline && (
                                <p className="text-sm text-red-600">{errors.site_tagline}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label>Logo Portal</Label>
                            <div className="space-y-4">
                                {/* Current Logo */}
                                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                    {settings.logo_url ? (
                                        <img
                                            src={`/storage/${settings.logo_url}`}
                                            alt="Current Logo"
                                            className="max-h-16 mx-auto"
                                            onError={(e) => {
                                                e.currentTarget.src = '/images/logo.png';
                                            }}
                                        />
                                    ) : (
                                        <div className="text-gray-500">
                                            <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                                            <p className="text-sm">Logo semasa</p>
                                        </div>
                                    )}
                                </div>

                                {/* Upload New Logo */}
                                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                    {logoPreview ? (
                                        <img
                                            src={logoPreview}
                                            alt="Logo Preview"
                                            className="max-h-16 mx-auto mb-4"
                                        />
                                    ) : (
                                        <div className="text-gray-500 mb-4">
                                            <Upload className="h-8 w-8 mx-auto mb-2" />
                                            <p className="text-sm">Pilih logo baru</p>
                                        </div>
                                    )}

                                    <Input
                                        type="file"
                                        accept="image/jpeg,image/png,image/jpg,image/gif"
                                        onChange={handleLogoChange}
                                        className="max-w-xs mx-auto"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="site_description">Penerangan Portal</Label>
                        <Textarea
                            id="site_description"
                            value={formData.site_description}
                            onChange={(e) => handleInputChange('site_description', e.target.value)}
                            placeholder="Portal rasmi untuk Sukan Intra Kor Kesihatan DiRaja 2025"
                            rows={3}
                        />
                        {errors.site_description && (
                            <p className="text-sm text-red-600">{errors.site_description}</p>
                        )}
                    </div>

                    {/* Header Text Settings */}
                    <div className="col-span-2">
                        <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">Tetapan Teks Header</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="header_title_part1">Bahagian Pertama Tajuk (Warna Merah)</Label>
                                <Input
                                    id="header_title_part1"
                                    value={formData.header_title_part1}
                                    onChange={(e) => handleInputChange('header_title_part1', e.target.value)}
                                    placeholder="SUKAN"
                                />
                                {errors.header_title_part1 && (
                                    <p className="text-sm text-red-600">{errors.header_title_part1}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="header_title_part2">Bahagian Kedua Tajuk (Warna Biru)</Label>
                                <Input
                                    id="header_title_part2"
                                    value={formData.header_title_part2}
                                    onChange={(e) => handleInputChange('header_title_part2', e.target.value)}
                                    placeholder="INTRA"
                                />
                                {errors.header_title_part2 && (
                                    <p className="text-sm text-red-600">{errors.header_title_part2}</p>
                                )}
                            </div>

                            <div className="space-y-2 md:col-span-2">
                                <Label htmlFor="header_subtitle">Subtajuk Header (Warna Kuning)</Label>
                                <Input
                                    id="header_subtitle"
                                    value={formData.header_subtitle}
                                    onChange={(e) => handleInputChange('header_subtitle', e.target.value)}
                                    placeholder="KOR KESIHATAN DIRAJA 2025"
                                />
                                {errors.header_subtitle && (
                                    <p className="text-sm text-red-600">{errors.header_subtitle}</p>
                                )}
                            </div>
                        </div>
                    </div>



                    <div className="space-y-2">
                        <Label htmlFor="copyright_text">Teks Hak Cipta</Label>
                        <Input
                            id="copyright_text"
                            value={formData.copyright_text}
                            onChange={(e) => handleInputChange('copyright_text', e.target.value)}
                            placeholder="© 2025 Kor Kesihatan DiRaja. Hak Cipta Terpelihara."
                        />
                        {errors.copyright_text && (
                            <p className="text-sm text-red-600">{errors.copyright_text}</p>
                        )}
                    </div>

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className="min-w-[120px]"
                        >
                            {isLoading ? 'Menyimpan...' : 'Simpan Tetapan'}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
