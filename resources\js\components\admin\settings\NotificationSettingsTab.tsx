import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-hot-toast';
import api from '@/lib/axios';

interface NotificationSettingsTabProps {
    settings: any;
    onSettingsUpdate?: () => void;
}

export default function NotificationSettingsTab({ settings, onSettingsUpdate }: NotificationSettingsTabProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState<any>({});
    const [formData, setFormData] = useState({
        email_notifications: false,
        sms_notifications: false,
        push_notifications: false,
        facebook_url: '',
        instagram_url: '',
        twitter_url: '',
        youtube_url: '',
    });

    // Update formData when settings prop changes
    useEffect(() => {
        setFormData({
            email_notifications: settings.email_notifications || false,
            sms_notifications: settings.sms_notifications || false,
            push_notifications: settings.push_notifications || false,
            facebook_url: settings.facebook_url || '',
            instagram_url: settings.instagram_url || '',
            twitter_url: settings.twitter_url || '',
            youtube_url: settings.youtube_url || '',
        });
    }, [settings]);

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        // Clear error for this field
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const handleCheckboxChange = (field: string, checked: boolean) => {
        setFormData(prev => ({
            ...prev,
            [field]: checked
        }));
        // Clear error for this field
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setErrors({});

        try {
            const response = await api.post('/admin/settings/notification', formData);

            if (response.status === 200) {
                toast.success('Tetapan notifikasi berjaya dikemaskini');
                // Call parent callback to refresh settings
                if (onSettingsUpdate) {
                    onSettingsUpdate();
                }
            }
        } catch (error: any) {
            if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            } else {
                toast.error('Ralat berlaku semasa menyimpan tetapan');
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Tetapan Notifikasi & Media Sosial</CardTitle>
                <CardDescription>
                    Konfigurasi notifikasi dan pautan media sosial
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Notification Settings */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Tetapan Notifikasi</h3>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="email_notifications"
                                checked={formData.email_notifications}
                                onCheckedChange={(checked) => handleCheckboxChange('email_notifications', checked as boolean)}
                            />
                            <Label htmlFor="email_notifications">Notifikasi Email</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="sms_notifications"
                                checked={formData.sms_notifications}
                                onCheckedChange={(checked) => handleCheckboxChange('sms_notifications', checked as boolean)}
                            />
                            <Label htmlFor="sms_notifications">Notifikasi SMS</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="push_notifications"
                                checked={formData.push_notifications}
                                onCheckedChange={(checked) => handleCheckboxChange('push_notifications', checked as boolean)}
                            />
                            <Label htmlFor="push_notifications">Push Notifications</Label>
                        </div>
                    </div>

                    {/* Social Media Settings */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Pautan Media Sosial</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="facebook_url">Facebook URL</Label>
                                <Input
                                    id="facebook_url"
                                    type="url"
                                    value={formData.facebook_url}
                                    onChange={(e) => handleInputChange('facebook_url', e.target.value)}
                                    placeholder="https://facebook.com/sukankd"
                                />
                                {errors.facebook_url && (
                                    <p className="text-sm text-red-600">{errors.facebook_url}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="instagram_url">Instagram URL</Label>
                                <Input
                                    id="instagram_url"
                                    type="url"
                                    value={formData.instagram_url}
                                    onChange={(e) => handleInputChange('instagram_url', e.target.value)}
                                    placeholder="https://instagram.com/sukankd"
                                />
                                {errors.instagram_url && (
                                    <p className="text-sm text-red-600">{errors.instagram_url}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="twitter_url">Twitter URL</Label>
                                <Input
                                    id="twitter_url"
                                    type="url"
                                    value={formData.twitter_url}
                                    onChange={(e) => handleInputChange('twitter_url', e.target.value)}
                                    placeholder="https://twitter.com/sukankd"
                                />
                                {errors.twitter_url && (
                                    <p className="text-sm text-red-600">{errors.twitter_url}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="youtube_url">YouTube URL</Label>
                                <Input
                                    id="youtube_url"
                                    type="url"
                                    value={formData.youtube_url}
                                    onChange={(e) => handleInputChange('youtube_url', e.target.value)}
                                    placeholder="https://youtube.com/sukankd"
                                />
                                {errors.youtube_url && (
                                    <p className="text-sm text-red-600">{errors.youtube_url}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className="min-w-[120px]"
                        >
                            {isLoading ? 'Menyimpan...' : 'Simpan Tetapan'}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
