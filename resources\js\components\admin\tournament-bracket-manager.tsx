
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy, RefreshCw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import ModernTournamentBracket from './modern-tournament-bracket';

interface TournamentBracketManagerProps {
    onMatchSelect?: (match: any) => void;
}

export default function TournamentBracketManager({ onMatchSelect }: TournamentBracketManagerProps) {
    const [selectedSport, setSelectedSport] = useState<string>('');
    const [sports, setSports] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    // Fetch sports list
    useEffect(() => {
        const fetchSports = async () => {
            try {
                const response = await api.get('/admin/sports');
                setSports(response.data || []);
            } catch (error: any) {
                toast.error(`Gagal memuat senarai sukan: ${error.response?.status || 'Unknown error'}`);
            }
        };

        fetchSports();
    }, []);

    const fetchBracketMatches = async () => {
        if (!selectedSport) {
            toast.error('Sila pilih sukan terlebih dahulu');
            return;
        }

        setIsLoading(true);
        try {
            const response = await api.get(`/admin/sport-matches/bracket/${selectedSport}`);

            if (response.data.success) {
                // The bracket data will be handled by ModernTournamentBracket component
            } else {
                throw new Error(response.data.message || 'Failed to fetch bracket data');
            }
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || error.message || 'Gagal memuat data bracket';
            toast.error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Header with Controls */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-6 w-6 text-yellow-600" />
                        Tournament Bracket Manager
                    </CardTitle>
                    <CardDescription>
                        Urus dan papar bracket tournament untuk setiap sukan
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex gap-4 items-end">
                        <div className="flex-1">
                            <label className="block text-sm font-medium mb-2">Pilih Sukan</label>
                            <Select value={selectedSport} onValueChange={setSelectedSport}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Pilih sukan..." />
                                </SelectTrigger>
                                <SelectContent>
                                    {sports.map((sport) => (
                                        <SelectItem key={sport.id} value={sport.id.toString()}>
                                            {sport.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <Button
                            onClick={fetchBracketMatches}
                            disabled={!selectedSport || isLoading}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {isLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            ) : (
                                <RefreshCw className="h-4 w-4 mr-2" />
                            )}
                            Load Bracket
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Modern Tournament Bracket - without the controls card */}
            <div className="space-y-6">
                <ModernTournamentBracket onMatchSelect={onMatchSelect} hideControls={true} />
            </div>
        </div>
    );
};
