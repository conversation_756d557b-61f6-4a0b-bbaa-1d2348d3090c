import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trophy, Users, Calendar, MapPin } from 'lucide-react';

interface BracketMatch {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    bracket_round: string;
    bracket_position: number;
    status: string;
    is_highlighted: boolean;
    match_date: string;
    match_time: string;
    venue: string;
    winner_zone?: string;
    score_zone_a?: number;
    score_zone_b?: number;
}

interface TournamentBracketProps {
    matches: BracketMatch[];
    title: string;
    sportName: string;
}

export default function TournamentBracket({ matches, title, sportName }: TournamentBracketProps) {
    const [bracketData, setBracketData] = useState<Record<string, BracketMatch[]>>({});

    useEffect(() => {
        // Group matches by bracket round
        const grouped = matches.reduce((acc, match) => {
            const round = match.bracket_round || 'Unknown';
            if (!acc[round]) {
                acc[round] = [];
            }
            acc[round].push(match);
            return acc;
        }, {} as Record<string, BracketMatch[]>);

        // Sort matches within each round by bracket_position
        Object.keys(grouped).forEach(round => {
            grouped[round].sort((a, b) => (a.bracket_position || 0) - (b.bracket_position || 0));
        });

        setBracketData(grouped);
    }, [matches]);

    const getRoundOrder = () => {
        const rounds = Object.keys(bracketData);
        const order = ['First Round', 'Semi-Final', 'Third Place', 'Final'];
        return order.filter(round => rounds.includes(round));
    };

    const getMatchStatusColor = (status: string, isHighlighted: boolean) => {
        if (isHighlighted) {
            return 'border-yellow-400 bg-yellow-50';
        }
        
        switch (status) {
            case 'completed':
                return 'border-green-400 bg-green-50';
            case 'ongoing':
                return 'border-blue-400 bg-blue-50';
            case 'cancelled':
                return 'border-red-400 bg-red-50';
            default:
                return 'border-gray-300 bg-white';
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'scheduled':
                return <Badge className="bg-blue-100 text-blue-800">Dijadualkan</Badge>;
            case 'ongoing':
                return <Badge className="bg-yellow-100 text-yellow-800">Berlangsung</Badge>;
            case 'completed':
                return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
            case 'cancelled':
                return <Badge className="bg-red-100 text-red-800">Dibatalkan</Badge>;
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    const formatDateTime = (date: string, time: string) => {
        const matchDate = new Date(date);
        return {
            date: matchDate.toLocaleDateString('ms-MY', { 
                day: 'numeric',
                month: 'short'
            }),
            time: time
        };
    };

    const renderMatch = (match: BracketMatch) => {
        const dateTime = formatDateTime(match.match_date, match.match_time);
        const hasResult = match.status === 'completed' && match.score_zone_a !== undefined && match.score_zone_b !== undefined;

        return (
            <div
                key={match.id}
                className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${getMatchStatusColor(match.status, match.is_highlighted)}`}
            >
                <div className="space-y-3">
                    {/* Match Header */}
                    <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">{match.title}</h4>
                        {getStatusBadge(match.status)}
                    </div>

                    {/* Teams */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${
                                    match.winner_zone === match.zone_a ? 'bg-green-500' : 'bg-gray-300'
                                }`} />
                                <span className="font-medium">{match.zone_a}</span>
                            </div>
                            {hasResult && (
                                <span className="font-bold text-lg">{match.score_zone_a}</span>
                            )}
                        </div>
                        
                        <div className="text-center text-gray-400 text-xs">VS</div>
                        
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${
                                    match.winner_zone === match.zone_b ? 'bg-green-500' : 'bg-gray-300'
                                }`} />
                                <span className="font-medium">{match.zone_b}</span>
                            </div>
                            {hasResult && (
                                <span className="font-bold text-lg">{match.score_zone_b}</span>
                            )}
                        </div>
                    </div>

                    {/* Match Details */}
                    <div className="text-xs text-gray-600 space-y-1">
                        <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{dateTime.date} • {dateTime.time}</span>
                        </div>
                        {match.venue && (
                            <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{match.venue}</span>
                            </div>
                        )}
                    </div>

                    {/* Winner Highlight */}
                    {match.winner_zone && (
                        <div className="text-center">
                            <Badge className="bg-green-100 text-green-800">
                                <Trophy className="h-3 w-3 mr-1" />
                                Pemenang: {match.winner_zone}
                            </Badge>
                        </div>
                    )}
                </div>
            </div>
        );
    };

    const renderConnector = (fromRound: string, toRound: string) => {
        return (
            <div className="flex items-center justify-center px-4">
                <div className="w-8 h-0.5 bg-gray-300"></div>
                <div className="w-2 h-2 bg-gray-300 rounded-full mx-1"></div>
                <div className="w-8 h-0.5 bg-gray-300"></div>
            </div>
        );
    };

    if (Object.keys(bracketData).length === 0) {
        return (
            <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                    <Trophy className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Tiada Bracket Tournament
                    </h3>
                    <p className="text-gray-600 text-center">
                        Tiada perlawanan bracket untuk {sportName}
                    </p>
                </CardContent>
            </Card>
        );
    }

    const orderedRounds = getRoundOrder();

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Trophy className="h-5 w-5" />
                    {title}
                </CardTitle>
                <CardDescription>
                    Tournament bracket untuk {sportName}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <div className="flex gap-6 min-w-max">
                        {orderedRounds.map((round, index) => (
                            <div key={round} className="flex items-center">
                                {/* Round Column */}
                                <div className="space-y-4 min-w-[280px]">
                                    <div className="text-center">
                                        <h3 className="font-semibold text-lg mb-2">{round}</h3>
                                        <Badge variant="outline">
                                            {bracketData[round].length} perlawanan
                                        </Badge>
                                    </div>
                                    
                                    <div className="space-y-4">
                                        {bracketData[round].map(match => renderMatch(match))}
                                    </div>
                                </div>

                                {/* Connector to next round */}
                                {index < orderedRounds.length - 1 && renderConnector(round, orderedRounds[index + 1])}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Legend */}
                <div className="mt-6 pt-4 border-t">
                    <h4 className="font-medium mb-3">Legenda:</h4>
                    <div className="flex flex-wrap gap-4 text-sm">
                        <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded border-2 border-yellow-400 bg-yellow-50"></div>
                            <span>Final/Penting</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded border-2 border-green-400 bg-green-50"></div>
                            <span>Selesai</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded border-2 border-blue-400 bg-blue-50"></div>
                            <span>Sedang Berlangsung</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-green-500"></div>
                            <span>Pemenang</span>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
