import React, { useState, useEffect } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { z } from 'zod';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import { useAuth } from '@/contexts/auth-context';

// Zod validation schema for edit
const editFormSchema = z.object({
    title: z.string()
        .min(1, 'Title diperlukan')
        .max(100, 'Title tidak boleh melebihi 100 aksara'),
    description: z.string()
        .min(1, 'Description diperlukan')
        .max(500, 'Description tidak boleh melebihi 500 aksara'),
    category: z.string()
        .min(1, 'Category diperlukan'),
    zone: z.string()
        .optional()
});

type EditForm = z.infer<typeof editFormSchema>;

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

interface EditMediaModalProps {
    isOpen: boolean;
    onClose: () => void;
    media: MediaItem | null;
    onSuccess: () => void;
}

const EditMediaModal: React.FC<EditMediaModalProps> = ({
    isOpen,
    onClose,
    media,
    onSuccess
}) => {
    const { user } = useAuth();
    const [isSaving, setIsSaving] = useState(false);
    const [editForm, setEditForm] = useState<EditForm>({
        title: '',
        description: '',
        category: '',
        zone: ''
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Initialize form when media changes
    useEffect(() => {
        if (media) {
            setEditForm({
                title: media.title,
                description: media.description,
                category: media.category,
                zone: media.zone
            });
        }
    }, [media]);

    if (!isOpen || !media) return null;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        try {
            editFormSchema.parse(editForm);
            setIsSaving(true);

            const response = await api.put(`/cms/gallery/${media.id}`, editForm);

            if (response.data.success) {
                toast.success('Media updated successfully!');
                onSuccess();
                onClose();
            } else {
                throw new Error(response.data.message || 'Failed to update media');
            }
        } catch (error: any) {
            if (error instanceof z.ZodError) {
                const errors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    if (err.path && err.path.length > 0) {
                        errors[err.path[0] as string] = err.message;
                    }
                });
                setValidationErrors(errors);
            } else {
                toast.error(error.response?.data?.message || 'Failed to update media');
            }
        } finally {
            setIsSaving(false);
        }
    };

    const handleClose = () => {
        if (!isSaving) {
            setValidationErrors({});
            onClose();
        }
    };

    const getImageCount = () => {
        if (media.file_urls && media.file_urls.length > 1) {
            return `${media.file_urls.length} images`;
        }
        return media.file_type === 'image' ? '1 image' : '1 video';
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                {/* Header */}
                <div className="p-6 border-b">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-gray-900">
                            Edit Media
                        </h2>
                        <button
                            onClick={handleClose}
                            disabled={isSaving}
                            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    {/* Media Preview */}
                    <div className="mb-6">
                        <div className="bg-gray-100 rounded-lg p-4">
                            <div className="flex items-center space-x-4">
                                <div className="flex-shrink-0">
                                    <img
                                        src={media.file_url}
                                        alt={media.title}
                                        className="w-20 h-20 object-cover rounded-lg"
                                    />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                        Current: {media.title}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        {getImageCount()} • {media.views} views
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        Zone: {media.zone}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Form Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label htmlFor="edit-title" className="block text-sm font-medium text-gray-700">
                                    Title
                                </label>
                                <input
                                    id="edit-title"
                                    type="text"
                                    value={editForm.title}
                                    onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                                    disabled={isSaving}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                    placeholder="Masukkan title gambar"
                                />
                                {validationErrors.title && (
                                    <p className="text-sm text-red-500">{validationErrors.title}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <label htmlFor="edit-category" className="block text-sm font-medium text-gray-700">
                                    Category
                                </label>
                                <select
                                    id="edit-category"
                                    value={editForm.category}
                                    onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value }))}
                                    disabled={isSaving}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                >
                                    <option value="">Pilih category</option>
                                    <option value="ceremony">Majlis Rasmi</option>
                                    <option value="football">Bola Sepak</option>
                                    <option value="netball">Bola Jaring</option>
                                    <option value="badminton">Badminton</option>
                                    <option value="volleyball">Bola Tampar</option>
                                    <option value="table-tennis">Ping Pong</option>
                                    <option value="traditional">Sukan Rakyat</option>
                                </select>
                                {validationErrors.category && (
                                    <p className="text-sm text-red-500">{validationErrors.category}</p>
                                )}
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="edit-description" className="block text-sm font-medium text-gray-700">
                                Description
                            </label>
                            <textarea
                                id="edit-description"
                                rows={4}
                                value={editForm.description}
                                onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                                disabled={isSaving}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                placeholder="Masukkan description gambar"
                            />
                            {validationErrors.description && (
                                <p className="text-sm text-red-500">{validationErrors.description}</p>
                            )}
                        </div>

                        {/* Zone field - only show for non-zone users */}
                        {user?.role !== 'zone' && (
                            <div className="space-y-2">
                                <label htmlFor="edit-zone" className="block text-sm font-medium text-gray-700">
                                    Zone
                                </label>
                                <select
                                    id="edit-zone"
                                    value={editForm.zone}
                                    onChange={(e) => setEditForm(prev => ({ ...prev, zone: e.target.value }))}
                                    disabled={isSaving}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                >
                                    <option value="">Pilih zone</option>
                                    <option value="Semua Zone">Semua Zone</option>
                                    <option value="Zone A">Zone A</option>
                                    <option value="Zone B">Zone B</option>
                                    <option value="Zone C">Zone C</option>
                                    <option value="BAKAT-KKD">Bakat KKD</option>
                                </select>
                                {validationErrors.zone && (
                                    <p className="text-sm text-red-500">{validationErrors.zone}</p>
                                )}
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-4">
                            <button
                                type="button"
                                onClick={handleClose}
                                disabled={isSaving}
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isSaving}
                                className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold rounded-lg transition-colors disabled:opacity-50 flex items-center"
                            >
                                {isSaving ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    <>
                                        <Save className="w-4 h-4 mr-2" />
                                        Save Changes
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default EditMediaModal;
