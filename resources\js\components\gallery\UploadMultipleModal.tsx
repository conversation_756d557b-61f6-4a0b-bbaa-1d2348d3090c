import React, { useState } from 'react';
import { X, Upload, Loader2 } from 'lucide-react';
import { z } from 'zod';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import { useAuth } from '@/contexts/auth-context';

// Zod validation schema for upload
const uploadFormSchema = z.object({
    title: z.string()
        .min(1, 'Title diperlukan')
        .max(100, 'Title tidak boleh melebihi 100 aksara'),
    description: z.string()
        .min(1, 'Description diperlukan')
        .max(500, 'Description tidak boleh melebihi 500 aksara'),
    category: z.string()
        .min(1, 'Category diperlukan'),
    zone: z.string()
        .optional() // Zone is optional, will be handled by backend
});

type UploadForm = z.infer<typeof uploadFormSchema>;

interface UploadMultipleModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
}

const UploadMultipleModal: React.FC<UploadMultipleModalProps> = ({
    isOpen,
    onClose,
    onSuccess
}) => {
    const { user } = useAuth();
    const [isUploading, setIsUploading] = useState(false);
    const [imageFiles, setImageFiles] = useState<File[]>([]);
    const [imagePreviews, setImagePreviews] = useState<string[]>([]);
    const [uploadForm, setUploadForm] = useState<UploadForm>({
        title: '',
        description: '',
        category: '',
        zone: ''
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    if (!isOpen) return null;

    // Image compression function
    const compressImage = (file: File, maxWidth: number = 1920, maxHeight: number = 1080, quality: number = 0.8): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new (window as any).Image();

            img.onload = () => {
                const { width, height } = calculateDimensions(img.width, img.height, maxWidth, maxHeight);
                
                canvas.width = width;
                canvas.height = height;
                
                ctx?.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now(),
                        });
                        resolve(compressedFile);
                    } else {
                        resolve(file);
                    }
                }, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const calculateDimensions = (originalWidth: number, originalHeight: number, maxWidth: number, maxHeight: number) => {
        const aspectRatio = originalWidth / originalHeight;
        let width = originalWidth;
        let height = originalHeight;

        if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
        }

        if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
        }

        return { width: Math.round(width), height: Math.round(height) };
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        
        if (files.length === 0) return;
        
        if (files.length > 5) {
            toast.error('Maximum 5 images allowed');
            return;
        }

        if (imageFiles.length + files.length > 5) {
            toast.error(`You can only upload ${5 - imageFiles.length} more image(s)`);
            return;
        }

        try {
            toast.loading(`Compressing ${files.length} image(s)...`);

            const compressedFiles: File[] = [];
            const newPreviews: string[] = [];

            for (const file of files) {
                const compressedFile = await compressImage(file);
                compressedFiles.push(compressedFile);

                const reader = new FileReader();
                const preview = await new Promise<string>((resolve) => {
                    reader.onload = (e) => resolve(e.target?.result as string);
                    reader.readAsDataURL(compressedFile);
                });
                newPreviews.push(preview);
            }

            toast.dismiss();
            toast.success(`${files.length} image(s) compressed successfully`);

            setImageFiles(prev => [...prev, ...compressedFiles]);
            setImagePreviews(prev => [...prev, ...newPreviews]);
        } catch (error) {
            toast.dismiss();
            toast.error('Failed to process images');

        }
    };

    const removeImage = (index: number) => {
        setImageFiles(prev => prev.filter((_, i) => i !== index));
        setImagePreviews(prev => prev.filter((_, i) => i !== index));
    };

    const clearAllImages = () => {
        setImageFiles([]);
        setImagePreviews([]);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        try {
            uploadFormSchema.parse(uploadForm);

            if (imageFiles.length === 0) {
                toast.error('Please select at least one image to upload');
                return;
            }

            setIsUploading(true);

            const uploadedUrls: string[] = [];
            
            for (let i = 0; i < imageFiles.length; i++) {
                const imageFile = imageFiles[i];
                const formData = new FormData();
                formData.append('image', imageFile);
                formData.append('section', 'gallery');
                formData.append('width', '1920');
                formData.append('height', '1080');
                formData.append('quality', '80');

                const uploadResponse = await api.post('/cms/upload-image', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                });

                if (uploadResponse.data.success) {
                    uploadedUrls.push(uploadResponse.data.data.url);
                    toast.success(`Image ${i + 1}/${imageFiles.length} uploaded successfully`);
                } else {
                    throw new Error(`Failed to upload image ${i + 1}: ${uploadResponse.data.message}`);
                }
            }

            const galleryData = {
                title: uploadForm.title,
                description: uploadForm.description,
                file_urls: uploadedUrls,
                file_type: 'image',
                category: uploadForm.category,
                zone: uploadForm.zone
            };

            const galleryResponse = await api.post('/cms/gallery', galleryData);

            if (galleryResponse.data.success) {
                toast.success(`${imageFiles.length} image(s) uploaded and saved to gallery successfully!`);
                
                // Reset form
                setUploadForm({
                    title: '',
                    description: '',
                    category: '',
                    zone: ''
                });
                clearAllImages();
                onClose();
                onSuccess();
            } else {
                throw new Error(galleryResponse.data.message);
            }
        } catch (error: any) {
            if (error instanceof z.ZodError) {
                const errors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    if (err.path && err.path.length > 0) {
                        errors[err.path[0] as string] = err.message;
                    }
                });
                setValidationErrors(errors);
            } else {
                toast.error(error.response?.data?.message || 'Failed to upload images');
            }
        } finally {
            setIsUploading(false);
        }
    };

    const handleClose = () => {
        if (!isUploading) {
            setUploadForm({
                title: '',
                description: '',
                category: '',
                zone: ''
            });
            clearAllImages();
            setValidationErrors({});
            onClose();
        }
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6 border-b">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-gray-900">
                            Upload Multiple Images ke Gallery
                        </h2>
                        <button
                            onClick={handleClose}
                            disabled={isUploading}
                            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                <div className="p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Multiple Images Upload */}
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <label htmlFor="image-files" className="block text-sm font-medium text-gray-700">
                                    Upload Gambar (Maximum 5)
                                </label>
                                <input
                                    id="image-files"
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={handleFileChange}
                                    disabled={isUploading}
                                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-yellow-50 file:text-yellow-700 hover:file:bg-yellow-100 cursor-pointer disabled:opacity-50"
                                />
                                <p className="text-sm text-gray-500">
                                    Pilih 1-5 gambar. Recommended: 1920x1080px atau lebih besar. Gambar akan dikompress secara automatik.
                                </p>
                            </div>

                            {/* Image Previews */}
                            {imagePreviews.length > 0 && (
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Preview ({imagePreviews.length}/5)
                                        </label>
                                        <button
                                            type="button"
                                            onClick={clearAllImages}
                                            disabled={isUploading}
                                            className="text-sm text-red-600 hover:text-red-800 transition-colors disabled:opacity-50"
                                        >
                                            Clear All
                                        </button>
                                    </div>
                                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                                        {imagePreviews.map((preview, index) => (
                                            <div key={index} className="relative">
                                                <img
                                                    src={preview}
                                                    alt={`Preview ${index + 1}`}
                                                    className="w-full h-32 object-cover rounded-lg border"
                                                />
                                                <button
                                                    type="button"
                                                    className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1 rounded-full transition-colors"
                                                    onClick={() => removeImage(index)}
                                                    disabled={isUploading}
                                                >
                                                    <X className="w-3 h-3" />
                                                </button>
                                                <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                                    {index + 1}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Form Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label htmlFor="title" className="block text-sm font-medium text-gray-700">Title</label>
                                <input
                                    id="title"
                                    type="text"
                                    value={uploadForm.title}
                                    onChange={(e) => setUploadForm(prev => ({ ...prev, title: e.target.value }))}
                                    disabled={isUploading}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                    placeholder="Masukkan title gambar"
                                />
                                {validationErrors.title && (
                                    <p className="text-sm text-red-500">{validationErrors.title}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <label htmlFor="category" className="block text-sm font-medium text-gray-700">Category</label>
                                <select
                                    id="category"
                                    value={uploadForm.category}
                                    onChange={(e) => setUploadForm(prev => ({ ...prev, category: e.target.value }))}
                                    disabled={isUploading}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                >
                                    <option value="">Pilih category</option>
                                    <option value="ceremony">Majlis Rasmi</option>
                                    <option value="football">Bola Sepak</option>
                                    <option value="netball">Bola Jaring</option>
                                    <option value="badminton">Badminton</option>
                                    <option value="volleyball">Bola Tampar</option>
                                    <option value="table-tennis">Ping Pong</option>
                                    <option value="traditional">Sukan Rakyat</option>
                                </select>
                                {validationErrors.category && (
                                    <p className="text-sm text-red-500">{validationErrors.category}</p>
                                )}
                            </div>
                        </div>

                        <div className="space-y-2">
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                            <textarea
                                id="description"
                                rows={3}
                                value={uploadForm.description}
                                onChange={(e) => setUploadForm(prev => ({ ...prev, description: e.target.value }))}
                                disabled={isUploading}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                placeholder="Masukkan description gambar"
                            />
                            {validationErrors.description && (
                                <p className="text-sm text-red-500">{validationErrors.description}</p>
                            )}
                        </div>

                        {/* Zone field - only show for non-zone users */}
                        {user?.role !== 'zone' && (
                            <div className="space-y-2">
                                <label htmlFor="zone" className="block text-sm font-medium text-gray-700">Zone</label>
                                <select
                                    id="zone"
                                    value={uploadForm.zone}
                                    onChange={(e) => setUploadForm(prev => ({ ...prev, zone: e.target.value }))}
                                    disabled={isUploading}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50"
                                >
                                    <option value="">Pilih zone</option>
                                    <option value="Semua Zone">Semua Zone</option>
                                    <option value="Zone A">Zone A</option>
                                    <option value="Zone B">Zone B</option>
                                    <option value="Zone C">Zone C</option>
                                    <option value="BAKAT-KKD">Bakat KKD</option>
                                </select>
                                {validationErrors.zone && (
                                    <p className="text-sm text-red-500">{validationErrors.zone}</p>
                                )}
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-4">
                            <button
                                type="button"
                                onClick={handleClose}
                                disabled={isUploading}
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                            >
                                Batal
                            </button>
                            <button
                                type="submit"
                                disabled={imageFiles.length === 0 || isUploading}
                                className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold rounded-lg transition-colors disabled:opacity-50 flex items-center"
                            >
                                {isUploading ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Uploading {imageFiles.length} image(s)...
                                    </>
                                ) : (
                                    <>
                                        <Upload className="w-4 h-4 mr-2" />
                                        Upload {imageFiles.length > 0 ? `${imageFiles.length} Image(s)` : 'Images'}
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default UploadMultipleModal;
