import React, { useState, useEffect } from 'react';
import { X, Calendar, Eye, User, MapPin, Tag, ChevronLeft, ChevronRight, Download, Share2 } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import api from '@/lib/axios';

interface MediaItem {
    id: number;
    title: string;
    description: string;
    file_url: string;
    file_urls?: string[];
    image_count?: number;
    file_type: 'image' | 'video';
    category: string;
    zone: string;
    views: number;
    uploaded_at: string;
    uploader?: string;
}

interface ViewMediaModalProps {
    isOpen: boolean;
    onClose: () => void;
    media: MediaItem | null;
    onDownload?: (url: string, filename: string) => void;
    onShare?: (media: MediaItem) => void;
}

const ViewMediaModal: React.FC<ViewMediaModalProps> = ({
    isOpen,
    onClose,
    media,
    onDownload,
    onShare
}) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [currentViews, setCurrentViews] = useState(0);

    // Increment views when modal opens
    useEffect(() => {
        if (isOpen && media) {
            setCurrentViews(media.views);
            setCurrentImageIndex(0);

            // Increment view count
            const incrementViews = async () => {
                try {
                    const response = await api.post(`/public/gallery/${media.id}/view`);
                    if (response.data && response.data.views) {
                        setCurrentViews(response.data.views);
                    } else {
                        setCurrentViews(prev => prev + 1);
                    }
                } catch (error) {

                    // Still increment locally if API fails
                    setCurrentViews(prev => prev + 1);
                }
            };

            incrementViews();
        }
    }, [isOpen, media]);

    if (!isOpen || !media) return null;

    // Get all images (use file_urls if available, fallback to single file_url)
    const allImages = media.file_urls && media.file_urls.length > 0
        ? media.file_urls
        : [media.file_url];

    const isMultipleImages = allImages.length > 1;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('ms-MY', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getCategoryName = (category: string) => {
        const categories: { [key: string]: string } = {
            'tournament': 'Pertandingan',
            'training': 'Latihan',
            'ceremony': 'Majlis',
            'general': 'Am',
            'football': 'Bola Sepak',
            'netball': 'Bola Jaring',
            'badminton': 'Badminton',
            'volleyball': 'Bola Tampar'
        };
        return categories[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: { [key: string]: string } = {
            'tournament': 'bg-red-500/20 text-red-400 border-red-500/30',
            'training': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
            'ceremony': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
            'general': 'bg-green-500/20 text-green-400 border-green-500/30',
            'football': 'bg-red-500/20 text-red-400 border-red-500/30',
            'netball': 'bg-pink-500/20 text-pink-400 border-pink-500/30',
            'badminton': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
            'volleyball': 'bg-orange-500/20 text-orange-400 border-orange-500/30'
        };
        return colors[category] || 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    };

    const goToPrevious = () => {
        setCurrentImageIndex((prev) =>
            prev === 0 ? allImages.length - 1 : prev - 1
        );
    };

    const goToNext = () => {
        setCurrentImageIndex((prev) =>
            prev === allImages.length - 1 ? 0 : prev + 1
        );
    };

    const handleDownload = () => {
        if (onDownload && allImages[currentImageIndex]) {
            const filename = `${media.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${currentImageIndex + 1}.jpg`;
            onDownload(allImages[currentImageIndex], filename);
        }
    };

    const handleShare = () => {
        if (onShare) {
            onShare(media);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
            <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[95vh] flex flex-col">
                {/* Fixed Header */}
                <div className="flex-shrink-0 bg-white border-b p-4 sm:p-6 rounded-t-2xl">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                            Lihat Media
                        </h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                        >
                            <X className="w-5 h-5 sm:w-6 sm:h-6" />
                        </button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                        {/* Media Display */}
                        <div className="space-y-4">
                            <div className="relative rounded-xl overflow-hidden bg-gray-100">
                                {media.file_type === 'image' ? (
                                    <div className="relative">
                                        <OptimizedImage
                                            src={allImages[currentImageIndex]}
                                            alt={media.title}
                                            className="w-full h-64 sm:h-80 lg:h-96 object-cover"
                                            lazy={false}
                                            enableCache={true}
                                        />

                                        {/* Navigation arrows for multiple images */}
                                        {isMultipleImages && (
                                            <>
                                                <button
                                                    onClick={goToPrevious}
                                                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors"
                                                >
                                                    <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                                                </button>
                                                <button
                                                    onClick={goToNext}
                                                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors"
                                                >
                                                    <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                                </button>

                                                {/* Image counter */}
                                                <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs sm:text-sm">
                                                    {currentImageIndex + 1} / {allImages.length}
                                                </div>
                                            </>
                                        )}
                                    </div>
                                ) : (
                                    <video
                                        src={allImages[currentImageIndex]}
                                        controls
                                        className="w-full h-64 sm:h-80 lg:h-96 object-cover"
                                    />
                                )}
                            </div>

                            {/* Image thumbnails for multiple images */}
                            {isMultipleImages && (
                                <div className="flex gap-2 overflow-x-auto pb-2">
                                    {allImages.map((imageUrl, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setCurrentImageIndex(index)}
                                            className={`flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                                                index === currentImageIndex
                                                    ? 'border-blue-500'
                                                    : 'border-gray-200 hover:border-gray-300'
                                            }`}
                                        >
                                            <OptimizedImage
                                                src={imageUrl}
                                                alt={`${media.title} ${index + 1}`}
                                                className="w-full h-full object-cover"
                                                lazy={false}
                                                enableCache={true}
                                            />
                                        </button>
                                    ))}
                                </div>
                            )}

                            {/* Category Badge */}
                            <div className="flex justify-center">
                                <span className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-semibold border ${getCategoryColor(media.category)}`}>
                                    <Tag className="h-3 w-3 sm:h-4 sm:w-4 inline mr-1 sm:mr-2" />
                                    {getCategoryName(media.category)}
                                </span>
                            </div>
                        </div>

                        {/* Media Info */}
                        <div className="space-y-4 sm:space-y-6">
                            <div>
                                <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-2">
                                    {media.title}
                                </h3>
                                <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                                    {media.description}
                                </p>
                            </div>

                            {/* Metadata */}
                            <div className="space-y-3 sm:space-y-4">
                                <div className="flex items-center gap-2 sm:gap-3 text-gray-600 text-sm sm:text-base">
                                    <Calendar className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>Dimuat naik: {formatDate(media.uploaded_at)}</span>
                                </div>

                                <div className="flex items-center gap-2 sm:gap-3 text-gray-600 text-sm sm:text-base">
                                    <Eye className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>{currentViews} tontonan</span>
                                </div>

                                <div className="flex items-center gap-2 sm:gap-3 text-gray-600 text-sm sm:text-base">
                                    <MapPin className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>{media.zone}</span>
                                </div>

                                {media.uploader && (
                                    <div className="flex items-center gap-2 sm:gap-3 text-gray-600 text-sm sm:text-base">
                                        <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                        <span>Dimuat naik oleh: {media.uploader}</span>
                                    </div>
                                )}
                            </div>

                            {/* Stats Card */}
                            <div className="bg-gray-50 rounded-xl p-3 sm:p-4">
                                <h4 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Statistik Media</h4>
                                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                                    <div className="text-center">
                                        <div className="text-xl sm:text-2xl font-bold text-blue-600">{currentViews}</div>
                                        <div className="text-xs sm:text-sm text-gray-600">Tontonan</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-xl sm:text-2xl font-bold text-green-600">
                                            {media.file_type === 'image' ? 'Foto' : 'Video'}
                                        </div>
                                        <div className="text-xs sm:text-sm text-gray-600">Jenis Media</div>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                                <button
                                    onClick={handleDownload}
                                    className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base"
                                >
                                    <Download className="w-4 h-4" />
                                    Muat Turun
                                </button>
                                <button
                                    onClick={handleShare}
                                    className="flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm sm:text-base"
                                >
                                    <Share2 className="w-4 h-4" />
                                    Kongsi
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Fixed Footer */}
                <div className="flex-shrink-0 border-t p-4 sm:p-6 bg-gray-50 rounded-b-2xl">
                    <div className="flex justify-end">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 sm:px-6 sm:py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base"
                        >
                            Tutup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ViewMediaModal;
