import { Calendar } from 'lucide-react';

interface MatchScheduleHeroProps {
    className?: string;
}

export default function MatchScheduleHero({ className = "" }: MatchScheduleHeroProps) {
    return (
        <div className={`text-center mb-16 ${className}`}>
            <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-500/20 rounded-full mb-6">
                <Calendar className="w-10 h-10 text-blue-400" />
            </div>
            <h1 className="text-5xl md:text-6xl font-black text-white mb-6 drop-shadow-lg">
                <span className="text-yellow-400">JADUAL</span> PERLAWANAN
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                Jadual lengkap semua perlawanan Sukan Intra Kor Kesihatan DiRaja 2025
                <br />
                <span className="text-yellow-300 font-semibold">21 - 25 Julai 2025 | PU Sendayan</span>
            </p>
        </div>
    );
}
