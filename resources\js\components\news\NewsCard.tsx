import React from 'react';
import { Calendar, Eye, User, MapPin, Tag, AlertCircle } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import { useAuth } from '@/contexts/auth-context';

interface NewsItem {
    id: number;
    title: string;
    excerpt: string;
    content?: string;
    featured_image?: string;
    author: string;
    published_at: string;
    category: string;
    tags: string[];
    views: number;
    is_important: boolean;
    zone: string;
}

interface NewsCardProps {
    item: NewsItem;
    onView: (item: NewsItem) => void;
    onEdit?: (item: NewsItem) => void;
    onDelete?: (item: NewsItem) => void;
}

const NewsCard: React.FC<NewsCardProps> = ({
    item,
    onView,
    onEdit,
    onDelete
}) => {
    const { isAuthenticated, user } = useAuth();

    // Check if user can edit/delete this item
    const canModify = isAuthenticated && (
        user?.role === 'superadmin' || 
        user?.role === 'admin' || 
        user?.role === 'media'
    );

    const getCategoryName = (category: string) => {
        const categories: { [key: string]: string } = {
            'announcement': 'Pengumuman',
            'registration': 'Pendaftaran',
            'schedule': 'Jadual',
            'results': 'Keputusan',
            'tips': 'Tips',
            'general': 'Am'
        };
        return categories[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: { [key: string]: string } = {
            'announcement': 'bg-red-500/20 text-red-300 border-red-500/30',
            'registration': 'bg-blue-500/20 text-blue-300 border-blue-500/30',
            'schedule': 'bg-purple-500/20 text-purple-300 border-purple-500/30',
            'results': 'bg-green-500/20 text-green-300 border-green-500/30',
            'tips': 'bg-orange-500/20 text-orange-300 border-orange-500/30',
            'general': 'bg-gray-500/20 text-gray-300 border-gray-500/30'
        };
        return colors[category] || 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    };

    const formatCardDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('ms-MY', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <div className={`group bg-black/60 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 ${
            item.is_important 
                ? 'border-red-500/50 hover:border-red-500/70' 
                : 'border-yellow-400/30 hover:border-yellow-400/60'
        } overflow-hidden shadow-2xl hover:scale-105 transition-all duration-300`}>
            <div className="relative overflow-hidden">
                <OptimizedImage
                    src={item.featured_image || '/images/default-news.jpg'}
                    alt={item.title}
                    className="w-full h-64 2xl:h-80 xl:h-72 lg:h-64 md:h-56 sm:h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    lazy={true}
                    enableCache={true}
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>

                {/* Important Badge */}
                {item.is_important && (
                    <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                        <span className="px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold bg-red-500/20 text-red-300 border border-red-500/30">
                            <AlertCircle className="h-3 w-3 2xl:h-4 2xl:w-4 xl:h-3 xl:w-3 lg:h-3 lg:w-3 md:h-2 md:w-2 sm:h-2 sm:w-2 inline mr-1" />
                            <span className="hidden sm:inline">Penting</span>
                        </span>
                    </div>
                )}

                {/* Category Badge */}
                <div className="absolute top-4 2xl:top-6 xl:top-5 lg:top-4 md:top-3 sm:top-2 right-4 2xl:right-6 xl:right-5 lg:right-4 md:right-3 sm:right-2">
                    <span className={`px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${getCategoryColor(item.category)}`}>
                        <span className="hidden md:inline">{getCategoryName(item.category)}</span>
                        <span className="md:hidden">📰</span>
                    </span>
                </div>

                {/* Zone Badge */}
                <div className="absolute bottom-4 2xl:bottom-6 xl:bottom-5 lg:bottom-4 md:bottom-3 sm:bottom-2 left-4 2xl:left-6 xl:left-5 lg:left-4 md:left-3 sm:left-2">
                    <span className="px-3 py-1 2xl:px-4 2xl:py-2 xl:px-3 xl:py-1 lg:px-3 lg:py-1 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                        {item.zone === 'all' ? 'Semua Zone' : item.zone}
                    </span>
                </div>
            </div>

            <div className="p-6 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-4">
                <h3 className="font-bold text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-base sm:text-sm text-white mb-3 2xl:mb-4 xl:mb-3 lg:mb-3 md:mb-2 sm:mb-2 group-hover:text-yellow-400 transition-colors line-clamp-2">
                    {item.title}
                </h3>
                <p className="text-gray-300 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2 line-clamp-3">
                    {item.excerpt}
                </p>

                <div className="flex items-center justify-between text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-4 md:mb-3 sm:mb-2">
                    <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        <span className="hidden md:inline">{formatCardDate(item.published_at)}</span>
                        <span className="md:hidden">📅</span>
                    </div>
                    <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2" />
                        <span>{item.views.toLocaleString()}</span>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2 2xl:space-x-3 xl:space-x-2 lg:space-x-2 md:space-x-1 sm:space-x-1">
                    <button 
                        onClick={() => onView(item)}
                        className="flex-1 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 px-4 py-2 2xl:px-6 2xl:py-3 xl:px-5 xl:py-2 lg:px-4 lg:py-2 md:px-3 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold transition-all duration-300 border border-yellow-500/30 hover:border-yellow-500/50"
                    >
                        <Eye className="h-4 w-4 2xl:h-5 2xl:w-5 xl:h-4 xl:w-4 lg:h-3 lg:w-3 md:h-3 md:w-3 sm:h-2 sm:w-2 inline mr-1" />
                        <span className="hidden sm:inline">Baca</span>
                    </button>

                    {/* Edit/Delete buttons for authorized users */}
                    {canModify && onEdit && (
                        <button 
                            onClick={() => onEdit(item)}
                            className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-purple-500/30 hover:border-purple-500/50"
                        >
                            ✏️
                        </button>
                    )}

                    {canModify && onDelete && (
                        <button 
                            onClick={() => onDelete(item)}
                            className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-3 py-2 2xl:px-4 2xl:py-3 xl:px-3 xl:py-2 lg:px-3 lg:py-2 md:px-2 md:py-1 sm:px-2 sm:py-1 rounded-lg 2xl:rounded-xl xl:rounded-lg lg:rounded-lg md:rounded-md sm:rounded-md text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs transition-all duration-300 border border-red-500/30 hover:border-red-500/50"
                        >
                            🗑️
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NewsCard;
