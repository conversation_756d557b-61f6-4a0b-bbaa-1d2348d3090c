import React, { useState, useEffect } from 'react';
import { X, Calendar, Eye, User, MapPin, Tag, AlertCircle, Share2 } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';
import api from '@/lib/axios';

interface NewsItem {
    id: number;
    title: string;
    excerpt: string;
    content: string;
    featured_image?: string;
    author: string;
    published_at: string;
    category: string;
    tags: string[];
    views: number;
    is_important: boolean;
    zone: string;
}

interface ViewNewsModalProps {
    isOpen: boolean;
    onClose: () => void;
    news: NewsItem | null;
    onShare?: (news: NewsItem) => void;
}

const ViewNewsModal: React.FC<ViewNewsModalProps> = ({
    isOpen,
    onClose,
    news,
    onShare
}) => {
    const [currentViews, setCurrentViews] = useState(0);

    // Increment views when modal opens
    useEffect(() => {
        if (isOpen && news) {
            setCurrentViews(news.views);
            
            // Increment view count
            const incrementViews = async () => {
                try {
                    const response = await api.post(`/public/news/${news.id}/view`);
                    if (response.data && response.data.views) {
                        setCurrentViews(response.data.views);
                    } else {
                        setCurrentViews(prev => prev + 1);
                    }
                } catch (error) {

                    // Still increment locally if API fails
                    setCurrentViews(prev => prev + 1);
                }
            };
            
            incrementViews();
        }
    }, [isOpen, news]);

    if (!isOpen || !news) return null;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('ms-MY', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getCategoryName = (category: string) => {
        const categories: { [key: string]: string } = {
            'announcement': 'Pengumuman',
            'registration': 'Pendaftaran',
            'schedule': 'Jadual',
            'results': 'Keputusan',
            'tips': 'Tips',
            'general': 'Am'
        };
        return categories[category] || category;
    };

    const getCategoryColor = (category: string) => {
        const colors: { [key: string]: string } = {
            'announcement': 'bg-red-500/20 text-red-400 border-red-500/30',
            'registration': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
            'schedule': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
            'results': 'bg-green-500/20 text-green-400 border-green-500/30',
            'tips': 'bg-orange-500/20 text-orange-400 border-orange-500/30',
            'general': 'bg-gray-500/20 text-gray-400 border-gray-500/30'
        };
        return colors[category] || 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    };

    const handleShare = () => {
        if (onShare) {
            onShare(news);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4">
            <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[95vh] flex flex-col">
                {/* Fixed Header */}
                <div className="flex-shrink-0 bg-white border-b p-4 sm:p-6 rounded-t-2xl">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                            Baca Berita
                        </h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                        >
                            <X className="w-5 h-5 sm:w-6 sm:h-6" />
                        </button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                    <div className="max-w-4xl mx-auto">
                        {/* Featured Image */}
                        {news.featured_image && (
                            <div className="mb-6">
                                <OptimizedImage
                                    src={news.featured_image}
                                    alt={news.title}
                                    className="w-full h-64 sm:h-80 lg:h-96 object-cover rounded-xl"
                                    lazy={false}
                                    enableCache={true}
                                />
                            </div>
                        )}

                        {/* Article Header */}
                        <div className="mb-6">
                            {/* Important Badge */}
                            {news.is_important && (
                                <div className="mb-4">
                                    <span className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-sm font-semibold bg-red-500/20 text-red-400 border border-red-500/30">
                                        <AlertCircle className="h-4 w-4 mr-2" />
                                        Berita Penting
                                    </span>
                                </div>
                            )}

                            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                                {news.title}
                            </h1>

                            <p className="text-lg sm:text-xl text-gray-600 mb-6 leading-relaxed">
                                {news.excerpt}
                            </p>

                            {/* Metadata */}
                            <div className="flex flex-wrap gap-4 text-sm sm:text-base text-gray-600 mb-6">
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>Diterbitkan: {formatDate(news.published_at)}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Eye className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>{currentViews} tontonan</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>Oleh: {news.author}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                    <span>{news.zone === 'all' ? 'Semua Zone' : news.zone}</span>
                                </div>
                            </div>

                            {/* Category and Tags */}
                            <div className="flex flex-wrap gap-2 mb-6">
                                <span className={`px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-sm font-semibold border ${getCategoryColor(news.category)}`}>
                                    <Tag className="h-3 w-3 sm:h-4 sm:w-4 inline mr-1 sm:mr-2" />
                                    {getCategoryName(news.category)}
                                </span>
                                
                                {news.tags && news.tags.map((tag, index) => (
                                    <span key={index} className="px-3 py-1.5 rounded-full text-sm bg-gray-100 text-gray-700 border border-gray-200">
                                        #{tag}
                                    </span>
                                ))}
                            </div>
                        </div>

                        {/* Article Content */}
                        <div className="prose prose-lg max-w-none mb-8">
                            <div 
                                className="text-gray-800 leading-relaxed"
                                dangerouslySetInnerHTML={{ __html: news.content }}
                            />
                        </div>

                        {/* Stats Card */}
                        <div className="bg-gray-50 rounded-xl p-4 sm:p-6 mb-6">
                            <h4 className="font-semibold text-gray-900 mb-4 text-sm sm:text-base">Statistik Berita</h4>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-center">
                                    <div className="text-2xl sm:text-3xl font-bold text-blue-600">{currentViews}</div>
                                    <div className="text-xs sm:text-sm text-gray-600">Tontonan</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl sm:text-3xl font-bold text-green-600">
                                        {getCategoryName(news.category)}
                                    </div>
                                    <div className="text-xs sm:text-sm text-gray-600">Kategori</div>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-3">
                            <button
                                onClick={handleShare}
                                className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base"
                            >
                                <Share2 className="w-4 h-4" />
                                Kongsi Berita
                            </button>
                        </div>
                    </div>
                </div>

                {/* Fixed Footer */}
                <div className="flex-shrink-0 border-t p-4 sm:p-6 bg-gray-50 rounded-b-2xl">
                    <div className="flex justify-end">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 sm:px-6 sm:py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base"
                        >
                            Tutup
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ViewNewsModal;
