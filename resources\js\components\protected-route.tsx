import { ReactNode, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth-context';

interface ProtectedRouteProps {
  children: ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated, checkAuth } = useAuth();
  const location = useLocation();

  useEffect(() => {
    // If we have a token but no user, try to check auth
    const token = localStorage.getItem('auth_token');
    if (token && !user && !isLoading) {
      checkAuth();
    }
  }, [user, isLoading, checkAuth]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memeriksa status pengguna...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // If user must change password, redirect to password change page
  if (user?.must_change_password && location.pathname !== '/auth/password/change') {
    return <Navigate to="/auth/password/change" replace />;
  }

  // If authenticated, render children
  return <>{children}</>;
}
