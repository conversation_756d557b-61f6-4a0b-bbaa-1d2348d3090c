import { Link } from 'react-router-dom';
import { Users, Trophy, Calendar } from 'lucide-react';
import { useHomepageContent } from '@/hooks/usePageContent';

interface CTASectionProps {
    user?: any;
    settings?: Record<string, any>;
}

export default function CTASection({
    user,
    settings
}: CTASectionProps) {
    const { data: content = {}, isLoading: loading } = useHomepageContent();

    // Use content from CMS or fallback to defaults
    const ctaContent = content.cta || {};
    const title = ctaContent.title || "Sertai Sukan Intra Kor Kesihatan DiRaja 2025";
    const subtitle = ctaContent.subtitle || "21 - 25 Julai 2025 | PU Sendayan";
    const description = ctaContent.description || "Saksikan pertandingan sukan intra-zon yang paling meriah tahun ini. Tunjukkan sokongan anda kepada para atlet!";
    const baseBackgroundImage = ctaContent.background_image || "/images/default.jpg";
    // Use background image directly
    const backgroundImage = baseBackgroundImage;

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }
    return (
        <section
            id="sertai-sukan"
            className="pt-16 sm:pt-20 lg:pt-12 xl:pt-28 pb-8 sm:pb-12 lg:pb-6 xl:pb-20 h-screen bg-cover bg-center bg-no-repeat relative flex items-center justify-center scroll-mt-16 sm:scroll-mt-20 md:scroll-mt-24 lg:scroll-mt-20 xl:scroll-mt-24"
            style={{
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${backgroundImage}')`
            }}
        >
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-600/80 via-blue-600/70 to-red-600/60"></div>

            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute top-10 left-10 w-16 h-16 lg:w-18 lg:h-18 xl:w-24 xl:h-24 bg-yellow-400/20 rounded-full animate-pulse"></div>
                <div className="absolute top-1/4 right-20 w-12 h-12 lg:w-14 lg:h-14 xl:w-20 xl:h-20 bg-green-400/20 rounded-full animate-bounce"></div>
                <div className="absolute bottom-20 left-1/4 w-20 h-20 lg:w-22 lg:h-22 xl:w-28 xl:h-28 bg-blue-400/20 rounded-full animate-pulse"></div>
                <div className="absolute bottom-10 right-10 w-10 h-10 lg:w-11 lg:h-11 xl:w-16 xl:h-16 bg-red-400/20 rounded-full animate-bounce"></div>
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                <div className="text-center">
                    {/* Main Title */}
                    <h2 className="text-2xl sm:text-3xl lg:text-3xl xl:text-5xl font-black text-white mb-2 sm:mb-3 lg:mb-3 xl:mb-6 drop-shadow-2xl">
                        <span className="bg-gradient-to-r from-yellow-400 via-red-500 to-blue-500 bg-clip-text text-transparent">
                            {title}
                        </span>
                    </h2>

                    {/* Subtitle */}
                    <p className="text-lg sm:text-xl lg:text-xl xl:text-3xl text-yellow-300 font-bold mb-2 sm:mb-3 lg:mb-3 xl:mb-6 drop-shadow-lg">
                        {subtitle}
                    </p>

                    {/* Description */}
                    <p className="text-sm sm:text-base lg:text-base xl:text-xl text-white/90 mb-4 sm:mb-6 lg:mb-5 xl:mb-12 leading-relaxed max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto font-medium">
                        {description}
                    </p>

                    {/* Action Buttons */}
                    {!user && (
                        <div className="flex justify-center items-center mb-4 sm:mb-6 lg:mb-5 xl:mb-12">
                            <Link
                                to="/auth/login"
                                className="group w-full sm:w-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 sm:px-8 lg:px-10 xl:px-16 py-3 sm:py-4 lg:py-4 xl:py-7 rounded-full font-bold text-sm sm:text-base lg:text-lg xl:text-2xl shadow-2xl hover:shadow-blue-500/50 transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-3 lg:gap-3 xl:gap-5"
                            >
                                <Users className="w-5 h-5 sm:w-5 sm:h-5 lg:w-6 lg:h-6 xl:w-8 xl:h-8" />
                                LOG MASUK
                            </Link>
                        </div>
                    )}

                    {/* Features Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-5 xl:gap-10">
                        <div className="group bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border border-white/20 rounded-xl lg:rounded-xl xl:rounded-3xl p-3 sm:p-4 lg:p-5 xl:p-10 text-center hover:scale-105 hover:border-white/40 transition-all duration-300 shadow-2xl">
                            <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 xl:w-24 xl:h-24 bg-gradient-to-br from-yellow-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-3 xl:mb-8 group-hover:scale-110 transition-transform shadow-xl">
                                <Trophy className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 xl:h-12 xl:w-12 text-white" />
                            </div>
                            <h4 className="text-xs sm:text-sm lg:text-base xl:text-xl font-bold text-white mb-1 sm:mb-2 lg:mb-2 xl:mb-5 group-hover:text-yellow-400 transition-colors">
                                Pertandingan Berkualiti
                            </h4>
                            <p className="text-xs sm:text-xs lg:text-sm xl:text-lg text-white/80 font-medium leading-relaxed">
                                Pelbagai kategori sukan untuk semua peringkat
                            </p>
                        </div>

                        <div className="group bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border border-white/20 rounded-xl lg:rounded-xl xl:rounded-3xl p-3 sm:p-4 lg:p-5 xl:p-10 text-center hover:scale-105 hover:border-white/40 transition-all duration-300 shadow-2xl">
                            <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 xl:w-24 xl:h-24 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-3 xl:mb-8 group-hover:scale-110 transition-transform shadow-xl">
                                <Users className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 xl:h-12 xl:w-12 text-white" />
                            </div>
                            <h4 className="text-xs sm:text-sm lg:text-base xl:text-xl font-bold text-white mb-1 sm:mb-2 lg:mb-2 xl:mb-5 group-hover:text-yellow-400 transition-colors">
                                Komuniti Aktif
                            </h4>
                            <p className="text-xs sm:text-xs lg:text-sm xl:text-lg text-white/80 font-medium leading-relaxed">
                                Bergabung dengan 500+ peserta dari 3 zon
                            </p>
                        </div>

                        <div className="group bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border border-white/20 rounded-xl lg:rounded-xl xl:rounded-3xl p-3 sm:p-4 lg:p-5 xl:p-10 text-center hover:scale-105 hover:border-white/40 transition-all duration-300 shadow-2xl">
                            <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 xl:w-24 xl:h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-3 xl:mb-8 group-hover:scale-110 transition-transform shadow-xl">
                                <Calendar className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 xl:h-12 xl:w-12 text-white" />
                            </div>
                            <h4 className="text-xs sm:text-sm lg:text-base xl:text-xl font-bold text-white mb-1 sm:mb-2 lg:mb-2 xl:mb-5 group-hover:text-yellow-400 transition-colors">
                                Acara Terancang
                            </h4>
                            <p className="text-xs sm:text-xs lg:text-sm xl:text-lg text-white/80 font-medium leading-relaxed">
                                Jadual yang tersusun rapi dan profesional
                            </p>
                        </div>
                    </div>

                    {/* Bottom Call to Action */}
                    <div className="text-center mt-3 sm:mt-4 lg:mt-4 xl:mt-12">
                        <p className="text-xs sm:text-xs lg:text-sm xl:text-lg text-white/70 font-medium">
                            Tempat terhad! Daftar sekarang sebelum terlambat.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    );
}
