import { useState, useEffect } from 'react';

interface CountdownProps {
    targetDate: string; // Format: 'YYYY-MM-DD'
}

interface TimeLeft {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
}

export default function Countdown({ targetDate }: CountdownProps) {
    const [timeLeft, setTimeLeft] = useState<TimeLeft>({
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
    });

    useEffect(() => {
        const calculateTimeLeft = () => {
            const difference = +new Date(targetDate) - +new Date();
            
            if (difference > 0) {
                setTimeLeft({
                    days: Math.floor(difference / (1000 * 60 * 60 * 24)),
                    hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
                    minutes: Math.floor((difference / 1000 / 60) % 60),
                    seconds: Math.floor((difference / 1000) % 60)
                });
            } else {
                setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
            }
        };

        calculateTimeLeft();
        const timer = setInterval(calculateTimeLeft, 1000);

        return () => clearInterval(timer);
    }, [targetDate]);



    return (
        <div className="space-y-6">
            {/* Large Days Box */}
            <div className="text-center group">
                <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-400/30 to-red-500/30 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm rounded-3xl p-6 border-2 border-white/30 group-hover:border-white/50 transition-all duration-300 group-hover:scale-105 shadow-2xl">
                        <span className="text-3xl sm:text-4xl md:text-5xl lg:text-4xl xl:text-8xl font-black text-white block leading-none drop-shadow-2xl">
                            {timeLeft.days.toString().padStart(2, '0')}
                        </span>
                    </div>
                </div>
                <span className="text-xs sm:text-sm lg:text-xs xl:text-xl text-white font-bold mt-1 sm:mt-2 lg:mt-1 xl:mt-3 block uppercase tracking-wider drop-shadow-lg">
                    HARI
                </span>
            </div>

            {/* Small Time Boxes - Hours, Minutes, Seconds */}
            <div className="grid grid-cols-3 gap-2 sm:gap-3">
                {[
                    { label: 'JAM', value: timeLeft.hours },
                    { label: 'MINIT', value: timeLeft.minutes },
                    { label: 'SAAT', value: timeLeft.seconds }
                ].map((unit, index) => (
                    <div key={index} className="text-center group">
                        <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5 rounded-2xl blur-sm group-hover:blur-none transition-all duration-300"></div>
                            <div className="relative bg-white/15 backdrop-blur-sm rounded-lg sm:rounded-xl lg:rounded-2xl p-2 sm:p-3 border border-white/25 group-hover:border-white/40 transition-all duration-300 group-hover:scale-105">
                                <span className="text-base sm:text-lg md:text-xl lg:text-lg xl:text-3xl font-black text-white block leading-none">
                                    {unit.value.toString().padStart(2, '0')}
                                </span>
                            </div>
                        </div>
                        <span className="text-xs lg:text-xs xl:text-sm text-white/90 font-semibold mt-0.5 lg:mt-0.5 xl:mt-1 block uppercase tracking-wider">
                            {unit.label}
                        </span>
                    </div>
                ))}
            </div>
        </div>
    );
}
