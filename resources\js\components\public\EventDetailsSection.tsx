import React from 'react';
import { Calendar, MapPin, Clock, Trophy } from 'lucide-react';
import Countdown from './Countdown';

export default function EventDetailsSection() {
    return (
        <section className="py-16 bg-gradient-to-br from-blue-50 to-white">
            <div className="max-w-7xl mx-auto px-6">
                {/* Section Header */}
                <div className="text-center mb-12">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Maklumat Acara
                    </h2>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Sertai kami dalam acara sukan intra yang meriah dan penuh semangat
                    </p>
                </div>

                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Event Details */}
                    <div className="space-y-8">
                        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                            <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                                <Trophy className="h-6 w-6 text-yellow-500 mr-3" />
                                Butiran Acara
                            </h3>
                            
                            <div className="space-y-6">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <Calendar className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Tarikh</h4>
                                        <p className="text-gray-600">21 - 25 Julai 2025</p>
                                        <p className="text-sm text-gray-500">5 hari berturut-turut</p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <Clock className="h-6 w-6 text-green-600" />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Masa</h4>
                                        <p className="text-gray-600">8:00 AM - 6:00 PM</p>
                                        <p className="text-sm text-gray-500">Setiap hari acara</p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                        <MapPin className="h-6 w-6 text-red-600" />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900 mb-1">Venue</h4>
                                        <p className="text-gray-600">PU Sendayan</p>
                                        <p className="text-sm text-gray-500">Kompleks Sukan Utama</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-3 gap-4">
                            <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white text-center">
                                <div className="text-2xl font-bold mb-1">15+</div>
                                <div className="text-sm opacity-90">Sukan</div>
                            </div>
                            <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white text-center">
                                <div className="text-2xl font-bold mb-1">500+</div>
                                <div className="text-sm opacity-90">Peserta</div>
                            </div>
                            <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl p-6 text-white text-center">
                                <div className="text-2xl font-bold mb-1">5</div>
                                <div className="text-sm opacity-90">Hari</div>
                            </div>
                        </div>
                    </div>

                    {/* Countdown */}
                    <div className="flex justify-center">
                        <div className="w-full max-w-md">
                            <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl p-8 text-white">
                                <h3 className="text-2xl font-bold text-center mb-6">
                                    Masa Bermula
                                </h3>
                                <Countdown targetDate="2025-07-21" />
                                
                                <div className="mt-8 text-center">
                                    <p className="text-blue-100 mb-4">
                                        Jangan lepaskan peluang untuk menyertai acara yang meriah ini!
                                    </p>
                                    <a
                                        href="/player-registration"
                                        className="inline-block bg-yellow-500 hover:bg-yellow-400 text-blue-900 px-6 py-3 rounded-lg font-semibold transition-colors duration-300"
                                    >
                                        Daftar Sekarang
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
