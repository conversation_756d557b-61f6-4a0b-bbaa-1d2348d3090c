import { Link } from 'react-router-dom';
import { Clock, Users, Trophy } from 'lucide-react';
import Countdown from './Countdown';
import { useHomepageContent } from '@/hooks/usePageContent';

interface HeroSectionProps {
    user?: any;
    settings?: Record<string, any>;
}

export default function HeroSection({
    user,
    settings
}: HeroSectionProps) {
    const { data: content = {}, isLoading: loading } = useHomepageContent();

    // Use content from CMS or fallback to defaults
    const heroContent = content.hero || {};
    const baseBackgroundImage = heroContent.background_image || "/images/default.jpg";
    const title = heroContent.title || "SUKAN INTRA";
    const subtitle = heroContent.subtitle || "KOR KESIHATAN DIRAJA 2025";
    const description = heroContent.description || "Platform terpusat untuk menguruskan semua aktiviti sukan intra-zon. Tarikh: 21 hingga 25 Julai 2025 | Venue: PU Sendayan";
    const countdownText = heroContent.countdown_text || "21 - 25 JULAI 2025 | PU SENDAYAN";
    // Use background image directly
    const backgroundImage = baseBackgroundImage;

    // Extract date from countdown text for countdown component
    const extractDateFromCountdown = (text: string): string => {
        const match = text.match(/(\d{1,2})\s*-\s*(\d{1,2})\s+(\w+)\s+(\d{4})/);
        if (match) {
            const [, startDay, endDay, month, year] = match;
            const monthMap: Record<string, string> = {
                'JANUARI': '01', 'FEBRUARI': '02', 'MAC': '03', 'APRIL': '04',
                'MEI': '05', 'JUN': '06', 'JULAI': '07', 'OGOS': '08',
                'SEPTEMBER': '09', 'OKTOBER': '10', 'NOVEMBER': '11', 'DISEMBER': '12'
            };
            const monthNum = monthMap[month.toUpperCase()] || '07';
            return `${year}-${monthNum}-${startDay.padStart(2, '0')}`;
        }
        return '2025-07-21'; // fallback
    };

    const targetDate = extractDateFromCountdown(countdownText);

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }
    return (
        <div
            id="hero"
            className="relative min-h-screen bg-cover bg-center bg-no-repeat scroll-mt-16 sm:scroll-mt-20 md:scroll-mt-24 lg:scroll-mt-20 xl:scroll-mt-24"
            style={{
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)), url('${backgroundImage}')`
            }}
        >
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/70 to-blue-800/50"></div>

            {/* Hero Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen pt-20 pb-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto w-full">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-8 xl:gap-12 items-center">
                        {/* Left Column - Main Content */}
                        <div className="text-center lg:text-left space-y-6 lg:space-y-6 xl:space-y-8">
                            {/* Main Title */}
                            <div className="space-y-3 lg:space-y-4">
                                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-7xl 2xl:text-8xl font-black leading-none tracking-tight">
                                    <span className="text-red-500 drop-shadow-2xl">SUKAN </span>
                                    <span className="text-blue-400 drop-shadow-2xl">INTRA</span>
                                </h1>

                                <div className="space-y-2">
                                    <h2 className="text-base sm:text-lg md:text-xl lg:text-xl xl:text-3xl 2xl:text-4xl font-bold drop-shadow-lg">
                                        <span className="text-yellow-400">{subtitle}</span>
                                    </h2>
                                    <h3 className="text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-4xl 2xl:text-5xl font-black text-white drop-shadow-lg">
                                        PU SENDAYAN
                                    </h3>
                                    <h4 className="text-base sm:text-lg md:text-xl lg:text-xl xl:text-3xl 2xl:text-4xl font-bold text-orange-400 drop-shadow-lg">
                                        {countdownText.split(' | ')[0]}
                                    </h4>
                                </div>
                            </div>



                            {/* CTA Buttons */}
                            <div className="flex justify-center lg:justify-start">
                                <a
                                    href="#sertai-sukan"
                                    className="group inline-flex items-center justify-center text-sm sm:text-base lg:text-base xl:text-2xl px-6 sm:px-8 lg:px-7 xl:px-12 py-3 sm:py-4 lg:py-3.5 xl:py-5 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-black rounded-lg lg:rounded-xl shadow-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-yellow-500/50"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        const element = document.getElementById('sertai-sukan');
                                        if (element) {
                                            element.scrollIntoView({
                                                behavior: 'smooth',
                                                block: 'start'
                                            });
                                        }
                                    }}
                                >
                                    <Trophy className="mr-2 lg:mr-3 h-4 w-4 sm:h-5 sm:w-5 lg:h-5 lg:w-5 xl:h-7 xl:w-7" />
                                    MAKLUMAT LANJUT
                                </a>
                            </div>
                        </div>

                        {/* Right Column - Countdown */}
                        <div className="flex items-center justify-center mt-6 lg:mt-0">
                            {/* Large Countdown Card */}
                            <div className="relative bg-gradient-to-br from-orange-500/40 to-red-500/40 backdrop-blur-xl rounded-2xl sm:rounded-3xl lg:rounded-2xl p-4 sm:p-6 lg:p-4 xl:p-8 border-2 border-orange-400/80 shadow-2xl overflow-hidden w-full max-w-xs sm:max-w-sm lg:max-w-xs xl:max-w-lg">
                                {/* Background Pattern */}
                                <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-transparent"></div>

                                {/* Animated Background Elements */}
                                <div className="absolute top-2 right-2 sm:top-3 sm:right-3 lg:top-2 lg:right-2 w-6 h-6 sm:w-8 sm:h-8 lg:w-6 lg:h-6 xl:w-16 xl:h-16 bg-gradient-to-br from-yellow-400/30 to-orange-500/30 rounded-full animate-pulse"></div>
                                <div className="absolute bottom-2 left-2 sm:bottom-3 sm:left-3 lg:bottom-2 lg:left-2 w-4 h-4 sm:w-6 sm:h-6 lg:w-4 lg:h-4 xl:w-12 xl:h-12 bg-gradient-to-br from-red-400/30 to-pink-500/30 rounded-full animate-bounce"></div>

                                <div className="relative text-center">
                                    <div className="flex items-center justify-center mb-2 sm:mb-3 lg:mb-2 xl:mb-6">
                                        <div className="bg-gradient-to-r from-orange-400 to-red-500 rounded-full p-1.5 sm:p-2 lg:p-1.5 xl:p-4 animate-pulse shadow-xl">
                                            <Clock className="h-4 w-4 sm:h-6 sm:w-6 lg:h-5 lg:w-5 xl:h-10 xl:w-10 text-white" />
                                        </div>
                                    </div>

                                    <h3 className="text-sm sm:text-lg lg:text-sm xl:text-3xl font-black text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-500 mb-2 sm:mb-3 lg:mb-2 xl:mb-6 drop-shadow-lg">
                                        BERMULA DALAM
                                    </h3>

                                    <div className="transform scale-75 sm:scale-85 lg:scale-70 xl:scale-100">
                                        <Countdown targetDate={targetDate} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
