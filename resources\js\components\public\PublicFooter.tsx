import { Trophy } from 'lucide-react';
import OptimizedImage from '@/components/OptimizedImage';

interface PublicFooterProps {
    settings?: Record<string, any>;
}

export default function PublicFooter({
    settings
}: PublicFooterProps) {
    const logoUrl = settings?.logo_url;
    const siteName = settings?.site_name || "SUKAN INTRA KOR KESIHATAN DIRAJA";
    const siteTagline = settings?.site_tagline || "Sukan Intra Kor Kesihatan DiRaja 2025";
    const copyrightText = settings?.copyright_text || "Semua hak terpelihara";
    const year = new Date().getFullYear();
    return (
        <footer className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 text-white py-3 sm:py-4 md:py-6 lg:py-4 xl:py-12 border-t-4 border-yellow-400">
            <div className="container mx-auto px-3 sm:px-6 lg:px-8">
                <div className="flex flex-col md:flex-row items-center justify-between">
                    <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-2.5 mb-2 sm:mb-3 md:mb-4 lg:mb-0">
                        <OptimizedImage
                            src={logoUrl ? (logoUrl.startsWith('/storage/') ? logoUrl : `/storage/${logoUrl}`) : '/images/logo.png'}
                            alt="Logo Sukan"
                            className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 lg:h-10 lg:w-10 xl:h-16 xl:w-16 object-contain"
                            fallbackSrc="/images/logo.png"
                            lazy={false}
                            enableCache={true}
                        />
                        <div>
                            <h3 className="text-sm sm:text-base md:text-lg lg:text-lg xl:text-3xl font-bold">
                                <span className="text-red-400">{settings?.header_title_part1 || 'SUKAN'}</span>{' '}
                                <span className="text-blue-300">{settings?.header_title_part2 || 'INTRA'}</span>
                            </h3>
                            <p className="font-medium text-xs sm:text-xs md:text-sm lg:text-sm xl:text-lg">
                                <span className="text-yellow-300">{settings?.header_subtitle || 'KOR KESIHATAN DIRAJA 2025'}</span>
                            </p>
                            <p className="text-yellow-300 font-bold text-xs sm:text-xs md:text-sm lg:text-sm xl:text-lg">{siteTagline}</p>
                        </div>
                    </div>
                    <div className="text-center md:text-right">
                        <p className="text-white text-xs sm:text-xs md:text-sm lg:text-sm xl:text-xl font-medium">© {year} {siteName}</p>
                        <p className="text-yellow-200 text-xs sm:text-xs md:text-sm lg:text-sm xl:text-lg">{copyrightText}</p>
                    </div>
                </div>
            </div>
        </footer>
    );
}
