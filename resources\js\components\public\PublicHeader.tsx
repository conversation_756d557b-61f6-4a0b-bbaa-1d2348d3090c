import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import OptimizedImage from '@/components/OptimizedImage';
import { useAuth } from '@/contexts/auth-context';

interface PublicHeaderProps {
    settings?: Record<string, any>;
}

// Main sections for home page (scroll to sections)
const homeSections = [
    { name: '<PERSON><PERSON>', href: '#hero', scrollTo: true },
    { name: 'Kate<PERSON>i <PERSON>', href: '#kategori-sukan', scrollTo: true }
];

// Other pages (navigate to different pages)
const otherPages = [
    { name: 'Jadual Perlawanan', href: '/match-schedule' },
    { name: 'Keputusan Pertandingan', href: '/tournament-results' },
    { name: 'Kedudukan Mata Keseluruhan', href: '/overall-standings' },
    { name: '<PERSON>urcara Kejohanan', href: '/championship-schedule' },
    { name: '<PERSON><PERSON>', href: '/gallery' },
    { name: '<PERSON><PERSON>', href: '/news' },
    { name: 'Peraturan Sukan', href: '/rules' }
];

export default function PublicHeader({
    settings
}: PublicHeaderProps) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const userMenuRef = useRef<HTMLDivElement>(null);
    const location = useLocation();
    const navigate = useNavigate();
    const { user, logout } = useAuth();

    // Handle click outside to close dropdowns
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false);
            }
        };

        if (isDropdownOpen || isUserMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isDropdownOpen, isUserMenuOpen]);

    // Handle navigation - scroll if on homepage, navigate if on other pages
    const handleNavigation = (elementId: string) => {
        // Check if we're on the homepage
        if (location.pathname === '/') {
            // We're on homepage, scroll to section
            const element = document.querySelector(elementId);
            if (element) {
                // Simple scrollIntoView with CSS scroll-margin-top
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        } else {
            // We're on another page, navigate to homepage with anchor using React Router
            navigate('/', { replace: true });
            // After navigation, scroll to the section
            setTimeout(() => {
                const element = document.querySelector(elementId);
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }, 500);
        }
        setIsMobileMenuOpen(false);
        setIsDropdownOpen(false);
    };

    const handleLogout = async () => {
        try {
            await logout();
            setIsUserMenuOpen(false);
            navigate('/');
        } catch (error) {

            // Force logout even if API fails
            setIsUserMenuOpen(false);
            navigate('/');
        }
    };

    return (
        <header className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 shadow-xl border-b-4 border-yellow-400 backdrop-blur-sm">
            <div className="container mx-auto px-3 sm:px-6 lg:px-8 py-2 sm:py-3 md:py-4 lg:py-3">
                <nav className="flex items-center justify-between">
                    {/* Logo and Site Name */}
                    <Link to="/" className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-3">
                        <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-3">
                            <OptimizedImage
                                src={settings?.logo_url ? (settings.logo_url.startsWith('/storage/') ? settings.logo_url : `/storage/${settings.logo_url}`) : '/images/logo.png'}
                                alt="Logo Sukan"
                                className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 lg:h-12 lg:w-12 xl:h-16 xl:w-16 object-contain"
                                fallbackSrc="/images/logo.png"
                                lazy={false}
                                enableCache={true}
                            />
                            <div className="text-white">
                                <h1 className="text-base sm:text-lg md:text-xl lg:text-xl xl:text-3xl font-bold tracking-wide">
                                    <span className="text-red-500">{settings?.header_title_part1 || 'SUKAN'}</span>{' '}
                                    <span className="text-blue-400">{settings?.header_title_part2 || 'INTRA'}</span>
                                </h1>
                                <p className="text-xs sm:text-sm md:text-base lg:text-base xl:text-xl font-medium">
                                    <span className="text-yellow-300">{settings?.header_subtitle || 'KOR KESIHATAN DIRAJA 2025'}</span>
                                </p>
                            </div>
                        </div>
                    </Link>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:flex items-center space-x-3 lg:space-x-4 xl:space-x-8">
                        {/* Home sections (scroll to) */}
                        {homeSections.map((section) => (
                            <button
                                key={section.href}
                                onClick={() => handleNavigation(section.href)}
                                className="text-white hover:text-yellow-300 font-bold text-sm lg:text-sm xl:text-lg tracking-wide transition-all duration-300 px-2 lg:px-3 xl:px-4 py-1.5 lg:py-2 rounded-lg hover:bg-gradient-to-r hover:from-blue-600/50 hover:to-blue-700/50 hover:scale-105 transform"
                            >
                                {section.name}
                            </button>
                        ))}

                        {/* Other pages dropdown */}
                        <div className="relative" ref={dropdownRef}>
                            <button
                                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                                className="text-white hover:text-yellow-300 font-bold text-sm lg:text-sm xl:text-lg tracking-wide transition-all duration-300 px-2 lg:px-3 xl:px-4 py-1.5 lg:py-2 rounded-lg hover:bg-gradient-to-r hover:from-blue-600/50 hover:to-blue-700/50 hover:scale-105 transform flex items-center"
                            >
                                Halaman Lain
                                <ChevronDown className={`ml-1 h-3 w-3 lg:h-3 lg:w-3 xl:h-4 xl:w-4 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
                            </button>

                            {/* Dropdown Menu */}
                            {isDropdownOpen && (
                                <div className="absolute top-full left-0 mt-2 w-44 lg:w-48 xl:w-64 bg-gradient-to-br from-blue-900 to-blue-800 rounded-xl shadow-2xl border-2 border-yellow-400/30 py-2 lg:py-3 z-50 backdrop-blur-sm">
                                    {otherPages.map((page) => (
                                        <Link
                                            key={page.href}
                                            to={page.href}
                                            className="block px-3 lg:px-4 xl:px-6 py-1.5 lg:py-2 xl:py-3 text-sm lg:text-sm xl:text-base text-white hover:bg-gradient-to-r hover:from-yellow-400/20 hover:to-orange-400/20 hover:text-yellow-300 transition-all duration-300 font-semibold tracking-wide hover:scale-105 transform hover:border-l-4 hover:border-yellow-400"
                                            onClick={() => setIsDropdownOpen(false)}
                                        >
                                            {page.name}
                                        </Link>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Auth Buttons & Mobile Menu */}
                    <div className="flex items-center gap-2 sm:gap-3 md:gap-4 lg:gap-3 xl:gap-4">
                        {user ? (
                            /* User Menu */
                            <div className="relative" ref={userMenuRef}>
                                <button
                                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                                    className="bg-yellow-500 text-blue-900 hover:bg-yellow-400 px-3 sm:px-4 md:px-5 lg:px-4 xl:px-6 py-2 sm:py-2.5 md:py-3 lg:py-2.5 xl:py-3 rounded-md font-semibold transition-colors duration-300 flex items-center shadow-lg text-sm sm:text-base md:text-lg lg:text-base xl:text-xl"
                                >
                                    <span>{user.name}</span>
                                    <ChevronDown className="ml-2 h-4 w-4" />
                                </button>

                                {/* User Dropdown */}
                                {isUserMenuOpen && (
                                    <div className="absolute top-full right-0 mt-2 w-48 lg:w-52 xl:w-64 bg-gradient-to-br from-blue-900 to-blue-800 rounded-xl shadow-2xl border-2 border-yellow-400/30 py-2 lg:py-3 z-50 backdrop-blur-sm">
                                        <div className="px-4 lg:px-5 xl:px-6 py-2 lg:py-2.5 xl:py-3 border-b border-white/20">
                                            <p className="text-sm text-white font-semibold">{user.name}</p>
                                            <p className="text-xs text-gray-300 capitalize">{user.role}</p>
                                        </div>
                                        <Link
                                            to="/dashboard"
                                            onClick={() => setIsUserMenuOpen(false)}
                                            className="block w-full text-left px-4 lg:px-5 xl:px-6 py-2 lg:py-2.5 xl:py-3 text-sm lg:text-sm xl:text-base text-white hover:bg-gradient-to-r hover:from-yellow-400/20 hover:to-orange-400/20 hover:text-yellow-300 transition-all duration-300 font-semibold tracking-wide"
                                        >
                                            Panel Admin
                                        </Link>
                                        <button
                                            onClick={handleLogout}
                                            className="w-full text-left px-4 lg:px-5 xl:px-6 py-2 lg:py-2.5 xl:py-3 text-sm lg:text-sm xl:text-base text-white hover:bg-gradient-to-r hover:from-red-500/20 hover:to-red-600/20 hover:text-red-300 transition-all duration-300 font-semibold tracking-wide"
                                        >
                                            Logout
                                        </button>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <Link
                                to="/auth/login"
                                className="bg-yellow-500 text-blue-900 hover:bg-yellow-400 px-3 sm:px-4 md:px-5 lg:px-4 xl:px-6 py-2 sm:py-2.5 md:py-3 lg:py-2.5 xl:py-3 rounded-md font-semibold transition-colors duration-300 shadow-lg text-sm sm:text-base md:text-lg lg:text-base xl:text-xl"
                            >
                                Log Masuk
                            </Link>
                        )}

                        {/* Mobile Menu Button */}
                        <button
                            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                            className="lg:hidden p-1 sm:p-2 md:p-2.5 rounded-md text-white hover:text-yellow-300 hover:bg-blue-700/50 transition-colors duration-300"
                        >
                            {isMobileMenuOpen ? (
                                <X className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                            ) : (
                                <Menu className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" />
                            )}
                        </button>
                    </div>
                </nav>

                {/* Mobile Navigation Menu */}
                {isMobileMenuOpen && (
                    <div className="lg:hidden mt-2 pb-2 border-t border-yellow-400/30">
                        <div className="flex flex-col space-y-1 pt-2">
                            {/* Home sections */}
                            {homeSections.map((section) => (
                                <button
                                    key={section.href}
                                    onClick={() => handleNavigation(section.href)}
                                    className="text-left text-white hover:text-yellow-300 font-semibold text-xs sm:text-sm tracking-wide transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-600/50 hover:to-blue-700/50"
                                >
                                    {section.name}
                                </button>
                            ))}

                            {/* Divider */}
                            <div className="border-t border-yellow-400/30 my-1"></div>

                            {/* Other pages */}
                            <div className="text-yellow-300 font-semibold px-3 py-1 text-xs tracking-wide">Halaman Lain:</div>
                            {otherPages.map((page) => (
                                <Link
                                    key={page.href}
                                    to={page.href}
                                    className="text-white hover:text-yellow-300 font-medium text-xs sm:text-sm tracking-wide transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-600/50 hover:to-blue-700/50 ml-4 border-l-2 border-yellow-400/30 hover:border-yellow-400"
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    {page.name}
                                </Link>
                            ))}

                            {/* Auth section for mobile */}
                            <div className="border-t border-yellow-400/30 my-1"></div>
                            {user ? (
                                <div className="px-3 py-2">
                                    <div className="text-yellow-300 font-semibold text-xs tracking-wide mb-2">
                                        {user.name} ({user.role})
                                    </div>
                                    <Link
                                        to="/dashboard"
                                        onClick={() => setIsMobileMenuOpen(false)}
                                        className="block w-full text-left text-white hover:text-yellow-300 font-semibold text-xs sm:text-sm tracking-wide transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gradient-to-r hover:from-yellow-400/20 hover:to-orange-400/20 mb-1"
                                    >
                                        Panel Admin
                                    </Link>
                                    <button
                                        onClick={handleLogout}
                                        className="w-full text-left text-white hover:text-red-300 font-semibold text-xs sm:text-sm tracking-wide transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gradient-to-r hover:from-red-600/50 hover:to-red-700/50"
                                    >
                                        Logout
                                    </button>
                                </div>
                            ) : (
                                <Link
                                    to="/auth/login"
                                    className="text-white hover:text-yellow-300 font-semibold text-xs sm:text-sm tracking-wide transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gradient-to-r hover:from-blue-600/50 hover:to-blue-700/50 sm:hidden"
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    Log Masuk
                                </Link>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}
