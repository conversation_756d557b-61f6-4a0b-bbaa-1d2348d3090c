import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useHomepageContent } from '@/hooks/usePageContent';
import OptimizedImage from '@/components/OptimizedImage';
import SportCard from '@/components/SportCard';

interface Sport {
    id: string;
    name: string;
    icon: string;
    description: string;
    participants: string;
    venue: string;
}

interface SportsCategoriesProps {
    settings?: Record<string, any>;
}

const defaultSports: Sport[] = [
    {
        id: 'football',
        name: '<PERSON><PERSON>',
        icon: '/images/bolasepakpsd.png',
        description: 'Pertandingan bola sepak antara zon dengan format 11 vs 11 pemain. Setiap zon akan bertanding dalam format liga untuk menentukan juara keseluruhan.',
        participants: '22 pemain per perlawanan',
        venue: 'Padang Bola Sepak Utama'
    },
    {
        id: 'netball',
        name: '<PERSON><PERSON>',
        icon: '/images/netballpsd.png',
        description: 'Sukan bola jaring yang dimainkan oleh dua pasukan dengan 7 pemain setiap satu. Pertandingan akan menggunakan peraturan standard netball antarabangsa.',
        participants: '7 pemain per pasukan',
        venue: 'Gelanggang Bola Jaring'
    },
    {
        id: 'relay',
        name: 'Larian 10 x 400m',
        icon: '/images/10x400mpsd.png',
        description: 'Larian bergilir-gilir dengan 10 orang pelari dari setiap zon. Setiap pelari akan berlari sejauh 400 meter sebelum menyerahkan tongkat kepada pelari seterusnya.',
        participants: '10 pelari per zon',
        venue: 'Trek Atletik 400m'
    },
    {
        id: 'badminton',
        name: 'Badminton',
        icon: '/images/badmintonpsd.png',
        description: 'Pertandingan badminton dalam kategori perseorangan lelaki, perseorangan wanita, dan beregu campuran. Format pertandingan menggunakan sistem kalah mati.',
        participants: '1-2 pemain per perlawanan',
        venue: 'Dewan Badminton Indoor'
    },
    {
        id: 'table-tennis',
        name: 'Ping Pong',
        icon: '/images/pinpongpsd.png',
        description: 'Pertandingan ping pong atau tenis meja dalam kategori perseorangan dan beregu. Setiap perlawanan akan dimainkan dalam format best of 5 set.',
        participants: '1-2 pemain per perlawanan',
        venue: 'Dewan Ping Pong'
    },
    {
        id: 'volleyball',
        name: 'Bola Tampar',
        icon: '/images/bolatamparpsd.png',
        description: 'Pertandingan bola tampar dengan 6 pemain setiap pasukan. Format pertandingan menggunakan sistem best of 3 set dengan setiap set hingga 25 mata.',
        participants: '6 pemain per pasukan',
        venue: 'Gelanggang Bola Tampar'
    },
    {
        id: 'traditional',
        name: 'Sukan Rakyat',
        icon: '/images/sukanrakyatpsd.png',
        description: 'Pelbagai permainan tradisional Malaysia termasuk tarik tali, lompat getah, baling selipar, dan congkak. Sukan ini menggalakkan semangat kerjasama dan warisan budaya.',
        participants: 'Berbeza mengikut permainan',
        venue: 'Kawasan Sukan Rakyat'
    }
];

export default function SportsCategories({
    settings
}: SportsCategoriesProps) {
    const { data: content = {}, isLoading: loading } = useHomepageContent();
    const [currentSlide, setCurrentSlide] = useState(0);

    // Use content from CMS or fallback to defaults
    const sportsContent = content.sports || {};
    const title = sportsContent.title || "KATEGORI SUKAN";
    const subtitle = sportsContent.subtitle || "7 kategori sukan yang akan dipertandingkan dalam Sukan Intra Kor Kesihatan DiRaja 2025";
    const baseBackgroundImage = sportsContent.background_image || "/images/default.jpg";
    // Use background image directly
    const backgroundImage = baseBackgroundImage;

    // Parse sports data from CMS or use defaults
    let sports = defaultSports;
    if (sportsContent.sports_data) {
        try {
            const parsedSports = JSON.parse(sportsContent.sports_data);
            if (Array.isArray(parsedSports) && parsedSports.length > 0) {
                sports = parsedSports;
            }
        } catch (error) {

            // Fallback to default sports if parsing fails
        }
    }

    // Auto-play functionality - moved before conditional return
    useEffect(() => {
        if (sports.length > 0) {
            const interval = setInterval(() => {
                setCurrentSlide((prev) => (prev + 1) % sports.length);
            }, 5000); // Change slide every 5 seconds

            return () => clearInterval(interval);
        }
    }, [sports.length]);

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % sports.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + sports.length) % sports.length);
    };

    const goToSlide = (index: number) => {
        setCurrentSlide(index);
    };

    return (
        <section
            id="kategori-sukan"
            className="relative min-h-screen bg-cover bg-center bg-no-repeat scroll-mt-16 sm:scroll-mt-20 md:scroll-mt-24 lg:scroll-mt-20 xl:scroll-mt-24"
            style={{
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${backgroundImage}')`
            }}
        >
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/60 to-red-900/40"></div>

            <div className="relative z-10 flex items-center justify-center min-h-screen pt-20 pb-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto w-full">
                    {/* Header Section */}
                    <div className="text-center mb-6 lg:mb-8 xl:mb-10">
                    <h2 className="text-2xl sm:text-3xl md:text-3xl lg:text-3xl xl:text-5xl 2xl:text-6xl font-black text-white mb-2 sm:mb-3 md:mb-4 lg:mb-2 xl:mb-3 drop-shadow-2xl">
                        {title}
                    </h2>
                    <div className="w-12 sm:w-16 md:w-20 lg:w-20 xl:w-28 h-1 lg:h-1 xl:h-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-3 sm:mb-4 md:mb-6 lg:mb-3 xl:mb-4 rounded-full"></div>
                    <p className="text-sm sm:text-base md:text-base lg:text-base xl:text-xl 2xl:text-2xl text-gray-200 max-w-4xl mx-auto font-semibold drop-shadow-lg px-2 sm:px-4">
                        {subtitle}
                    </p>
                </div>

                    {/* Carousel Container */}
                    <div className="relative flex items-center justify-center px-4 sm:px-6 lg:px-8">
                    <div className="w-full max-w-5xl md:max-w-6xl lg:max-w-7xl xl:max-w-[90rem] mx-auto">
                        {/* Main Carousel */}
                        <div className="relative overflow-hidden rounded-lg sm:rounded-xl lg:rounded-2xl xl:rounded-3xl bg-gradient-to-br from-black/70 to-black/50 backdrop-blur-xl border border-yellow-400/20 shadow-xl lg:shadow-2xl xl:shadow-3xl h-[500px] sm:h-[550px] md:h-[500px] lg:h-[400px] xl:h-[500px]">
                            <div
                                className="flex transition-transform duration-500 ease-in-out"
                                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                            >
                                {sports.map((sport, index) => (
                                    <div key={sport.id} className="w-full flex-shrink-0">
                                        {/* Mobile & SM: Full width with background logo */}
                                        <div className="md:hidden relative w-full h-[480px] sm:h-[530px] rounded-2xl overflow-hidden">
                                            {/* Background Logo - Complete & Clear */}
                                            <OptimizedImage
                                                src={sport.icon}
                                                alt={`${sport.name} icon`}
                                                className="absolute inset-0 w-full h-full object-contain opacity-60 sm:opacity-70"
                                                enableCache={true}
                                                lazy={true}
                                            />
                                            {/* Subtle overlay to maintain text readability */}
                                            <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-black/30"></div>
                                            {/* Additional glow effect around logo */}
                                            <OptimizedImage
                                                src={sport.icon}
                                                alt={`${sport.name} icon blur`}
                                                className="absolute inset-0 w-full h-full object-contain opacity-30 sm:opacity-40 blur-sm"
                                                enableCache={true}
                                                lazy={true}
                                            />

                                            {/* Content */}
                                            <div className="relative z-10 flex flex-col justify-center h-full p-8 sm:p-10 text-center space-y-6 sm:space-y-8">
                                                <div className="space-y-4 sm:space-y-5">
                                                    <h3 className="text-4xl sm:text-5xl font-black text-white drop-shadow-2xl leading-tight">
                                                        {sport.name}
                                                    </h3>
                                                    <div className="w-24 sm:w-28 h-2 sm:h-2.5 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto rounded-full"></div>
                                                </div>

                                                <p className="text-xl sm:text-2xl text-gray-100 leading-relaxed font-semibold max-w-2xl mx-auto drop-shadow-lg">
                                                    {sport.description}
                                                </p>

                                                <div className="space-y-4 sm:space-y-5">
                                                    <div className="flex items-center justify-center gap-4 sm:gap-5">
                                                        <div className="w-3 h-3 sm:w-4 sm:h-4 bg-yellow-400 rounded-full shadow-lg"></div>
                                                        <span className="text-lg sm:text-xl text-white font-bold drop-shadow-lg">
                                                            <span className="text-yellow-400 font-black">Peserta:</span> {sport.participants}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center justify-center gap-4 sm:gap-5">
                                                        <div className="w-3 h-3 sm:w-4 sm:h-4 bg-green-400 rounded-full shadow-lg"></div>
                                                        <span className="text-lg sm:text-xl text-white font-bold drop-shadow-lg">
                                                            <span className="text-green-400 font-black">Venue:</span> {sport.venue}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* MD+: Grid layout */}
                                        <div className="hidden md:grid grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-4 xl:gap-12 items-center p-6 lg:p-6 xl:p-12 2xl:p-16 h-[500px] lg:h-[400px] xl:h-[500px]">
                                            {/* Left Side - Sport Info (2 columns on md, 3 on lg+) */}
                                            <div className="col-span-2 lg:col-span-3 text-left space-y-3 lg:space-y-3 xl:space-y-6 pr-4 lg:pr-4 xl:pr-16">
                                                <div className="space-y-3 lg:space-y-3 xl:space-y-4">
                                                    <h3 className="text-2xl lg:text-3xl xl:text-5xl 2xl:text-5xl font-black text-white drop-shadow-2xl leading-tight">
                                                        {sport.name}
                                                    </h3>
                                                    <div className="w-20 lg:w-24 xl:w-32 h-1.5 lg:h-1.5 xl:h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
                                                </div>

                                                <p className="text-base lg:text-lg xl:text-xl 2xl:text-2xl text-gray-200 leading-relaxed font-medium max-w-3xl xl:max-w-4xl">
                                                    {sport.description}
                                                </p>

                                                <div className="space-y-2 lg:space-y-2 xl:space-y-4 pt-1 lg:pt-1 xl:pt-6">
                                                    <div className="flex items-center justify-start space-x-2 lg:space-x-2 xl:space-x-6">
                                                        <div className="w-2 h-2 lg:w-2 lg:h-2 xl:w-5 xl:h-5 bg-yellow-400 rounded-full flex-shrink-0"></div>
                                                        <span className="text-sm lg:text-sm xl:text-lg 2xl:text-2xl text-white font-semibold">
                                                            <span className="text-yellow-400 font-bold">Peserta:</span> {sport.participants}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center justify-start space-x-2 lg:space-x-2 xl:space-x-6">
                                                        <div className="w-2 h-2 lg:w-2 lg:h-2 xl:w-5 xl:h-5 bg-green-400 rounded-full flex-shrink-0"></div>
                                                        <span className="text-sm lg:text-sm xl:text-lg 2xl:text-2xl text-white font-semibold">
                                                            <span className="text-green-400 font-bold">Venue:</span> {sport.venue}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Right Side - Sport Icon (1 column on md, 2 on lg+) */}
                                            <div className="col-span-1 lg:col-span-2 flex items-center justify-center">
                                                <div className="relative group">
                                                    {/* Animated Background */}
                                                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-lg lg:blur-lg xl:blur-3xl group-hover:blur-3xl transition-all duration-500 animate-pulse"></div>

                                                    {/* Main Icon Container with Enhanced Design */}
                                                    <div className="relative">
                                                        <SportCard
                                                            sportName={sport.name}
                                                            sportIcon={sport.icon}
                                                            className="w-32 h-32 lg:w-36 lg:h-36 xl:w-72 xl:h-72 2xl:w-80 2xl:h-80"
                                                            showTextOverlay={true}
                                                            textPosition="bottom"
                                                            textStyle="shadow"
                                                            iconSize="xlarge"
                                                            enableCache={true}
                                                            lazy={true}
                                                        />
                                                    </div>

                                                    {/* Floating Elements */}
                                                    <div className="absolute -top-2 -right-2 lg:-top-3 lg:-right-3 xl:-top-4 xl:-right-4 w-4 h-4 lg:w-5 lg:h-5 xl:w-10 xl:h-10 bg-yellow-400 rounded-full animate-bounce opacity-80"></div>
                                                    <div className="absolute -bottom-2 -left-2 lg:-bottom-3 lg:-left-3 xl:-bottom-4 xl:-left-4 w-3 h-3 lg:w-4 lg:h-4 xl:w-8 xl:h-8 bg-orange-500 rounded-full animate-pulse opacity-80"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Navigation Arrows - Hidden on sm/md, outside content area on lg+ */}
                            <button
                                onClick={prevSlide}
                                className="hidden lg:flex absolute left-2 lg:-left-12 xl:-left-20 top-1/2 -translate-y-1/2 w-10 h-10 lg:w-12 lg:h-12 xl:w-20 xl:h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full items-center justify-center text-white hover:scale-110 hover:shadow-2xl transition-all duration-300 shadow-lg lg:shadow-xl xl:shadow-2xl z-20 border-2 border-white/20"
                            >
                                <ChevronLeft className="w-4 h-4 lg:w-5 lg:h-5 xl:w-10 xl:h-10" />
                            </button>

                            <button
                                onClick={nextSlide}
                                className="hidden lg:flex absolute right-2 lg:-right-12 xl:-right-20 top-1/2 -translate-y-1/2 w-10 h-10 lg:w-12 lg:h-12 xl:w-20 xl:h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full items-center justify-center text-white hover:scale-110 hover:shadow-2xl transition-all duration-300 shadow-lg lg:shadow-xl xl:shadow-2xl z-20 border-2 border-white/20"
                            >
                                <ChevronRight className="w-4 h-4 lg:w-5 lg:h-5 xl:w-10 xl:h-10" />
                            </button>
                        </div>

                        {/* Slide Indicators */}
                        <div className="flex justify-center space-x-1.5 sm:space-x-2 md:space-x-2 lg:space-x-2.5 xl:space-x-5 mt-6 lg:mt-8 xl:mt-10">
                            {sports.map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => goToSlide(index)}
                                    className={`w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-3 md:h-3 lg:w-3.5 lg:h-3.5 xl:w-6 xl:h-6 rounded-full transition-all duration-300 border sm:border md:border lg:border xl:border-2 ${
                                        index === currentSlide
                                            ? 'bg-gradient-to-r from-yellow-400 to-orange-500 scale-125 border-white/50 shadow-lg'
                                            : 'bg-white/20 hover:bg-white/40 border-white/30 hover:scale-110'
                                    }`}
                                />
                            ))}
                        </div>


                    </div>
                </div>
            </div>
            </div>
        </section>
    );
}
