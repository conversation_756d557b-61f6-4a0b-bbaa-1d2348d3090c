import { Trophy, Users, Calendar, Target } from 'lucide-react';
import { useHomepageContent } from '@/hooks/usePageContent';

interface Stat {
    id: string;
    label: string;
    value: string;
    icon: string;
    description?: string;
}

interface StatsSectionProps {
    settings?: Record<string, any>;
}

const defaultStats: Stat[] = [
    {
        id: 'participants',
        label: 'Peserta Berdaftar',
        value: '500+',
        icon: 'users',
        description: 'Pelajar aktif'
    },
    {
        id: 'sports',
        label: 'Kategori Sukan',
        value: '7',
        icon: 'trophy',
        description: '<PERSON><PERSON> pertanding<PERSON>'
    },
    {
        id: 'matches',
        label: 'Pertandingan',
        value: '50+',
        icon: 'calendar',
        description: 'Perlawanan dijadualkan'
    }
];

const getIcon = (iconName: string) => {
    const icons: { [key: string]: any } = {
        users: Users,
        trophy: Trophy,
        calendar: Calendar,
        target: Target
    };
    return icons[iconName] || Trophy;
};

export default function StatsSection({
    settings
}: StatsSectionProps) {
    const { data: content = {}, isLoading: loading } = useHomepageContent();

    // Use content from CMS or fallback to defaults
    const statsContent = content.stats || {};
    const title = statsContent.title || "Portal Sukan Dalam Angka";
    const subtitle = statsContent.subtitle || "Statistik terkini Portal Sukan Intra KKD";
    const baseBackgroundImage = statsContent.background_image || "/images/default.jpg";
    // Use background image directly
    const backgroundImage = baseBackgroundImage;

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    // Get statistics data from CMS content
    const getStatValue = (key: string, fallback: string) => {
        // First try to get from CMS content
        const cmsValue = content.stats?.[key];
        if (cmsValue && cmsValue !== '0') {
            return cmsValue;
        }
        // Fallback to settings or default
        return settings?.[`total_${key.replace('_count', 's')}`] || fallback;
    };

    const stats: Stat[] = [
        {
            id: 'participants',
            label: 'Peserta Berdaftar',
            value: getStatValue('participants_count', '150+'),
            icon: 'users',
            description: 'Anggota aktif'
        },
        {
            id: 'sports',
            label: 'Kategori Sukan',
            value: getStatValue('sports_count', '7'),
            icon: 'trophy',
            description: 'Jenis sukan'
        },
        {
            id: 'zones',
            label: 'Zon Penyertaan',
            value: getStatValue('zones_count', '4'),
            icon: 'target',
            description: 'Zon aktif'
        },
        {
            id: 'events',
            label: 'Acara Sukan',
            value: getStatValue('events_count', '25+'),
            icon: 'calendar',
            description: 'Acara dijadualkan'
        }
    ];
    return (
        <section
                id="portal-dalam-angka"
                className="relative min-h-screen bg-cover bg-center bg-no-repeat scroll-mt-16 sm:scroll-mt-20 md:scroll-mt-24 lg:scroll-mt-20 xl:scroll-mt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${backgroundImage}')`
                }}
            >
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 to-blue-900/80"></div>

            <div className="relative z-10 flex items-center justify-center min-h-screen pt-20 pb-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto w-full">
                    <div className="text-center mb-8 lg:mb-10 xl:mb-12">
                        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-3xl xl:text-5xl 2xl:text-6xl font-black text-white mb-3 sm:mb-4 md:mb-5 lg:mb-4 xl:mb-6 drop-shadow-2xl">{title}</h2>
                        <div className="w-16 sm:w-20 md:w-24 lg:w-20 xl:w-28 h-1 lg:h-1 xl:h-1.5 bg-gradient-to-r from-green-400 to-blue-500 mx-auto mb-3 sm:mb-4 md:mb-5 lg:mb-4 xl:mb-6 rounded-full"></div>
                        <p className="text-sm sm:text-base md:text-base lg:text-sm xl:text-xl 2xl:text-2xl text-white/90 max-w-3xl mx-auto font-semibold drop-shadow-lg">
                        {subtitle}
                    </p>
                </div>

                    <div className="flex flex-col lg:flex-row items-center justify-center gap-6 lg:gap-6 xl:gap-8 w-full max-w-6xl mx-auto">
                    {stats.map((stat) => {
                        const IconComponent = getIcon(stat.icon);

                        return (
                            <div
                                key={stat.id}
                                className="text-center bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border border-green-400/30 rounded-xl sm:rounded-2xl lg:rounded-2xl xl:rounded-3xl p-4 sm:p-6 lg:p-6 xl:p-8 hover:scale-105 hover:border-green-400/60 transition-all duration-300 shadow-2xl group relative w-full max-w-sm lg:max-w-xs xl:max-w-sm"
                            >
                                {/* Background Glow */}
                                <div className="absolute inset-0 bg-gradient-to-br from-green-400/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl sm:rounded-2xl lg:rounded-2xl xl:rounded-3xl"></div>

                                {/* Floating Decorative Elements for XL+ */}
                                <div className="hidden 2xl:block absolute -top-6 -right-6 w-12 h-12 bg-yellow-400/20 rounded-full animate-pulse"></div>
                                <div className="hidden 2xl:block absolute -bottom-6 -left-6 w-10 h-10 bg-blue-400/20 rounded-full animate-bounce"></div>

                                <div className="relative">
                                    <div className="w-16 h-16 sm:w-18 sm:h-18 lg:w-16 lg:h-16 xl:w-20 xl:h-20 2xl:w-24 2xl:h-24 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 lg:mb-3 xl:mb-4 2xl:mb-6 group-hover:scale-110 transition-transform shadow-xl">
                                        <IconComponent className="h-8 w-8 sm:h-9 sm:w-9 lg:h-8 lg:w-8 xl:h-10 xl:w-10 2xl:h-12 2xl:w-12 text-white" />
                                    </div>
                                    <div className="text-2xl sm:text-3xl lg:text-2xl xl:text-4xl 2xl:text-5xl font-black text-white mb-2 sm:mb-2 lg:mb-1 xl:mb-3 2xl:mb-4 drop-shadow-lg">
                                        {stat.value}
                                    </div>
                                    <h3 className="text-sm sm:text-base lg:text-sm xl:text-lg 2xl:text-xl font-bold text-white mb-1 sm:mb-2 lg:mb-1 xl:mb-2 2xl:mb-3 group-hover:text-green-400 transition-colors">
                                        {stat.label}
                                    </h3>
                                    {stat.description && (
                                        <p className="text-xs sm:text-sm lg:text-xs xl:text-sm 2xl:text-base text-white/80 font-medium">
                                            {stat.description}
                                        </p>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                    </div>
                </div>
            </div>
        </section>
    );
}
