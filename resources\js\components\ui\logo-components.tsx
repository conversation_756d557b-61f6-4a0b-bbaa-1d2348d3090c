import React from 'react';
import { getSportLogo, getZoneLogo } from '@/utils/logo-mapping';

// Sport logo component props interface
export interface SportLogoProps {
    sportName: string | undefined | null;
    logoUrl?: string | undefined | null;
    className?: string;
    alt?: string;
}

// Zone logo component props interface
export interface ZoneLogoProps {
    zoneCode: string | undefined | null;
    className?: string;
    alt?: string;
}

// Get sport logo with error handling component
export const SportLogo: React.FC<SportLogoProps> = ({
    sportName,
    logoUrl,
    className = "w-6 h-6 object-contain",
    alt
}) => {
    // Handle undefined or null sportName
    const safeSportName = sportName || 'default';

    // Use logoUrl from database if available, otherwise use mapping
    const logoSrc = logoUrl || getSportLogo(sportName);

    return (
        <img
            src={logoSrc}
            alt={alt || safeSportName}
            className={className}
            onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
                // Fallback to mapping if database logo fails
                if (e.currentTarget.src !== getSportLogo(sportName)) {
                    e.currentTarget.src = getSportLogo(sportName);
                } else {
                    e.currentTarget.src = '/images/default.jpg';
                }
            }}
        />
    );
};

// Get zone logo with error handling component
export const ZoneLogo: React.FC<ZoneLogoProps> = ({
    zoneCode,
    className = "w-6 h-6 object-contain",
    alt
}) => {
    // Handle undefined or null zoneCode
    const safeZoneCode = zoneCode || 'default';

    return (
        <img
            src={getZoneLogo(zoneCode)}
            alt={alt || `Zon ${safeZoneCode}`}
            className={className}
            onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
                e.currentTarget.src = '/images/zones/zona-logo.png';
            }}
        />
    );
};
