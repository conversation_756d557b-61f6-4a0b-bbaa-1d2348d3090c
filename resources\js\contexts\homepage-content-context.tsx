import { createContext, useContext, ReactNode } from 'react';
import { useHomepageContent as useOptimizedHomepageContent } from '@/hooks/usePageContent';

interface HomepageContent {
  hero?: {
    title?: string;
    subtitle?: string;
    description?: string;
    countdown_text?: string;
    background_image?: string;
  };
  sports?: {
    title?: string;
    subtitle?: string;
    background_image?: string;
    sports_data?: string; // JSON string of sports array
  };
  stats?: {
    title?: string;
    subtitle?: string;
    background_image?: string;
    participants_count?: string;
    sports_count?: string;
    zones_count?: string;
    events_count?: string;
  };
  cta?: {
    title?: string;
    subtitle?: string;
    description?: string;
    background_image?: string;
  };
}

interface HomepageContentContextType {
  content: HomepageContent;
  loading: boolean;
  error: string | null;
}

const HomepageContentContext = createContext<HomepageContentContextType | undefined>(undefined);

export function HomepageContentProvider({ children }: { children: ReactNode }) {
  const { data: content = {}, isLoading: loading, error } = useOptimizedHomepageContent();

  return (
    <HomepageContentContext.Provider value={{
      content,
      loading,
      error: error ? 'Failed to fetch homepage content' : null
    }}>
      {children}
    </HomepageContentContext.Provider>
  );
}

export function useHomepageContent() {
  const context = useContext(HomepageContentContext);
  if (context === undefined) {
    throw new Error('useHomepageContent must be used within a HomepageContentProvider');
  }
  return context;
}
