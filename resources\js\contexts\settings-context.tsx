import { createContext, useContext, ReactNode } from 'react';
import { useSettings as useOptimizedSettings } from '@/hooks/useSettings';

interface SettingsContextType {
  settings: Record<string, any>;
  user: any;
  loading: boolean;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function SettingsProvider({ children }: { children: ReactNode }) {
  const { data: settings = {}, isLoading: loading } = useOptimizedSettings();

  return (
    <SettingsContext.Provider value={{ settings, user: null, loading }}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}
