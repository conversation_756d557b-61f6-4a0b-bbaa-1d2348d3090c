import { useState, useMemo } from 'react';
import { Match, MatchFilters } from '@/types/matches';

export function useMatchFilters(matches: Match[]) {
    const [filters, setFilters] = useState<MatchFilters>({
        searchTerm: '',
        statusFilter: 'all',
        sportFilter: 'all',
        dateFilter: 'all',
        venueFilter: 'all',
        zoneFilter: 'all',
    });

    const updateFilters = (newFilters: Partial<MatchFilters>) => {
        setFilters(prev => ({ ...prev, ...newFilters }));
    };

    // Filter matches based on current filters
    const filteredMatches = useMemo(() => {
        let filtered = [...matches];

        // Search filter
        if (filters.searchTerm) {
            const searchLower = filters.searchTerm.toLowerCase();
            filtered = filtered.filter((match: Match) =>
                match.title?.toLowerCase().includes(searchLower) ||
                match.sport?.name?.toLowerCase().includes(searchLower) ||
                match.zone_a?.toLowerCase().includes(searchLower) ||
                match.zone_b?.toLowerCase().includes(searchLower) ||
                match.venue?.toLowerCase().includes(searchLower)
            );
        }

        // Status filter
        if (filters.statusFilter && filters.statusFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.status === filters.statusFilter);
        }

        // Sport filter
        if (filters.sportFilter && filters.sportFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.sport?.name === filters.sportFilter);
        }

        // Date filter
        if (filters.dateFilter && filters.dateFilter !== 'all') {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            filtered = filtered.filter((match: Match) => {
                if (!match.match_date) return false;
                const matchDate = new Date(match.match_date);
                matchDate.setHours(0, 0, 0, 0);

                switch (filters.dateFilter) {
                    case 'today':
                        return matchDate.getTime() === today.getTime();
                    case 'tomorrow':
                        const tomorrow = new Date(today);
                        tomorrow.setDate(today.getDate() + 1);
                        return matchDate.getTime() === tomorrow.getTime();
                    case 'this_week':
                        const weekStart = new Date(today);
                        weekStart.setDate(today.getDate() - today.getDay());
                        const weekEnd = new Date(weekStart);
                        weekEnd.setDate(weekStart.getDate() + 6);
                        return matchDate >= weekStart && matchDate <= weekEnd;
                    case 'next_week':
                        const nextWeekStart = new Date(today);
                        nextWeekStart.setDate(today.getDate() - today.getDay() + 7);
                        const nextWeekEnd = new Date(nextWeekStart);
                        nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
                        return matchDate >= nextWeekStart && matchDate <= nextWeekEnd;
                    default:
                        return true;
                }
            });
        }

        // Venue filter
        if (filters.venueFilter && filters.venueFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.venue === filters.venueFilter);
        }

        // Zone filter
        if (filters.zoneFilter && filters.zoneFilter !== 'all') {
            filtered = filtered.filter((match: Match) =>
                match.zone_a === filters.zoneFilter ||
                match.zone_b === filters.zoneFilter ||
                (match.participating_zones &&
                 (typeof match.participating_zones === 'string'
                  ? JSON.parse(match.participating_zones).includes(filters.zoneFilter)
                  : match.participating_zones.includes(filters.zoneFilter)))
            );
        }

        return filtered;
    }, [matches, filters]);

    // Get unique options for filters
    const uniqueSports = useMemo(() => 
        [...new Set(matches.map((m: Match) => m.sport?.name))].filter(Boolean),
        [matches]
    );

    const uniqueVenues = useMemo(() => 
        [...new Set(matches.map((m: Match) => m.venue))].filter(Boolean),
        [matches]
    );

    const uniqueZones = useMemo(() => 
        [...new Set([
            ...matches.map((m: Match) => m.zone_a),
            ...matches.map((m: Match) => m.zone_b),
            ...matches.flatMap((m: Match) => {
                if (m.participating_zones) {
                    try {
                        return typeof m.participating_zones === 'string'
                            ? JSON.parse(m.participating_zones)
                            : m.participating_zones;
                    } catch {
                        return [];
                    }
                }
                return [];
            })
        ])].filter(Boolean),
        [matches]
    );

    return {
        filters,
        updateFilters,
        filteredMatches,
        uniqueSports,
        uniqueVenues,
        uniqueZones,
    };
}
