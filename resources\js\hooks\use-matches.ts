import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import { Match } from '@/types/matches';

export function useMatches() {
    const queryClient = useQueryClient();

    // Fetch matches
    const { data: matches = [], isLoading: isLoadingMatches, error } = useQuery({
        queryKey: ['matches'],
        queryFn: async () => {
            // Request all matches by setting a high per_page value
            const response = await api.get('/admin/sport-matches', {
                params: {
                    per_page: 1000 // Get all matches without pagination
                }
            });

            // Ensure we have valid data structure
            if (response.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else if (response.data && Array.isArray(response.data)) {
                return response.data;
            } else {
                return [];
            }
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Add match mutation
    const addMatchMutation = useMutation({
        mutationFn: async (data: any) => {
            const payload = {
                sport_id: parseInt(data.sport_id),
                title: data.title,
                event_type: data.event_type,
                zone_a: data.zone_a,
                zone_b: data.zone_b,
                participating_zones: data.participating_zones,
                match_date: data.match_date,
                match_time: data.match_time,
                venue: data.venue,
                status: data.status || 'scheduled',
            };

            const response = await api.post('/admin/sport-matches', payload);
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya ditambah');
        },
        onError: (error: any) => {
            console.error('Add match error:', error);
            const errorMessage = error.response?.data?.message || 'Gagal menambah perlawanan';
            toast.error(errorMessage);
        },
    });

    // Edit match mutation
    const editMatchMutation = useMutation({
        mutationFn: async ({ id, data }: { id: number; data: any }) => {
            const payload = {
                sport_id: parseInt(data.sport_id),
                title: data.title,
                event_type: data.event_type,
                zone_a: data.zone_a,
                zone_b: data.zone_b,
                participating_zones: data.participating_zones,
                match_date: data.match_date,
                match_time: data.match_time,
                venue: data.venue,
                status: data.status || 'scheduled',
            };

            const response = await api.put(`/admin/sport-matches/${id}`, payload);
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya dikemaskini');
        },
        onError: (error: any) => {
            console.error('Edit match error:', error);
            const errorMessage = error.response?.data?.message || 'Gagal mengemaskini perlawanan';
            toast.error(errorMessage);
        },
    });

    // Delete match mutation
    const deleteMatchMutation = useMutation({
        mutationFn: async (id: number) => {
            const response = await api.delete(`/admin/sport-matches/${id}`);
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya dipadam');
        },
        onError: (error: any) => {
            console.error('Delete match error:', error);
            const errorMessage = error.response?.data?.message || 'Gagal memadam perlawanan';
            toast.error(errorMessage);
        },
    });

    return {
        matches,
        isLoadingMatches,
        error,
        addMatchMutation,
        editMatchMutation,
        deleteMatchMutation,
    };
}
