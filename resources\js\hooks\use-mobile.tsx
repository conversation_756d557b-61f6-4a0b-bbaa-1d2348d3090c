import { useEffect, useState } from 'react';

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
    const [isMobile, setIsMobile] = useState<boolean>(false);

    useEffect(() => {
        // Check if window is available (client-side)
        if (typeof window === 'undefined') return;

        const checkIsMobile = () => {
            return window.innerWidth < MOBILE_BREAKPOINT;
        };

        // Set initial value
        setIsMobile(checkIsMobile());

        // Create media query listener
        const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);

        const onChange = () => {
            setIsMobile(checkIsMobile());
        };

        // Use the newer API if available, fallback to older one
        if (mql.addEventListener) {
            mql.addEventListener('change', onChange);
        } else {
            // Fallback for older browsers
            mql.addListener(onChange);
        }

        return () => {
            if (mql.removeEventListener) {
                mql.removeEventListener('change', onChange);
            } else {
                // Fallback for older browsers
                mql.removeListener(onChange);
            }
        };
    }, []);

    return isMobile;
}
