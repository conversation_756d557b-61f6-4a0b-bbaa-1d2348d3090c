import { useQuery } from '@tanstack/react-query';
import publicApi from '@/lib/public-api';

interface PageContent {
  [key: string]: any;
}

/**
 * Optimized hook for fetching page content using TanStack Query
 */
export function usePageContent(page: string) {
  return useQuery<PageContent>({
    queryKey: ['pageContent', page],
    queryFn: async () => {
      const response = await publicApi.get(`/public/content/${page}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes - content can change more frequently
    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection
    refetchOnWindowFocus: false,
    retry: 2,
    enabled: !!page, // Only fetch if page is provided
  });
}

/**
 * Hook for fetching homepage content specifically
 */
export function useHomepageContent() {
  return useQuery<PageContent>({
    queryKey: ['homepageContent'],
    queryFn: async () => {
      const response = await publicApi.get('/public/homepage-content');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2,
  });
}

/**
 * Hook for getting specific content from a page section
 */
export function useContentValue(page: string, section: string, key: string, fallback: any = '') {
  const { data: content, isLoading, error } = usePageContent(page);
  
  const value = content?.[section]?.[key] ?? fallback;
  
  return {
    value,
    isLoading,
    error,
    content
  };
}
