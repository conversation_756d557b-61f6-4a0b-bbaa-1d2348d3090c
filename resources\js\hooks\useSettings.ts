import { useQuery } from '@tanstack/react-query';
import publicApi from '@/lib/public-api';

interface Settings {
  [key: string]: any;
}

/**
 * Optimized hook for fetching site settings using TanStack Query
 * Provides caching, background refetch, and error handling
 */
export function useSettings() {
  return useQuery<Settings>({
    queryKey: ['settings'],
    queryFn: async () => {
      const response = await publicApi.get('/public/settings');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - settings don't change often
    gcTime: 30 * 60 * 1000, // 30 minutes garbage collection
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook for getting a specific setting value with fallback
 */
export function useSetting(key: string, fallback: any = null) {
  const { data: settings, isLoading, error } = useSettings();
  
  const value = settings?.[key] ?? fallback;
  
  return {
    value,
    isLoading,
    error,
    settings
  };
}
