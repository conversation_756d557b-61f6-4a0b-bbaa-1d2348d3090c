import { Link, Outlet } from 'react-router-dom';
import { ReactNode } from 'react';
import { SettingsProvider, useSettings } from '@/contexts/settings-context';
import DynamicFavicon from '@/components/DynamicFavicon';

interface AuthLayoutProps {
    children?: ReactNode;
    title?: string;
    description?: string;
}

function AuthLayoutContent({ children, title, description }: AuthLayoutProps = {}) {
    const { settings } = useSettings();

    return (
        <>
            {/* Dynamic Favicon */}
            <DynamicFavicon logoUrl={settings?.logo_url} />

            <div
                className="relative min-h-screen bg-cover bg-center bg-no-repeat flex items-center justify-center px-3 sm:px-6 lg:px-8"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`
                }}
            >
            {/* Header */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 shadow-xl border-b-2 sm:border-b-4 border-yellow-400 backdrop-blur-sm">
                <div className="container mx-auto px-3 sm:px-6 lg:px-8 py-1 sm:py-2 md:py-3 lg:py-3 xl:py-4">
                    <nav className="flex items-center justify-between">
                        {/* Logo and Site Name */}
                        <Link to="/" className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 lg:space-x-3 xl:space-x-4">
                            <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 lg:space-x-3 xl:space-x-4">
                                <img
                                    src="/images/logo.png"
                                    alt="Logo Sukan"
                                    className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 lg:h-12 lg:w-12 xl:h-14 xl:w-14 2xl:h-16 2xl:w-16 object-contain"
                                />
                                <div className="text-white">
                                    <div className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl font-black leading-tight">
                                        <span className="text-red-400">SUKAN </span>
                                        <span className="text-blue-300">INTRA</span>
                                    </div>
                                    <div className="text-xs sm:text-xs md:text-sm lg:text-sm xl:text-base 2xl:text-lg font-bold text-yellow-300 leading-tight">
                                        KOR KESIHATAN DIRAJA 2025
                                    </div>
                                </div>
                            </div>
                        </Link>

                        {/* Navigation Links - Desktop */}
                        <div className="hidden md:flex items-center space-x-3 lg:space-x-4 xl:space-x-6 2xl:space-x-8">
                            <Link
                                to="/"
                                className="text-white hover:text-yellow-300 transition-colors duration-200 text-sm lg:text-base xl:text-lg 2xl:text-xl font-semibold"
                            >
                                Laman Utama
                            </Link>
                        </div>
                    </nav>
                </div>
            </header>

            {/* Main Content */}
            <div className="relative z-10 w-full max-w-xs sm:max-w-sm lg:max-w-sm xl:max-w-md mx-auto pt-20 sm:pt-24 lg:pt-32 xl:pt-32">
                {/* Auth Card */}
                <div className="bg-white/10 backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-xl xl:rounded-2xl p-4 sm:p-6 lg:p-6 xl:p-8 border border-white/20 shadow-2xl">
                    {/* Header */}
                    <div className="text-center mb-4 sm:mb-6 lg:mb-6 xl:mb-8">
                        <div className="inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-14 lg:h-14 xl:w-16 xl:h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mb-3 sm:mb-4 lg:mb-3 xl:mb-4">
                            <img
                                src="/images/logo.png"
                                alt="Logo"
                                className="w-6 h-6 sm:w-8 sm:h-8 lg:w-8 lg:h-8 xl:w-10 xl:h-10 object-contain"
                            />
                        </div>
                        <h1 className="text-lg sm:text-xl lg:text-xl xl:text-2xl font-bold text-white mb-1 sm:mb-2 lg:mb-1 xl:mb-2">{title}</h1>
                        <p className="text-white/70 text-xs sm:text-sm lg:text-xs xl:text-sm">{description}</p>
                    </div>

                    {/* Form Content */}
                    {children || <Outlet />}
                </div>

                {/* Footer */}
                <div className="text-center mt-4 sm:mt-6 lg:mt-4 xl:mt-6">
                    <p className="text-white/60 text-xs sm:text-sm lg:text-xs xl:text-sm">
                        © 2025 Portal Sukan Intra KKD. Semua hak terpelihara.
                    </p>
                </div>
            </div>
        </div>
        </>
    );
}

export default function AuthLayout(props: AuthLayoutProps) {
    return (
        <SettingsProvider>
            <AuthLayoutContent {...props} />
        </SettingsProvider>
    );
}
