import React, { useState } from 'react';
import { Outlet, Link, useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Home,
  Users,
  Trophy,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  UserCircle,
  Shield,
  ChevronDown,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Globe,
  Camera,
  Newspaper
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import DynamicFavicon from '@/components/DynamicFavicon';
import { useAuth } from '@/contexts/auth-context';

// API functions
const fetchUser = async () => {
  const token = localStorage.getItem('auth_token');
  if (!token) {
    throw new Error('No auth token found');
  }

  const response = await fetch('/api/user', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
    },
  });

  if (response.status === 401) {
    // Token expired or invalid
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    throw new Error('Token expired');
  }

  if (!response.ok) {
    throw new Error(`Failed to fetch user: ${response.status}`);
  }

  return response.json();
};

const fetchSettings = async () => {
  const token = localStorage.getItem('auth_token');
  const response = await fetch('/api/settings', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
    },
  });
  if (!response.ok) {

    return { site_name: 'Portal Sukan' }; // Default fallback
  }
  return response.json();
};

const fetchZone = async () => {
  const token = localStorage.getItem('auth_token');
  const userData = localStorage.getItem('user_data');

  if (!userData) {
    throw new Error('User data not found');
  }

  const user = JSON.parse(userData);
  if (!user.zone) {
    throw new Error('User zone not found');
  }

  const response = await fetch(`/api/admin/zones/${user.zone}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
    },
  });
  if (!response.ok) throw new Error('Failed to fetch zone');
  return response.json();
};

function DashboardLayoutContent() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { logout } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  // Fetch user data
  const { data: user, error: userError } = useQuery({
    queryKey: ['user'],
    queryFn: fetchUser,
    retry: (failureCount, error) => {
      // Don't retry if token is expired/invalid
      if (error.message === 'Token expired') {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Fetch settings
  const { data: settings } = useQuery({
    queryKey: ['settings'],
    queryFn: fetchSettings,
  });

  // Fetch zone data for zone users
  const { data: zone } = useQuery({
    queryKey: ['zone'],
    queryFn: fetchZone,
    enabled: user?.role === 'zone',
  });



  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleLogoutConfirm = async () => {
    try {
      await logout();
      queryClient.clear();
      navigate('/auth/login');
    } catch (error) {

      // Force logout even if API fails
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      queryClient.clear();
      navigate('/auth/login');
    } finally {
      setShowLogoutModal(false);
    }
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  // Handle authentication errors
  if (userError && userError.message === 'Token expired') {
    navigate('/auth/login');
    return null;
  }

  // Navigation items based on user role
  const getNavigationItems = () => {
    const baseItems = [
      {
        title: "Dashboard",
        url: "/dashboard",
        icon: Home,
      },
    ];

    if (user?.role === 'superadmin') {
      baseItems.push(
        {
          title: "Pengurusan Peserta",
          url: "/dashboard/admin/players",
          icon: Users,
        },
        {
          title: "Pengurusan Perlawanan",
          url: "/dashboard/admin/matches",
          icon: Trophy,
        },

        {
          title: "CMS - Page Content",
          url: "/dashboard/admin/cms/page-content",
          icon: FileText,
        },
        {
          title: "Laporan & Statistik",
          url: "/dashboard/admin/reports",
          icon: BarChart3,
        },
        {
          title: "Tetapan Sistem",
          url: "/dashboard/admin/settings",
          icon: Settings,
        }
      );
    } else if (user?.role === 'admin') {
      baseItems.push(
        {
          title: "Pengurusan Peserta",
          url: "/dashboard/admin/players",
          icon: Users,
        },
        {
          title: "Pengurusan Perlawanan",
          url: "/dashboard/admin/matches",
          icon: Trophy,
        },

        {
          title: "Laporan & Statistik",
          url: "/dashboard/admin/reports",
          icon: BarChart3,
        }
      );
    } else if (user?.role === 'zone') {
      baseItems.push(
        {
          title: "Peserta",
          url: "/dashboard/admin/players",
          icon: Users,
        },
        {
          title: "Zon Profile",
          url: "/dashboard/admin/zone-profile",
          icon: Shield,
        }
      );
    }

    // Add public pages for all roles
    baseItems.push(
      {
        title: "Portal Utama",
        url: "/",
        icon: Globe,
      },
      {
        title: "Galeri",
        url: "/gallery",
        icon: Camera,
      },
      {
        title: "Berita",
        url: "/news",
        icon: Newspaper,
      }
    );

    return baseItems;
  };

  return (
    <>
      {/* Dynamic Favicon */}
      <DynamicFavicon logoUrl={settings?.logo_url} />

      <div className="flex h-screen w-full overflow-hidden">
        {/* Mobile Backdrop */}
        {mobileMenuOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/50 md:hidden"
            onClick={() => setMobileMenuOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={`
          ${sidebarCollapsed ? 'w-16' : 'w-64'}
          ${mobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          fixed md:relative z-50 md:z-auto
          h-full border-r bg-background transition-all duration-300 ease-in-out
          flex-shrink-0
        `}>
          <div className="flex h-full flex-col">
            {/* Header */}
            <div className="flex h-16 items-center justify-between border-b px-4">
              <Link to="/dashboard" className={`flex items-center gap-2 ${sidebarCollapsed ? 'justify-center w-full' : ''}`}>
                {user?.role === 'zone' && zone ? (
                  <>
                    {zone?.logo_url ? (
                      <img
                        src={zone.logo_url}
                        alt={`Logo ${zone.name}`}
                        className="h-8 w-8 rounded-md object-cover flex-shrink-0"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : null}
                    {(!zone?.logo_url || !zone.logo_url) && (
                      <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground flex-shrink-0">
                        <Shield className="h-4 w-4" />
                      </div>
                    )}
                    {!sidebarCollapsed && (
                      <div className="flex flex-col">
                        <span className="text-sm font-semibold">{zone?.name || 'Zone'}</span>
                        <span className="text-xs text-muted-foreground">Admin Panel</span>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    {settings?.logo_url ? (
                      <img
                        src={settings.logo_url}
                        alt="Portal Sukan"
                        className="h-8 w-8 rounded-md object-cover flex-shrink-0"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : null}
                    {(!settings?.logo_url || !settings.logo_url) && (
                      <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground flex-shrink-0">
                        <Trophy className="h-4 w-4" />
                      </div>
                    )}
                    {!sidebarCollapsed && (
                      <div className="flex flex-col">
                        <span className="text-sm font-semibold">Portal Sukan</span>
                        <span className="text-xs text-muted-foreground">Admin Panel</span>
                      </div>
                    )}
                  </>
                )}
              </Link>

              {/* Collapse Button - Desktop only */}
              <Button
                variant="ghost"
                size="sm"
                className="hidden md:flex h-8 w-8 p-0"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              >
                {sidebarCollapsed ? (
                  <ChevronRight className="h-4 w-4" />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
              </Button>

              {/* Close Button - Mobile only */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden h-8 w-8 p-0"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-auto py-2">
              <div className="space-y-1 px-2">
                {!sidebarCollapsed && (
                  <div className="px-2 py-1">
                    <h2 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                      Platform
                    </h2>
                  </div>
                )}
                {getNavigationItems().map((item) => (
                  <Link
                    key={item.url}
                    to={item.url}
                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors ${
                      sidebarCollapsed ? 'justify-center' : ''
                    }`}
                    title={sidebarCollapsed ? item.title : undefined}
                  >
                    <item.icon className="h-4 w-4 flex-shrink-0" />
                    {!sidebarCollapsed && item.title}
                  </Link>
                ))}
              </div>
            </div>

            {/* Footer */}
            <div className="border-t p-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className={`w-full gap-2 px-2 ${sidebarCollapsed ? 'justify-center' : 'justify-start'}`}>
                    <Avatar className="h-6 w-6 flex-shrink-0">
                      <AvatarImage src="" />
                      <AvatarFallback className="text-xs">
                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    {!sidebarCollapsed && (
                      <>
                        <div className="flex flex-1 flex-col items-start text-left">
                          <span className="text-sm font-medium">{user?.name || 'User'}</span>
                          <span className="text-xs text-muted-foreground capitalize">
                            {user?.role === 'superadmin' ? 'Super Administrator' :
                             user?.role === 'admin' ? 'Administrator' :
                             user?.role === 'zone' ? 'Zone Admin' : user?.role}
                          </span>
                        </div>
                        <ChevronDown className="h-4 w-4" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={handleLogoutClick}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-h-0 w-0">
          {/* Header */}
          <header className="flex h-16 items-center gap-4 border-b bg-background px-4">
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden h-8 w-8 p-0"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="h-4 w-4" />
            </Button>

            <h1 className="text-lg font-semibold">Portal Sukan</h1>
          </header>

          {/* Content */}
          <main className="flex-1 overflow-auto p-4">
            <Outlet />
          </main>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <Dialog open={showLogoutModal} onOpenChange={setShowLogoutModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Pengesahan Log Keluar</DialogTitle>
            <DialogDescription>
              Adakah anda pasti ingin log keluar dari sistem? Anda perlu log masuk semula untuk mengakses dashboard.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-3 sm:gap-3">
            <Button variant="outline" onClick={handleLogoutCancel}>
              Batal
            </Button>
            <Button variant="destructive" onClick={handleLogoutConfirm}>
              Ya, Log Keluar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default function DashboardLayout() {
  return <DashboardLayoutContent />;
}
