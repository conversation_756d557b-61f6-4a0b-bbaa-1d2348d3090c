import { Outlet, <PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  LayoutGrid,
  Users,
  Trophy,
  FileText,
  Settings,
  BarChart3,
  ChevronsUpDown,
  LogOut,
  Menu
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarProvider,
  SidebarInset,
  SidebarTrigger
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/auth-context';
import { useSettings } from '@/contexts/settings-context';
import { useZone } from '@/contexts/zone-context';
import DynamicFavicon from '@/components/DynamicFavicon';

interface NavItem {
  title: string;
  href: string;
  icon: any;
}

function DashboardLayoutContent() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { settings } = useSettings();
  const { zone } = useZone();




  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      navigate('/auth/login');
    }
  };

  // Role-based navigation items
  const getMainNavItems = (): NavItem[] => {
    const baseItems: NavItem[] = [
      {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
      },
    ];

    // Add role-specific items based on user role
    if (user?.role === 'superadmin') {
      baseItems.push(
        {
          title: 'Pengurusan Peserta',
          href: '/dashboard/admin/players',
          icon: Users,
        },
        {
          title: 'Pengurusan Perlawanan',
          href: '/dashboard/admin/matches',
          icon: Trophy,
        },
        {
          title: 'CMS - Page Content',
          href: '/dashboard/admin/cms/page-content',
          icon: FileText,
        },
        {
          title: 'Laporan & Statistik',
          href: '/dashboard/admin/reports',
          icon: BarChart3,
        },
        {
          title: 'Tetapan Sistem',
          href: '/dashboard/admin/settings',
          icon: Settings,
        }
      );
    } else if (user?.role === 'admin') {
      baseItems.push(
        {
          title: 'Pengurusan Peserta',
          href: '/dashboard/admin/players',
          icon: Users,
        },
        {
          title: 'Pengurusan Perlawanan',
          href: '/dashboard/admin/matches',
          icon: Trophy,
        },
        {
          title: 'Laporan Zon',
          href: '/dashboard/admin/reports',
          icon: BarChart3,
        }
      );
    } else if (user?.role === 'zone') {
      baseItems.push(
        {
          title: 'Profil Zon',
          href: '/dashboard/admin/zone-profile',
          icon: LayoutGrid,
        },
        {
          title: 'Peserta',
          href: '/dashboard/admin/players',
          icon: Users,
        },
        {
          title: 'Jadual Perlawanan',
          href: '/dashboard/admin/matches',
          icon: Trophy,
        }
      );
    } else if (user?.role === 'media') {
      baseItems.push(
        {
          title: 'CMS - Page Content',
          href: '/dashboard/admin/cms/page-content',
          icon: FileText,
        }
      );
    }

    return baseItems;
  };

  return (
    <>
      {/* Dynamic Favicon */}
      <DynamicFavicon logoUrl={settings?.logo_url} />

      <SidebarProvider>
        <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link to="/dashboard">
                  {user?.role === 'zone' && zone ? (
                    <>
                      {zone.logo_url ? (
                        <img
                          src={zone.logo_url}
                          alt={`Logo ${zone.name}`}
                          className="aspect-square size-8 object-contain"
                        />
                      ) : (
                        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                          <Trophy className="size-4" />
                        </div>
                      )}
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">{zone.name}</span>
                        <span className="truncate text-xs">Portal Sukan</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                        <Trophy className="size-4" />
                      </div>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">Portal Sukan</span>
                        <span className="truncate text-xs">Admin Panel</span>
                      </div>
                    </>
                  )}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Platform</SidebarGroupLabel>
            <SidebarMenu>
              {getMainNavItems().map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={location.pathname === item.href || (item.href !== '/dashboard' && location.pathname.startsWith(item.href))}
                    tooltip={item.title}
                  >
                    <Link to={item.href}>
                      {item.icon && <item.icon className="size-4" />}
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton size="lg" className="group text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent">
                    <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                      <span className="text-sm font-semibold">
                        {user?.name?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">{user?.name || 'User'}</span>
                      <span className="truncate text-xs capitalize">{user?.role || 'user'}</span>
                    </div>
                    <ChevronsUpDown className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  align="end"
                >
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log Keluar
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>

      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <h1 className="text-lg font-semibold">Portal Sukan</h1>
        </header>
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </SidebarInset>
    </SidebarProvider>
    </>
  );
}

export default function DashboardLayout() {
  return (
    <DashboardLayoutContent />
  );
}
