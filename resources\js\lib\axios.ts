import axios from 'axios';
import toast from 'react-hot-toast';

// Get base URL from environment variable or fallback to current origin
const getBaseURL = () => {
  // Try to get from Vite environment variable first
  if (import.meta.env.VITE_APP_URL) {
    // Remove trailing slash to prevent double slash
    let baseUrl = import.meta.env.VITE_APP_URL.replace(/\/$/, '');

    // Force HTTPS in production if current page is HTTPS
    if (window.location.protocol === 'https:' && !baseUrl.startsWith('https:')) {
      baseUrl = baseUrl.replace('http:', 'https:');
    }

    return `${baseUrl}/api`;
  }

  // Fallback to current origin (will automatically use the same protocol)
  return `${window.location.origin}/api`;
};

// Create axios instance
const api = axios.create({
  baseURL: getBaseURL(),
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Setup CSRF token
const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
if (token) {
  api.defaults.headers.common['X-CSRF-TOKEN'] = token;
}

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Refresh CSRF token for each request
    const currentToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (currentToken) {
      config.headers['X-CSRF-TOKEN'] = currentToken;
    }

    // Add auth token if available
    const authToken = localStorage.getItem('auth_token');
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Only redirect to login if this is not a public API call and not already on login page
      const isPublicAPI = error.config?.url?.includes('/public/');
      const isLoginPage = window.location.pathname === '/auth/login';

      if (!isPublicAPI && !isLoginPage) {
        // Unauthorized - redirect to login
        localStorage.removeItem('auth_token');
        // Use React Router navigation instead of window.location
        // window.location.href = '/auth/login';
        // toast.error('Sesi anda telah tamat. Sila log masuk semula.');
      }
    } else if (error.response?.status === 403) {
      // Forbidden
      toast.error('Anda tidak mempunyai kebenaran untuk akses ini.');
    } else if (error.response?.status === 422) {
      // Validation errors
      const errors = error.response.data.errors;
      if (errors) {
        Object.values(errors).forEach((errorMessages: any) => {
          if (Array.isArray(errorMessages)) {
            errorMessages.forEach((message) => toast.error(message));
          } else {
            toast.error(errorMessages);
          }
        });
      }
    } else if (error.response?.status >= 500) {
      // Server errors
      toast.error('Ralat pelayan. Sila cuba lagi.');
    } else if (error.code === 'NETWORK_ERROR') {
      // Network errors
      toast.error('Ralat rangkaian. Sila periksa sambungan internet anda.');
    }

    return Promise.reject(error);
  }
);

// Create public API instance (no auth interceptors)
export const publicApi = axios.create({
  baseURL: getBaseURL(),
  withCredentials: false,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

export default api;
