import axios from 'axios';

// Get base URL from environment variable or fallback to current origin
const getBaseURL = () => {
  // Try to get from Vite environment variable first
  if (import.meta.env.VITE_APP_URL) {
    // Remove trailing slash to prevent double slash
    let baseUrl = import.meta.env.VITE_APP_URL.replace(/\/$/, '');

    // Force HTTPS in production if current page is HTTPS
    if (window.location.protocol === 'https:' && !baseUrl.startsWith('https:')) {
      baseUrl = baseUrl.replace('http:', 'https:');
    }

    return `${baseUrl}/api`;
  }

  // Fallback to current origin (will automatically use the same protocol)
  return `${window.location.origin}/api`;
};

// Create a separate axios instance for public API calls without interceptors
const publicApi = axios.create({
  baseURL: getBaseURL(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// No interceptors for public API to avoid authentication loops

export default publicApi;
