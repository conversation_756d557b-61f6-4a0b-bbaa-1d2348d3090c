import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Plus, List, Target, Calendar, Clock, Trophy, MapPin } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';

import MatchResultModal from '@/components/admin/match-result-modal';
import TournamentBracketManager from '@/components/admin/tournament-bracket-manager';
import MatchForm from '@/components/admin/match-form';
import MatchesStatsCards from '@/components/admin/matches/matches-stats-cards';
import MatchesFilters from '@/components/admin/matches/matches-filters';
import MatchesTable from '@/components/admin/matches/matches-table';
import MatchDeleteDialog from '@/components/admin/matches/match-delete-dialog';

import { useMatches } from '@/hooks/use-matches';
import { useMatchFilters } from '@/hooks/use-match-filters';
import { Match, BreadcrumbItem } from '@/types/matches';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Pengurusan Perlawanan',
        href: '/admin/matches',
    },
];

export default function Matches() {
    const { user } = useAuth();

    // Use custom hooks for data management
    const {
        matches,
        isLoadingMatches,
        addMatchMutation,
        editMatchMutation,
        deleteMatchMutation
    } = useMatches();

    // Use custom hook for filtering
    const {
        filters,
        updateFilters,
        filteredMatches,
        uniqueSports,
        uniqueVenues,
        uniqueZones
    } = useMatchFilters(matches);

    // Modal states
    const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
    const [isResultModalOpen, setIsResultModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [matchToDelete, setMatchToDelete] = useState<Match | null>(null);
    const [activeTab, setActiveTab] = useState('list');

    // Event handlers
    const handleSetResult = (match: Match) => {
        setSelectedMatch(match);
        setIsResultModalOpen(true);
    };

    const handleEditMatch = (match: Match) => {
        setSelectedMatch(match);
        setIsEditModalOpen(true);
    };

    const handleResultSuccess = () => {
        setIsResultModalOpen(false);
        setSelectedMatch(null);
    };

    const handleAddMatch = () => {
        setSelectedMatch(null);
        setIsAddModalOpen(true);
    };

    const handleDeleteMatch = (match: Match) => {
        setMatchToDelete(match);
        setIsDeleteDialogOpen(true);
    };

    const confirmDeleteMatch = async () => {
        if (!matchToDelete) return;
        deleteMatchMutation.mutate(matchToDelete.id);
        setIsDeleteDialogOpen(false);
        setMatchToDelete(null);
    };

    const handleAddSubmit = async (data: any) => {
        addMatchMutation.mutate(data);
        setIsAddModalOpen(false);
        setSelectedMatch(null);
    };

    const handleEditSubmit = async (data: any) => {
        if (!selectedMatch) return;
        editMatchMutation.mutate({ id: selectedMatch.id, data });
        setIsEditModalOpen(false);
        setSelectedMatch(null);
    };

    if (isLoadingMatches) {
        return <div className="flex items-center justify-center h-64"><div className="text-lg">Loading...</div></div>;
    }

    // Calculate stats for stats cards
    const stats = {
        totalMatches: matches.length,
        upcomingCount: matches.filter((m: Match) => m.status === 'scheduled').length,
        ongoingCount: matches.filter((m: Match) => m.status === 'ongoing').length,
        completedCount: matches.filter((m: Match) => m.status === 'completed').length,
    };

    return (
        <div className="space-y-6">
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Pengurusan Perlawanan
                        </h1>
                        <p className="text-gray-600">
                            Urus jadual, keputusan dan tournament bracket
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Kalendar
                        </Button>
                        {(user?.role === 'superadmin' || user?.role === 'admin') && (
                            <Button
                                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                                onClick={handleAddMatch}
                            >
                                <Plus className="h-4 w-4" />
                                Tambah Perlawanan
                            </Button>
                        )}
                    </div>
                </div>



                {/* Stats Cards */}
                <MatchesStatsCards stats={stats} />

                {/* Tabs for different views */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="list" className="flex items-center gap-2">
                            <List className="h-4 w-4" />
                            <span>Jadual Perlawanan</span>
                        </TabsTrigger>
                        <TabsTrigger value="bracket" className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            <span>Tournament Bracket</span>
                        </TabsTrigger>
                    </TabsList>

                    {/* List View - Tournament Schedule Format */}
                    <TabsContent value="list" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Jadual Perlawanan</CardTitle>
                                <CardDescription>
                                    Senarai lengkap perlawanan mengikut format rasmi kejohanan
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {/* Filters */}
                                <MatchesFilters
                                    filters={filters}
                                    onFiltersChange={updateFilters}
                                    uniqueSports={uniqueSports}
                                    uniqueVenues={uniqueVenues}
                                    uniqueZones={uniqueZones}
                                />

                                {/* Results count */}
                                <div className="mb-4 text-sm text-gray-600">
                                    Menunjukkan {filteredMatches.length} daripada {matches.length} perlawanan
                                    {filters.searchTerm && (
                                        <span className="ml-2 text-blue-600">
                                            untuk "{filters.searchTerm}"
                                        </span>
                                    )}
                                </div>

                                <MatchesTable
                                    matches={filteredMatches}
                                    onSetResult={handleSetResult}
                                    onEditMatch={handleEditMatch}
                                    onDeleteMatch={handleDeleteMatch}
                                    userRole={user?.role}
                                />

                                {filteredMatches.length === 0 && (
                                    <div className="text-center py-8 text-gray-500">
                                        <div className="h-12 w-12 mx-auto mb-4 opacity-50">📋</div>
                                        <p>Tiada perlawanan dijumpai</p>
                                        <p className="text-sm">Cuba ubah kriteria carian anda</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>



                    {/* Tournament Bracket View */}
                    <TabsContent value="bracket" className="space-y-4">
                        <TournamentBracketManager onMatchSelect={handleSetResult} />
                    </TabsContent>
                </Tabs>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-blue-600">
                                <Clock className="h-5 w-5" />
                                Perlawanan Hari Ini
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {matches.filter((m: Match) =>
                                    new Date(m.match_date).toDateString() === new Date().toDateString()
                                ).length}
                            </p>
                            <p className="text-sm text-gray-600">Jadual hari ini</p>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-green-600">
                                <Trophy className="h-5 w-5" />
                                Keputusan Terkini
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {String(matches.filter((m: Match) => m.status === 'completed').slice(0, 3).length || 0)}
                            </p>
                            <p className="text-sm text-gray-600">Keputusan baru</p>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-orange-600">
                                <MapPin className="h-5 w-5" />
                                Venue Aktif
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {String(new Set(matches.map((m: Match) => m.venue)).size || 0)}
                            </p>
                            <p className="text-sm text-gray-600">Lokasi perlawanan</p>
                        </CardContent>
                    </Card>
                </div>


            </div>

            {/* Modals */}
            <MatchResultModal
                match={selectedMatch}
                isOpen={isResultModalOpen}
                onClose={() => {
                    setIsResultModalOpen(false);
                    setSelectedMatch(null);
                }}
                onSuccess={handleResultSuccess}
            />

            {/* Add Match Modal */}
            <Dialog
                open={isAddModalOpen}
                onOpenChange={addMatchMutation.isPending ? undefined : setIsAddModalOpen}
            >
                <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
                    {/* Fixed Header */}
                    <div className="flex-shrink-0 px-6 py-4 border-b">
                        <DialogTitle>Tambah Perlawanan Baru</DialogTitle>
                    </div>

                    {/* Scrollable Content */}
                    <div className="flex-1 overflow-y-auto px-6 py-4">
                        <MatchForm
                            onCancel={() => setIsAddModalOpen(false)}
                            onSubmit={handleAddSubmit}
                            onSuccess={() => {
                                setIsAddModalOpen(false);
                                setSelectedMatch(null);
                            }}
                            isLoading={addMatchMutation.isPending}
                        />
                    </div>

                    {/* Fixed Footer */}
                    <div className="flex-shrink-0 px-6 py-4 border-t bg-white">
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsAddModalOpen(false)}
                                disabled={addMatchMutation.isPending}
                            >
                                Batal
                            </Button>
                            <Button
                                type="submit"
                                form="match-form"
                                disabled={addMatchMutation.isPending}
                                className="min-w-[140px]"
                            >
                                {addMatchMutation.isPending ? (
                                    <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        Menyimpan...
                                    </div>
                                ) : (
                                    'Tambah Perlawanan'
                                )}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Edit Match Modal */}
            <Dialog
                open={isEditModalOpen}
                onOpenChange={editMatchMutation.isPending ? undefined : setIsEditModalOpen}
            >
                <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
                    {/* Fixed Header */}
                    <div className="flex-shrink-0 px-6 py-4 border-b">
                        <DialogTitle>Edit Perlawanan</DialogTitle>
                    </div>

                    {/* Scrollable Content */}
                    <div className="flex-1 overflow-y-auto px-6 py-4">
                        {selectedMatch && (
                            <MatchForm
                                match={selectedMatch}
                                onCancel={() => setIsEditModalOpen(false)}
                                onSubmit={handleEditSubmit}
                                onSuccess={() => {
                                    setIsEditModalOpen(false);
                                    setSelectedMatch(null);
                                }}
                                isLoading={editMatchMutation.isPending}
                            />
                        )}
                    </div>

                    {/* Fixed Footer */}
                    <div className="flex-shrink-0 px-6 py-4 border-t bg-white">
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsEditModalOpen(false)}
                                disabled={editMatchMutation.isPending}
                            >
                                Batal
                            </Button>
                            <Button
                                type="submit"
                                form="match-form"
                                disabled={editMatchMutation.isPending}
                                className="min-w-[160px]"
                            >
                                {editMatchMutation.isPending ? (
                                    <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        Menyimpan...
                                    </div>
                                ) : (
                                    'Kemaskini Perlawanan'
                                )}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <MatchDeleteDialog
                isOpen={isDeleteDialogOpen}
                onClose={() => setIsDeleteDialogOpen(false)}
                onConfirm={confirmDeleteMatch}
                match={matchToDelete}
                isDeleting={deleteMatchMutation.isPending}
            />
        </div>
    );
}
