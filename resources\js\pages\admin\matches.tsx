import { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/axios';
import toast from 'react-hot-toast';
import { useAuth } from '@/contexts/auth-context';
import {
    Trophy,
    Plus,
    Calendar,
    Clock,
    MapPin,
    Users,
    Edit,
    Eye,
    CheckCircle,
    XCircle,
    List,
    Target,
    Play,
    Filter,
    Search,
    Trash2,
    Loader2,
    X
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';

import MatchResultModal from '@/components/admin/match-result-modal';
import TournamentBracketManager from '@/components/admin/tournament-bracket-manager';
import MatchForm from '@/components/admin/match-form';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogOverlay,
    DialogPortal,
} from '@/components/ui/dialog';
import { SportLogo, ZoneLogo } from '@/components/ui/logo-components';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Pengurusan Perlawanan',
        href: '/admin/matches',
    },
];

interface Match {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    notes?: string;
    bracket_round?: string;
    is_highlighted?: boolean;
    event_type?: 'head_to_head' | 'group_event' | 'knockout_group' | 'category_based';
    participating_zones?: string | string[];
    special_teams?: string;
    detailed_scores?: string;
    zone_rankings?: string | any[];
    points_zone_a?: number;
    points_zone_b?: number;
    court_field?: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    sports_schedule?: {
        id: number;
        schedule_name: string;
    };
}

interface Sport {
    id: number;
    name: string;
}

interface Zone {
    code: string;
    name: string;
}

interface MatchesProps {
    matches: {
        data: Match[];
        links: any;
        meta: any;
    };
    sports: Sport[];
    zones: Zone[];
    filters: {
        sport?: string;
        status?: string;
        date?: string;
    };
}

export default function Matches() {
    const { user } = useAuth();
    const queryClient = useQueryClient();


    const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
    const [isResultModalOpen, setIsResultModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [matchToDelete, setMatchToDelete] = useState<Match | null>(null);
    const [activeTab, setActiveTab] = useState('list');

    // Filters
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sportFilter, setSportFilter] = useState('all');
    const [dateFilter, setDateFilter] = useState('all');
    const [venueFilter, setVenueFilter] = useState('all');
    const [zoneFilter, setZoneFilter] = useState('all');

    // TanStack Query for fetching matches
    const { data: matches = [], isLoading: isLoadingMatches, error } = useQuery({
        queryKey: ['matches'],
        queryFn: async () => {
            // Request all matches by setting a high per_page value
            const response = await api.get('/admin/sport-matches', {
                params: {
                    per_page: 1000 // Get all matches without pagination
                }
            });

            // Ensure we have valid data structure
            if (response.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else if (response.data && Array.isArray(response.data)) {
                return response.data;
            } else {

                return [];
            }
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Mutations
    const addMatchMutation = useMutation({
        mutationFn: async (data: any) => {
            const payload = {
                sport_id: parseInt(data.sport_id),
                title: data.title,
                event_type: data.event_type,
                zone_a: data.zone_a,
                zone_b: data.zone_b,
                participating_zones: data.participating_zones,
                match_date: data.match_date,
                match_time: data.match_time,
                venue: data.venue,
                status: data.status || 'scheduled',
            };

            const response = await api.post('/admin/sport-matches', payload);
            return response.data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya ditambah');
            setIsAddModalOpen(false);
            setSelectedMatch(null);
        },
        onError: (error: any) => {

            const message = error.response?.data?.message || 'Gagal menyimpan perlawanan';
            toast.error(message);
        },
    });

    const editMatchMutation = useMutation({
        mutationFn: async ({ id, data }: { id: number; data: any }) => {
            const payload = {
                sport_id: parseInt(data.sport_id),
                title: data.title,
                event_type: data.event_type,
                zone_a: data.zone_a,
                zone_b: data.zone_b,
                participating_zones: data.participating_zones,
                match_date: data.match_date,
                match_time: data.match_time,
                venue: data.venue,
                status: data.status || 'scheduled',
            };

            console.log('Edit mutation payload:', payload);
            console.log('Edit mutation URL:', `/admin/sport-matches/${id}`);

            const response = await api.put(`/admin/sport-matches/${id}`, payload);
            return response.data;
        },
        onSuccess: (data) => {
            console.log('Edit mutation success:', data);
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya dikemaskini');
            setIsEditModalOpen(false);
            setSelectedMatch(null);
        },
        onError: (error: any) => {
            console.error('Edit mutation error:', error);
            console.error('Error response:', error.response?.data);
            const message = error.response?.data?.message || 'Gagal mengemaskini perlawanan';
            toast.error(message);
        },
    });

    const deleteMatchMutation = useMutation({
        mutationFn: async (id: number) => {
            await api.delete(`/admin/sport-matches/${id}`);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['matches'] });
            toast.success('Perlawanan berjaya dipadamkan');
            setIsDeleteDialogOpen(false);
            setMatchToDelete(null);
        },
        onError: (error: any) => {

            const message = error.response?.data?.message || 'Gagal memadamkan perlawanan';
            toast.error(message);
        },
    });

    // Use useMemo to avoid infinite loops
    const filteredMatches = useMemo(() => {
        let filtered = matches;

        if (searchTerm) {
            filtered = filtered.filter((match: Match) =>
                match.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                match.sport?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                match.zone_a.toLowerCase().includes(searchTerm.toLowerCase()) ||
                match.zone_b.toLowerCase().includes(searchTerm.toLowerCase()) ||
                match.venue.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (statusFilter && statusFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.status === statusFilter);
        }

        if (sportFilter && sportFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.sport?.name === sportFilter);
        }

        if (dateFilter && dateFilter !== 'all') {
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];

            filtered = filtered.filter((match: Match) => {
                const matchDate = new Date(match.match_date);
                const matchDateStr = matchDate.toISOString().split('T')[0];

                switch (dateFilter) {
                    case 'today':
                        return matchDateStr === todayStr;
                    case 'tomorrow':
                        const tomorrow = new Date(today);
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        return matchDateStr === tomorrow.toISOString().split('T')[0];
                    case 'this_week':
                        const weekStart = new Date(today);
                        weekStart.setDate(today.getDate() - today.getDay());
                        const weekEnd = new Date(weekStart);
                        weekEnd.setDate(weekStart.getDate() + 6);
                        return matchDate >= weekStart && matchDate <= weekEnd;
                    case 'next_week':
                        const nextWeekStart = new Date(today);
                        nextWeekStart.setDate(today.getDate() - today.getDay() + 7);
                        const nextWeekEnd = new Date(nextWeekStart);
                        nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
                        return matchDate >= nextWeekStart && matchDate <= nextWeekEnd;
                    default:
                        return true;
                }
            });
        }

        if (venueFilter && venueFilter !== 'all') {
            filtered = filtered.filter((match: Match) => match.venue === venueFilter);
        }

        if (zoneFilter && zoneFilter !== 'all') {
            filtered = filtered.filter((match: Match) =>
                match.zone_a === zoneFilter ||
                match.zone_b === zoneFilter ||
                (match.participating_zones &&
                 (typeof match.participating_zones === 'string'
                  ? JSON.parse(match.participating_zones).includes(zoneFilter)
                  : match.participating_zones.includes(zoneFilter)))
            );
        }

        return filtered;
    }, [matches, searchTerm, statusFilter, sportFilter, dateFilter, venueFilter, zoneFilter]);

    const handleSetResult = (match: Match) => {
        setSelectedMatch(match);
        setIsResultModalOpen(true);
    };

    const handleEditMatch = (match: Match) => {
        console.log('Edit match clicked:', match);
        setSelectedMatch(match);
        setIsEditModalOpen(true);
    };

    const handleResultSuccess = () => {
        queryClient.invalidateQueries({ queryKey: ['matches'] });
        setIsResultModalOpen(false);
        setSelectedMatch(null);
    };

    const handleAddMatch = () => {
        setSelectedMatch(null);
        setIsAddModalOpen(true);
    };

    const handleDeleteMatch = (match: Match) => {
        setMatchToDelete(match);
        setIsDeleteDialogOpen(true);
    };

    const confirmDeleteMatch = async () => {
        if (!matchToDelete) return;
        deleteMatchMutation.mutate(matchToDelete.id);
    };



    const handleAddSubmit = async (data: any) => {
        addMatchMutation.mutate(data);
    };

    const handleEditSubmit = async (data: any) => {
        if (!selectedMatch) return;
        console.log('Edit submit data:', data);
        console.log('Selected match ID:', selectedMatch.id);
        editMatchMutation.mutate({ id: selectedMatch.id, data });
    };

    if (isLoadingMatches) {
        return <div className="flex items-center justify-center h-64"><div className="text-lg">Loading...</div></div>;
    }

    // Calculate counts from data
    const totalMatches = matches.length;
    const upcomingCount = matches.filter((m: Match) => m.status === 'scheduled').length;
    const ongoingCount = matches.filter((m: Match) => m.status === 'ongoing').length;
    const completedCount = matches.filter((m: Match) => m.status === 'completed').length;

    // Get unique options for filters
    const uniqueSports = [...new Set(matches.map((m: Match) => m.sport?.name))].filter(Boolean);
    const uniqueVenues = [...new Set(matches.map((m: Match) => m.venue))].filter(Boolean);
    const uniqueZones = [...new Set([
        ...matches.map((m: Match) => m.zone_a),
        ...matches.map((m: Match) => m.zone_b),
        ...matches.flatMap((m: Match) => {
            if (m.participating_zones) {
                try {
                    return typeof m.participating_zones === 'string'
                        ? JSON.parse(m.participating_zones)
                        : m.participating_zones;
                } catch {
                    return [];
                }
            }
            return [];
        })
    ])].filter(Boolean);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'scheduled':
                return <Badge className="bg-blue-100 text-blue-800">Dijadualkan</Badge>;
            case 'ongoing':
                return <Badge className="bg-yellow-100 text-yellow-800">Sedang Berlangsung</Badge>;
            case 'completed':
                return <Badge className="bg-green-100 text-green-800">Selesai</Badge>;
            case 'cancelled':
                return <Badge className="bg-red-100 text-red-800">Dibatalkan</Badge>;
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    const formatDateTime = (date: string, time: string) => {
        try {
            const matchDate = new Date(date);
            if (isNaN(matchDate.getTime())) {
                return {
                    date: 'Invalid Date',
                    time: time || 'N/A'
                };
            }

            const formattedDate = matchDate.toLocaleDateString('ms-MY', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            return {
                date: String(formattedDate || 'N/A'),
                time: String(time || 'N/A')
            };
        } catch (error) {
            return {
                date: 'Error formatting date',
                time: String(time || 'N/A')
            };
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Pengurusan Perlawanan
                        </h1>
                        <p className="text-gray-600">
                            Urus jadual, keputusan dan tournament bracket
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Kalendar
                        </Button>
                        {(user?.role === 'superadmin' || user?.role === 'admin') && (
                            <Button
                                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                                onClick={handleAddMatch}
                            >
                                <Plus className="h-4 w-4" />
                                Tambah Perlawanan
                            </Button>
                        )}
                    </div>
                </div>



                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Akan Datang
                            </CardTitle>
                            <Clock className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{String(upcomingCount || 0)}</div>
                            <p className="text-xs text-muted-foreground">
                                Perlawanan dijadualkan
                            </p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Sedang Berlangsung
                            </CardTitle>
                            <Trophy className="h-4 w-4 text-yellow-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{String(ongoingCount || 0)}</div>
                            <p className="text-xs text-muted-foreground">
                                Perlawanan aktif
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Selesai
                            </CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{String(completedCount || 0)}</div>
                            <p className="text-xs text-muted-foreground">
                                Keputusan direkod
                            </p>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Jumlah Perlawanan
                            </CardTitle>
                            <Trophy className="h-4 w-4 text-purple-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{String(matches.length || 0)}</div>
                            <p className="text-xs text-muted-foreground">
                                Semua perlawanan
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Tabs for different views */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="list" className="flex items-center gap-2">
                            <List className="h-4 w-4" />
                            <span>Jadual Perlawanan</span>
                        </TabsTrigger>
                        <TabsTrigger value="bracket" className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            <span>Tournament Bracket</span>
                        </TabsTrigger>
                    </TabsList>

                    {/* List View - Tournament Schedule Format */}
                    <TabsContent value="list" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Jadual Perlawanan</CardTitle>
                                <CardDescription>
                                    Senarai lengkap perlawanan mengikut format rasmi kejohanan
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {/* Search and Filters inside table */}
                                <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
                                    <div className="flex flex-col lg:flex-row gap-4">
                                        <div className="flex-1">
                                            <div className="relative">
                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                                <Input
                                                    placeholder="Cari perlawanan, sukan, atau zon..."
                                                    value={searchTerm}
                                                    onChange={(e) => setSearchTerm(e.target.value)}
                                                    className="pl-10"
                                                />
                                            </div>
                                        </div>
                                        <div className="flex flex-col sm:flex-row gap-2">
                                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                                <SelectTrigger className="w-full sm:w-40">
                                                    <SelectValue placeholder="Semua Status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">Semua Status</SelectItem>
                                                    <SelectItem value="scheduled">Dijadualkan</SelectItem>
                                                    <SelectItem value="ongoing">Sedang Berlangsung</SelectItem>
                                                    <SelectItem value="completed">Selesai</SelectItem>
                                                    <SelectItem value="cancelled">Dibatalkan</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <Select value={sportFilter} onValueChange={setSportFilter}>
                                                <SelectTrigger className="w-full sm:w-40">
                                                    <SelectValue placeholder="Semua Sukan" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">Semua Sukan</SelectItem>
                                                    {uniqueSports.map((sport) => (
                                                        <SelectItem key={sport as string} value={sport as string}>
                                                            {String(sport || 'Unknown Sport')}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {/* Clear Filters Button */}
                                            {(searchTerm || statusFilter !== 'all' || sportFilter !== 'all') && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        setSearchTerm('');
                                                        setStatusFilter('all');
                                                        setSportFilter('all');
                                                        setDateFilter('all');
                                                        setVenueFilter('all');
                                                        setZoneFilter('all');
                                                    }}
                                                    className="flex items-center gap-1"
                                                >
                                                    <X className="h-3 w-3" />
                                                    Clear
                                                </Button>
                                            )}
                                        </div>
                                    </div>

                                    {/* Results count */}
                                    <div className="mt-3 text-sm text-gray-600">
                                        Menunjukkan {filteredMatches.length} daripada {matches.length} perlawanan
                                        {searchTerm && (
                                            <span className="ml-2 text-blue-600">
                                                untuk "{searchTerm}"
                                            </span>
                                        )}
                                    </div>
                                </div>

                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Tarikh & Masa</TableHead>
                                            <TableHead>Perlawanan</TableHead>
                                            <TableHead>Perlawanan</TableHead>
                                            <TableHead>Tempat</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead className="text-right">Tindakan</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredMatches.map((match: Match) => {
                                            const dateTime = formatDateTime(match.match_date, match.match_time);
                                            return (
                                                <TableRow
                                                    key={match.id}
                                                    className={match.is_highlighted ? 'bg-yellow-50 border-yellow-200' : ''}
                                                >
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="text-sm font-medium">
                                                                {String(dateTime.date || 'N/A')}
                                                            </div>
                                                            <div className="text-xs text-gray-600">
                                                                {String(dateTime.time || 'N/A')}
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="font-semibold text-gray-900">{String(match.title || 'N/A')}</div>
                                                            <div className="flex items-center gap-2">
                                                                <SportLogo
                                                                    sportName={String(match.sport?.name || '')}
                                                                    logoUrl={match.sport?.logo_url}
                                                                    className="w-4 h-4"
                                                                />
                                                                <span className="text-sm text-blue-600">{String(match.sport?.name || 'Unknown Sport')}</span>
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            {match.special_teams ? (
                                                                <div className="font-medium text-purple-700">
                                                                    {String(match.special_teams)}
                                                                </div>
                                                            ) : match.event_type === 'group_event' || match.event_type === 'knockout_group' ? (
                                                                <div className="text-center">
                                                                    <div className="font-medium text-purple-700 mb-2">
                                                                        {match.event_type === 'knockout_group' ? 'Knockout System' : 'Group Event'}
                                                                    </div>
                                                                    <div className="flex flex-wrap justify-center gap-1">
                                                                        {(() => {
                                                                            try {
                                                                                let zones: string[] = [];

                                                                                if (match.participating_zones) {
                                                                                    // Handle both string and array formats
                                                                                    if (typeof match.participating_zones === 'string') {
                                                                                        zones = JSON.parse(match.participating_zones);
                                                                                    } else if (Array.isArray(match.participating_zones)) {
                                                                                        zones = match.participating_zones;
                                                                                    }
                                                                                } else {
                                                                                    // For matches without participating_zones, construct from zone_a, zone_b, special_teams
                                                                                    if (match.zone_a) zones.push(match.zone_a);
                                                                                    if (match.zone_b) zones.push(match.zone_b);
                                                                                    if (match.special_teams) {
                                                                                        // Handle special_teams which might be comma-separated or single value
                                                                                        const specialTeams = match.special_teams.split(',').map(t => t.trim());
                                                                                        zones.push(...specialTeams);
                                                                                    }
                                                                                }

                                                                                if (zones.length === 0) {
                                                                                    return <span className="text-gray-400">Group Event</span>;
                                                                                }

                                                                                // Get ribbon color and text based on position
                                                                                const getRibbonStyle = (position: number) => {
                                                                                    switch (position) {
                                                                                        case 1:
                                                                                            return { bg: 'bg-yellow-500', text: '1st' };
                                                                                        case 2:
                                                                                            return { bg: 'bg-gray-400', text: '2nd' };
                                                                                        case 3:
                                                                                            return { bg: 'bg-amber-600', text: '3rd' };
                                                                                        case 4:
                                                                                            return { bg: 'bg-blue-500', text: '4th' };
                                                                                        default:
                                                                                            return { bg: 'bg-gray-500', text: `${position}th` };
                                                                                    }
                                                                                };

                                                                                // Create 2 rows layout
                                                                                const zonesWithRankings = zones.map((zoneCode: string) => {
                                                                                    let rankingPosition = null;
                                                                                    if (match.zone_rankings) {
                                                                                        try {
                                                                                            const rankings = typeof match.zone_rankings === 'string'
                                                                                                ? JSON.parse(match.zone_rankings)
                                                                                                : match.zone_rankings;


                                                                                            const zoneRanking = rankings.find((r: any) => {

                                                                                                return String(r.zone_code) === String(zoneCode);
                                                                                            });
                                                                                            rankingPosition = zoneRanking?.ranking_position;

                                                                                        } catch (e) {

                                                                                        }
                                                                                    }
                                                                                    return { zoneCode, rankingPosition };
                                                                                });

                                                                                // Split into 2 rows
                                                                                const midPoint = Math.ceil(zonesWithRankings.length / 2);
                                                                                const firstRow = zonesWithRankings.slice(0, midPoint);
                                                                                const secondRow = zonesWithRankings.slice(midPoint);

                                                                                return (
                                                                                    <div className="flex flex-col gap-2">
                                                                                        {/* Single Maskot Icon */}
                                                                                        <div className="flex justify-center items-center">
                                                                                            <div className="relative flex justify-center items-center">
                                                                                                <img
                                                                                                    src="/images/maskot.png"
                                                                                                    alt="Maskot"
                                                                                                    className="w-16 h-16 object-contain mx-auto"
                                                                                                />
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            } catch (e) {

                                                                                return <span className="text-gray-400">Group Event</span>;
                                                                            }
                                                                        })()}
                                                                    </div>
                                                                </div>
                                                            ) : (
                                                                <div className="flex items-center justify-center gap-4">
                                                                    {/* Check if this is a placeholder match */}
                                                                    {(match.zone_a === match.zone_b ||
                                                                      match.zone_a === 'TBD' ||
                                                                      match.zone_b === 'TBD' ||
                                                                      match.zone_a === 'W1' ||
                                                                      match.zone_b === 'W2' ||
                                                                      match.zone_a === 'W3' ||
                                                                      match.zone_b === 'W4' ||
                                                                      match.zone_a === 'Y' ||
                                                                      match.zone_b === 'Z' ||
                                                                      (match.bracket_round && (
                                                                          match.bracket_round.toLowerCase().includes('final') ||
                                                                          match.bracket_round.toLowerCase().includes('semi') ||
                                                                          match.bracket_round.toLowerCase().includes('third')
                                                                      ))) ? (
                                                                        <div className="flex items-center justify-center gap-4 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                                                                            <div className="text-center">
                                                                                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                                                                    <span className="text-gray-500 font-bold text-sm">
                                                                                        {match.zone_a === 'W1' ? 'W1' :
                                                                                         match.zone_a === 'W2' ? 'W2' :
                                                                                         match.zone_a === 'W3' ? 'W3' :
                                                                                         match.zone_a === 'W4' ? 'W4' :
                                                                                         match.zone_a === 'Y' ? 'Y' :
                                                                                         'TBD'}
                                                                                    </span>
                                                                                </div>
                                                                                <span className="text-xs text-gray-500">
                                                                                    {match.zone_a.startsWith('W') ? 'Pemenang' :
                                                                                     match.zone_a === 'Y' || match.zone_a === 'Z' ? 'Peserta' :
                                                                                     'Menunggu'}
                                                                                </span>
                                                                            </div>
                                                                            <span className="text-gray-400 text-lg font-bold">VS</span>
                                                                            <div className="text-center">
                                                                                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                                                                    <span className="text-gray-500 font-bold text-sm">
                                                                                        {match.zone_b === 'W1' ? 'W1' :
                                                                                         match.zone_b === 'W2' ? 'W2' :
                                                                                         match.zone_b === 'W3' ? 'W3' :
                                                                                         match.zone_b === 'W4' ? 'W4' :
                                                                                         match.zone_b === 'Z' ? 'Z' :
                                                                                         'TBD'}
                                                                                    </span>
                                                                                </div>
                                                                                <span className="text-xs text-gray-500">
                                                                                    {match.zone_b.startsWith('W') ? 'Pemenang' :
                                                                                     match.zone_b === 'Y' || match.zone_b === 'Z' ? 'Peserta' :
                                                                                     'Menunggu'}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    ) : (
                                                                        <>
                                                                            <div className="flex items-center justify-center">
                                                                                <img
                                                                                    src="/images/maskot.png"
                                                                                    alt="Maskot"
                                                                                    className="w-16 h-16 object-contain mx-auto"
                                                                                />
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                </div>
                                                            )}
                                                            {match.status === 'completed' && !match.participating_zones && (
                                                                <div className="flex items-center justify-center mt-2">
                                                                    <div className="text-center">
                                                                        {match.sport?.name === 'Bola Jaring' && match.points_zone_a !== undefined && match.points_zone_b !== undefined ? (
                                                                            <div className="text-lg font-bold text-green-700">
                                                                                {String(match.points_zone_a || 0)} - {String(match.points_zone_b || 0)}
                                                                            </div>
                                                                        ) : match.score_zone_a !== undefined && match.score_zone_b !== undefined ? (
                                                                            <div className="text-lg font-bold text-green-700">
                                                                                {String(match.score_zone_a || 0)} - {String(match.score_zone_b || 0)}
                                                                            </div>
                                                                        ) : (
                                                                            <span className="text-gray-400">-</span>
                                                                        )}
                                                                        {match.winner_zone === 'draw' && (
                                                                            <div className="text-xs text-yellow-600 mt-1">(Seri)</div>
                                                                        )}
                                                                    </div>


                                                                </div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="flex items-center gap-1">
                                                                <MapPin className="h-3 w-3 text-gray-400" />
                                                                <span className="text-sm">{String(match.venue || 'N/A')}</span>
                                                            </div>
                                                            {match.court_field && (
                                                                <div className="text-xs text-gray-500">
                                                                    {String(match.court_field)}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        {getStatusBadge(match.status)}
                                                        {match.bracket_round && (
                                                            <div className="mt-1">
                                                                <Badge variant="outline" className="text-xs">
                                                                    {String(match.bracket_round)}
                                                                </Badge>
                                                            </div>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        {/* Detailed Scores */}
                                                        {match.status === 'completed' && match.detailed_scores && (() => {
                                                            try {
                                                                // Handle both JSON string and object
                                                                const detailedData = typeof match.detailed_scores === 'string'
                                                                    ? JSON.parse(match.detailed_scores)
                                                                    : match.detailed_scores;

                                                                if (detailedData && detailedData.sets && detailedData.sets.length > 0) {
                                                                    // Filter out sets that weren't played (0-0) for set-based sports
                                                                    const playedSets = (match.sport?.name === 'Badminton' || match.sport?.name === 'Ping Pong')
                                                                        ? detailedData.sets.filter((set: any) => set.zone_a > 0 || set.zone_b > 0)
                                                                        : detailedData.sets;

                                                                    const scoresText = playedSets.map((set: any) => `${set.zone_a}-${set.zone_b}`).join(', ');
                                                                    return (
                                                                        <div className="text-sm text-gray-600 mb-2 text-right">
                                                                            {scoresText}
                                                                        </div>
                                                                    );
                                                                }
                                                            } catch (e) {

                                                                return null;
                                                            }
                                                            return null;
                                                        })()}



                                                        <div className="flex items-center gap-1 justify-end">
                                                            {match.status === 'completed' ? (
                                                                <div className="flex gap-1 flex-wrap">
                                                                    {/* Dynamic badges from detailed_scores */}
                                                                    {match.detailed_scores && (() => {
                                                                        try {
                                                                            // Handle both JSON string and object
                                                                            const detailedData = typeof match.detailed_scores === 'string'
                                                                                ? JSON.parse(match.detailed_scores)
                                                                                : match.detailed_scores;

                                                                            if (detailedData && detailedData.sets && detailedData.sets.length > 0) {
                                                                                // Filter out sets that weren't played (0-0) for set-based sports
                                                                                const playedSets = (match.sport?.name === 'Badminton' || match.sport?.name === 'Ping Pong')
                                                                                    ? detailedData.sets.filter((set: any) => set.zone_a > 0 || set.zone_b > 0)
                                                                                    : detailedData.sets;

                                                                                return playedSets.map((set: any, index: number) => (
                                                                                    <Badge key={index} variant="outline" className="text-xs">
                                                                                        {set.zone_a}-{set.zone_b}
                                                                                    </Badge>
                                                                                ));
                                                                            }
                                                                        } catch (e) {

                                                                        }
                                                                        return null;
                                                                    })()}
                                                                </div>
                                                            ) : match.status !== 'cancelled' && (
                                                                <Button
                                                                    size="sm"
                                                                    onClick={() => handleSetResult(match)}
                                                                    className="bg-green-600 hover:bg-green-700 text-xs px-2"
                                                                >
                                                                    <Trophy className="h-3 w-3 sm:mr-1" />
                                                                    <span className="hidden sm:inline">Set Keputusan</span>
                                                                </Button>
                                                            )}
                                                            {(user?.role === 'superadmin' || user?.role === 'admin') && (
                                                                <>
                                                                    <Button variant="outline" size="sm" onClick={() => handleEditMatch(match)} className="px-2">
                                                                        <Edit className="h-3 w-3" />
                                                                    </Button>
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => handleDeleteMatch(match)}
                                                                        className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                                                    >
                                                                        <Trash2 className="h-3 w-3" />
                                                                    </Button>
                                                                </>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                                {filteredMatches.length === 0 && (
                                    <div className="text-center py-8 text-gray-500">
                                        <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>Tiada perlawanan dijumpai</p>
                                        <p className="text-sm">Cuba ubah kriteria carian anda</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>



                    {/* Tournament Bracket View */}
                    <TabsContent value="bracket" className="space-y-4">
                        <TournamentBracketManager onMatchSelect={handleSetResult} />
                    </TabsContent>
                </Tabs>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-blue-600">
                                <Clock className="h-5 w-5" />
                                Perlawanan Hari Ini
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {matches.filter((m: Match) =>
                                    new Date(m.match_date).toDateString() === new Date().toDateString()
                                ).length}
                            </p>
                            <p className="text-sm text-gray-600">Jadual hari ini</p>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-green-600">
                                <Trophy className="h-5 w-5" />
                                Keputusan Terkini
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {String(matches.filter((m: Match) => m.status === 'completed').slice(0, 3).length || 0)}
                            </p>
                            <p className="text-sm text-gray-600">Keputusan baru</p>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-orange-600">
                                <MapPin className="h-5 w-5" />
                                Venue Aktif
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-2xl font-bold">
                                {String(new Set(matches.map((m: Match) => m.venue)).size || 0)}
                            </p>
                            <p className="text-sm text-gray-600">Lokasi perlawanan</p>
                        </CardContent>
                    </Card>
                </div>


            </div>

            {/* Modals */}
            <MatchResultModal
                match={selectedMatch}
                isOpen={isResultModalOpen}
                onClose={() => {
                    setIsResultModalOpen(false);
                    setSelectedMatch(null);
                }}
                onSuccess={handleResultSuccess}
            />

            {/* Add Match Modal */}
            <Dialog
                open={isAddModalOpen}
                onOpenChange={addMatchMutation.isPending ? undefined : setIsAddModalOpen}
            >
                <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
                    {/* Fixed Header */}
                    <div className="flex-shrink-0 px-6 py-4 border-b">
                        <DialogTitle>Tambah Perlawanan Baru</DialogTitle>
                    </div>

                    {/* Scrollable Content */}
                    <div className="flex-1 overflow-y-auto px-6 py-4">
                        <MatchForm
                            onCancel={() => setIsAddModalOpen(false)}
                            onSubmit={handleAddSubmit}
                            onSuccess={() => {
                                setIsAddModalOpen(false);
                                setSelectedMatch(null);
                            }}
                            isLoading={addMatchMutation.isPending}
                        />
                    </div>

                    {/* Fixed Footer */}
                    <div className="flex-shrink-0 px-6 py-4 border-t bg-white">
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsAddModalOpen(false)}
                                disabled={addMatchMutation.isPending}
                            >
                                Batal
                            </Button>
                            <Button
                                type="submit"
                                form="match-form"
                                disabled={addMatchMutation.isPending}
                                className="min-w-[140px]"
                            >
                                {addMatchMutation.isPending ? (
                                    <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        Menyimpan...
                                    </div>
                                ) : (
                                    'Tambah Perlawanan'
                                )}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Edit Match Modal */}
            <Dialog
                open={isEditModalOpen}
                onOpenChange={editMatchMutation.isPending ? undefined : setIsEditModalOpen}
            >
                <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
                    {/* Fixed Header */}
                    <div className="flex-shrink-0 px-6 py-4 border-b">
                        <DialogTitle>Edit Perlawanan</DialogTitle>
                    </div>

                    {/* Scrollable Content */}
                    <div className="flex-1 overflow-y-auto px-6 py-4">
                        {selectedMatch && (
                            <MatchForm
                                match={selectedMatch}
                                onCancel={() => setIsEditModalOpen(false)}
                                onSubmit={handleEditSubmit}
                                onSuccess={() => {
                                    setIsEditModalOpen(false);
                                    setSelectedMatch(null);
                                }}
                                isLoading={editMatchMutation.isPending}
                            />
                        )}
                    </div>

                    {/* Fixed Footer */}
                    <div className="flex-shrink-0 px-6 py-4 border-t bg-white">
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsEditModalOpen(false)}
                                disabled={editMatchMutation.isPending}
                            >
                                Batal
                            </Button>
                            <Button
                                type="submit"
                                form="match-form"
                                disabled={editMatchMutation.isPending}
                                className="min-w-[160px]"
                            >
                                {editMatchMutation.isPending ? (
                                    <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        Menyimpan...
                                    </div>
                                ) : (
                                    'Kemaskini Perlawanan'
                                )}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Padam Perlawanan</DialogTitle>
                    </DialogHeader>

                    <div className="py-4">
                        <p className="text-sm text-gray-600">
                            Adakah anda pasti ingin memadamkan perlawanan "{matchToDelete?.title}"?
                            Tindakan ini tidak boleh dibatalkan.
                        </p>
                    </div>

                    <div className="flex gap-3 justify-end pt-4 border-t">
                        <Button
                            variant="outline"
                            onClick={() => setIsDeleteDialogOpen(false)}
                            disabled={deleteMatchMutation.isPending}
                        >
                            Batal
                        </Button>
                        <Button
                            onClick={confirmDeleteMatch}
                            disabled={deleteMatchMutation.isPending}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {deleteMatchMutation.isPending ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Memadamkan...
                                </>
                            ) : (
                                'Padam'
                            )}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
