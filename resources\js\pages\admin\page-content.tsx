import { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { 
    FileText, 
    Edit, 
    Save, 
    Plus,
    Trash2,
    Eye,
    Globe
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import tab components
import HomepageTab from '@/components/admin/page-content/HomepageTab';
import MatchTab from '@/components/admin/page-content/MatchTab';
import TournamentTab from '@/components/admin/page-content/TournamentTab';
import GalleryTab from '@/components/admin/page-content/GalleryTab';
import NewsTab from '@/components/admin/page-content/NewsTab';
import RulesTab from '@/components/admin/page-content/RulesTab';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

interface PageContent {
    id: number;
    page: string;
    section?: string;
    content_key: string;
    content_value: string;
    content_type: string;
    description?: string;
    is_active: boolean;
    updated_by?: number;
    updatedBy?: {
        name: string;
    };
    updated_at: string;
}

interface PageContentProps {
    content: Record<string, Record<string, PageContent[]>>;
    pages: Record<string, string>;
    sections: Record<string, string>;
    totalContent: number;
}

export default function PageContentManagement() {
    const [content, setContent] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedPage, setSelectedPage] = useState<string>('homepage');
    const [editingContent, setEditingContent] = useState<any | null>(null);


    // Define pages mapping
    const pages = {
        homepage: 'Homepage',
        'match-schedule': 'Match Schedule',
        'tournament-results': 'Tournament Results',
        gallery: 'Gallery',
        news: 'News',
        rules: 'Rules'
    };

    // Define sections mapping
    const sections = {
        hero: 'Hero Section',
        about: 'About Section',
        features: 'Features Section',
        contact: 'Contact Section',
        footer: 'Footer Section'
    };

    // Calculate total content
    const totalContent = content?.length || 0;

    // Edit form state
    const [editData, setEditData] = useState({
        content_value: '',
        content_type: 'text',
        description: '',
    });
    const [editProcessing, setEditProcessing] = useState(false);
    const [editErrors, setEditErrors] = useState<any>({});



    useEffect(() => {
        const fetchContent = async () => {
            try {
                const response = await api.get('/cms/page-content');
                setContent(response.data);
            } catch (error) {
                // Handle error silently
            } finally {
                setIsLoading(false);
            }
        };

        fetchContent();
    }, []);

    if (isLoading) {
        return <div className="flex items-center justify-center h-64"><div className="text-lg">Loading...</div></div>;
    }

    const handleEdit = (contentItem: PageContent) => {
        setEditingContent(contentItem);
        setEditData({
            content_value: contentItem.content_value,
            content_type: contentItem.content_type,
            description: contentItem.description || '',
        });
    };

    const handleUpdate = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingContent) return;

        setEditProcessing(true);
        setEditErrors({});

        try {
            await api.put(`/cms/page-content/${editingContent.id}`, editData);
            setEditingContent(null);
            setEditData({
                content_value: '',
                content_type: 'text',
                description: '',
            });
            // Refresh content list
            const response = await api.get('/cms/page-content');
            setContent(response.data);
        } catch (error: any) {
            setEditErrors(error.response?.data?.errors || {});
        } finally {
            setEditProcessing(false);
        }
    };







    const previewUrl = (page: string) => {
        const urls: Record<string, string> = {
            'homepage': '/',
            'match-schedule': '/match-schedule',
            'tournament-results': '/tournament-results',
            'gallery': '/gallery',
            'news': '/news',
            'rules': '/rules',
        };
        return urls[page] || '/';
    };

    return (
        <div className="space-y-6">

            <div className="px-4 py-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Page Content Management</h1>
                        <p className="text-gray-600">Edit text content for all public pages</p>
                    </div>
                    
                    <div className="flex space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => window.open(previewUrl(selectedPage), '_blank')}
                        >
                            <Eye className="w-4 h-4 mr-2" />
                            Preview Page
                        </Button>
                    </div>
                </div>

                {/* Stats Card */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Content Items</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalContent}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pages</CardTitle>
                            <Globe className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{Object.keys(pages).length}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Current Page</CardTitle>
                            <Eye className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{pages[selectedPage as keyof typeof pages]}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Content by Page */}
                <Tabs value={selectedPage} onValueChange={setSelectedPage}>
                    <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                        {Object.entries(pages).map(([key, label]) => (
                            <TabsTrigger key={key} value={key} className="text-xs">
                                {label.split(' ')[0]}
                            </TabsTrigger>
                        ))}
                    </TabsList>

                    {/* Homepage Tab */}
                    <TabsContent value="homepage" className="mt-6">
                        <HomepageTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>

                    {/* Match Schedule Tab */}
                    <TabsContent value="match-schedule" className="mt-6">
                        <MatchTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>

                    {/* Tournament Tab */}
                    <TabsContent value="tournament-results" className="mt-6">
                        <TournamentTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>

                    {/* Gallery Tab */}
                    <TabsContent value="gallery" className="mt-6">
                        <GalleryTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>

                    {/* News Tab */}
                    <TabsContent value="news" className="mt-6">
                        <NewsTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>

                    {/* Rules Tab */}
                    <TabsContent value="rules" className="mt-6">
                        <RulesTab
                            content={content || []}
                            onUpdate={() => {
                                // Refresh content after updates
                                const fetchContent = async () => {
                                    try {
                                        const response = await api.get('/cms/page-content');
                                        setContent(response.data);
                                    } catch (error) {
                                        // Handle error silently
                                    }
                                };
                                fetchContent();
                            }}
                        />
                    </TabsContent>
                </Tabs>

                {/* Edit Content Modal */}
                {editingContent && (
                    <Dialog open={!!editingContent} onOpenChange={() => setEditingContent(null)}>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Edit Content: {editingContent.content_key}</DialogTitle>
                                <DialogDescription>
                                    {editingContent.description || 'Edit this content item'}
                                </DialogDescription>
                            </DialogHeader>
                            
                            <form onSubmit={handleUpdate} className="space-y-4">
                                <div>
                                    <Label htmlFor="edit-type">Content Type</Label>
                                    <Select value={editData.content_type} onValueChange={(value) => setEditData({...editData, content_type: value})}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="text">Text</SelectItem>
                                            <SelectItem value="html">HTML</SelectItem>
                                            <SelectItem value="image">Image URL</SelectItem>
                                            <SelectItem value="json">JSON</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label htmlFor="edit-value">Content Value</Label>
                                    <Textarea
                                        id="edit-value"
                                        value={editData.content_value}
                                        onChange={(e) => setEditData({...editData, content_value: e.target.value})}
                                        rows={6}
                                    />
                                    {editErrors.content_value && <p className="text-red-500 text-sm mt-1">{editErrors.content_value}</p>}
                                </div>

                                <div>
                                    <Label htmlFor="edit-description">Description</Label>
                                    <Input
                                        id="edit-description"
                                        value={editData.description}
                                        onChange={(e) => setEditData({...editData, description: e.target.value})}
                                        placeholder="Describe this content item..."
                                    />
                                </div>

                                <div className="flex justify-end space-x-2">
                                    <Button type="button" variant="outline" onClick={() => setEditingContent(null)}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={editProcessing}>
                                        <Save className="w-4 h-4 mr-2" />
                                        {editProcessing ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                )}
            </div>
        </div>
    );
}
