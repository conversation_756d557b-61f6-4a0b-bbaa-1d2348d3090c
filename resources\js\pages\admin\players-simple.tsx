import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';

export default function PlayersSimplePage() {
    const { user } = useAuth();
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Simple test - just set loading to false after 1 second
        const timer = setTimeout(() => {
            setLoading(false);
        }, 1000);

        return () => clearTimeout(timer);
    }, []);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Pen<PERSON><PERSON><PERSON>rta (Simple Test)
                    </h1>
                    <p className="text-gray-600">Test page untuk debug masalah</p>
                </div>
            </div>

            <div className="bg-white p-6 rounded-lg border">
                <h2 className="text-lg font-semibold mb-4">User Info</h2>
                <div className="space-y-2">
                    <p><strong>Name:</strong> {user?.name || 'N/A'}</p>
                    <p><strong>Role:</strong> {user?.role || 'N/A'}</p>
                    <p><strong>Zone:</strong> {user?.zone || 'N/A'}</p>
                </div>
            </div>

            <div className="bg-white p-6 rounded-lg border">
                <h2 className="text-lg font-semibold mb-4">Test Status</h2>
                <div className="text-green-600">
                    ✅ Page loaded successfully without errors
                </div>
            </div>
        </div>
    );
}
