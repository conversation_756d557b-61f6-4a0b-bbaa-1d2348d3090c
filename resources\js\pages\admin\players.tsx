import { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { useAuth } from '@/contexts/auth-context';
import { Users } from 'lucide-react';

import { toast } from 'react-hot-toast';
import SportsTable from '@/components/admin/players/SportsTable';
import SportManagementModal from '@/components/admin/players/SportManagementModal';
import PlayersListTable from '@/components/admin/players/PlayersListTable';
import PlayerDetailsModal from '@/components/admin/players/PlayerDetailsModal';

interface SportTeam {
    id: number;
    name: string;
    icon: string | null;
    sport_team: {
        id: number;
        team_leader_name: string;
        team_leader_phone: string;
        team_leader_contact: string | null;
    } | null;
    team_leader_name: string | null;
    main_players_count: number;
    substitute_players_count: number;
    total_players: number;
    can_manage: boolean;
}

interface Player {
    id: number;
    name: string;
    phone: string;
    player_type: 'main' | 'substitute';
    status: string;
    zone: string | null;
    zone_name: string | null;
    sport: string | null;
    sport_id: number | null;
    team_leader_name: string | null;
    team_leader_phone: string | null;
    created_at: string;
}
export default function PlayersPage() {
    const { user } = useAuth();
    const [sports, setSports] = useState<SportTeam[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedSport, setSelectedSport] = useState<SportTeam | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    // For player details modal (superadmin/admin)
    const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
    const [isPlayerModalOpen, setIsPlayerModalOpen] = useState(false);

    // Fetch sports with team information
    useEffect(() => {
        fetchSports();
    }, []);

    const fetchSports = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (user?.zone) {
                params.append('zone', user.zone);
            }

            const response = await api.get(`/admin/sport-teams?${params}`);
            setSports(response.data);
        } catch (error) {
            toast.error('Gagal memuat data sukan');
        } finally {
            setLoading(false);
        }
    };

    const handleManageSport = (sport: SportTeam) => {
        setSelectedSport(sport);
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedSport(null);
    };

    const handleRefresh = async () => {
        await fetchSports();
    };

    const handleViewPlayer = (player: Player) => {
        setSelectedPlayer(player);
        setIsPlayerModalOpen(true);
    };

    const handleClosePlayerModal = () => {
        setIsPlayerModalOpen(false);
        setSelectedPlayer(null);
    };
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        {user?.role === 'zone' ? `Pengurusan Peserta - ${user.zone}` : 'Pengurusan Peserta'}
                    </h1>
                    <p className="text-gray-600">Urus pendaftaran dan maklumat peserta mengikut sukan</p>
                </div>

            </div>

            {/* Conditional rendering based on user role */}
            {user?.role === 'zone' ? (
                <>
                    {/* Zone Admin View - Sports Management */}
                    <SportsTable
                        sports={sports}
                        loading={loading}
                        onManageSport={handleManageSport}
                        onRefresh={fetchSports}
                    />

                    {/* Sport Management Modal */}
                    <SportManagementModal
                        sport={selectedSport}
                        isOpen={isModalOpen}
                        onClose={handleCloseModal}
                        onRefresh={handleRefresh}
                    />
                </>
            ) : (
                <>
                    {/* Superadmin/Admin View - Players List */}
                    <PlayersListTable onViewPlayer={handleViewPlayer} />

                    {/* Player Details Modal */}
                    <PlayerDetailsModal
                        player={selectedPlayer}
                        isOpen={isPlayerModalOpen}
                        onClose={handleClosePlayerModal}
                    />
                </>
            )}
        </div>
    );
}
