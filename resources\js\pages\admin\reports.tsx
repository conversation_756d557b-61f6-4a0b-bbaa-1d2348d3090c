import { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { 
    BarChart3, 
    Download, 
    Calendar, 
    Users,
    Trophy,
    Target,
    TrendingUp,
    PieC<PERSON>,
    FileText,
    Filter
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Laporan & Statistik',
        href: '/admin/reports',
    },
];

interface ReportData {
    zoneStats: Array<{
        zone: string;
        totalPlayers: number;
        activeMatches: number;
        winRate: number;
        participation: number;
    }>;
    sportStats: Array<{
        sport: string;
        participants: number;
        matches: number;
        popularity: number;
    }>;
    performanceData: Array<{
        date: string;
        matches: number;
        participants: number;
        completion: number;
    }>;
}



export default function Reports() {
    const [reportData, setReportData] = useState<any>({
        by_zone: [],
        by_sport: [],
        monthly_progress: []
    });
    const [totalParticipants, setTotalParticipants] = useState(0);
    const [totalMatches, setTotalMatches] = useState(0);
    const [completionRate, setCompletionRate] = useState(0);
    const [averageParticipation, setAverageParticipation] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchReports = async () => {
            try {
                const response = await api.get('/reports');
                setReportData(response.data);

                // Use data from API response
                if (response.data.summary) {
                    setTotalParticipants(response.data.summary.total_participants || 0);
                    setTotalMatches(response.data.summary.total_matches || 0);
                    setCompletionRate(response.data.summary.completion_rate || 0);
                    setAverageParticipation(response.data.summary.average_participation || 0);
                }
            } catch (error) {
                // Set fallback values on error
                setTotalParticipants(0);
                setTotalMatches(0);
                setCompletionRate(0);
                setAverageParticipation(0);
            } finally {
                setIsLoading(false);
            }
        };

        fetchReports();
    }, []);

    if (isLoading) {
        return <div className="flex items-center justify-center h-64"><div className="text-lg">Loading...</div></div>;
    }

    // Add safety check for variables
    const safeTotal = totalParticipants || 0;
    const safeTotalMatches = totalMatches || 0;
    const safeCompletionRate = completionRate || 0;
    const safeAverageParticipation = averageParticipation || 0;

    return (
        <div className="space-y-6">

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Laporan & Statistik
                        </h1>
                        <p className="text-gray-600">
                            Analisis prestasi dan statistik Portal Sukan Intra KKD
                        </p>
                    </div>
                    
                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <Filter className="h-4 w-4" />
                            Tapis Tarikh
                        </Button>
                        <Button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
                            <Download className="h-4 w-4" />
                            Export Laporan
                        </Button>
                    </div>
                </div>

                {/* Overview Stats */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Jumlah Peserta
                            </CardTitle>
                            <Users className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeTotal}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-green-600">+12%</span> dari bulan lalu
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Jumlah Perlawanan
                            </CardTitle>
                            <Trophy className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeTotalMatches}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-green-600">+8%</span> dari sasaran
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Kadar Penyiapan
                            </CardTitle>
                            <Target className="h-4 w-4 text-purple-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeCompletionRate}%</div>
                            <p className="text-xs text-muted-foreground">
                                Perlawanan selesai
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Purata Penyertaan
                            </CardTitle>
                            <TrendingUp className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{safeAverageParticipation}%</div>
                            <p className="text-xs text-muted-foreground">
                                Kadar kehadiran
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Reports Tabs */}
                <Tabs defaultValue="zones" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="zones" className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            Prestasi Zon
                        </TabsTrigger>
                        <TabsTrigger value="sports" className="flex items-center gap-2">
                            <Trophy className="h-4 w-4" />
                            Statistik Sukan
                        </TabsTrigger>
                        <TabsTrigger value="performance" className="flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            Prestasi Keseluruhan
                        </TabsTrigger>
                    </TabsList>

                    {/* Zone Performance Tab */}
                    <TabsContent value="zones" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Prestasi Mengikut Zon</CardTitle>
                                <CardDescription>
                                    Analisis prestasi dan penyertaan setiap zon
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Zon</TableHead>
                                            <TableHead>Jumlah Peserta</TableHead>
                                            <TableHead>Perlawanan Aktif</TableHead>
                                            <TableHead>Kadar Kemenangan</TableHead>
                                            <TableHead>Penyertaan</TableHead>
                                            <TableHead>Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {reportData.by_zone?.map((zone, index) => (
                                            <TableRow key={index}>
                                                <TableCell className="font-medium">
                                                    Zon {zone.zone || zone.name}
                                                </TableCell>
                                                <TableCell>{zone.totalPlayers || zone.total_players || 0}</TableCell>
                                                <TableCell>{zone.activeMatches || zone.active_matches || 0}</TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <span>{zone.winRate || zone.win_rate || 0}%</span>
                                                        <div className="w-16 h-2 bg-gray-200 rounded-full">
                                                            <div 
                                                                className="h-2 bg-green-500 rounded-full"
                                                                style={{ width: `${zone.winRate || zone.win_rate || 0}%` }}
                                                            />
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge 
                                                        variant={(zone.participation || 0) >= 80 ? 'default' : 'secondary'}
                                                        className={(zone.participation || 0) >= 80 ? 'bg-green-100 text-green-800' : ''}
                                                    >
                                                        {zone.participation || 0}%
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge 
                                                        variant={(zone.participation || 0) >= 80 ? 'default' : 'secondary'}
                                                        className={(zone.participation || 0) >= 80 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}
                                                    >
                                                        {(zone.participation || 0) >= 80 ? 'Cemerlang' : 'Sederhana'}
                                                    </Badge>
                                                </TableCell>
                                            </TableRow>
                                        )) || []}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Sports Statistics Tab */}
                    <TabsContent value="sports" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Statistik Mengikut Sukan</CardTitle>
                                <CardDescription>
                                    Populariti dan penyertaan setiap kategori sukan
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2">
                                    {reportData.by_sport?.map((sport, index) => (
                                        <Card key={index}>
                                            <CardHeader className="pb-3">
                                                <CardTitle className="text-lg">{sport.sport || sport.name}</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="space-y-2">
                                                    <div className="flex justify-between">
                                                        <span className="text-sm text-gray-600">Peserta</span>
                                                        <span className="font-medium">{sport.participants || sport.total_participants || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-sm text-gray-600">Perlawanan</span>
                                                        <span className="font-medium">{sport.matches || sport.total_matches || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-sm text-gray-600">Populariti</span>
                                                        <div className="flex items-center gap-2">
                                                            <span className="text-sm font-medium">{sport.popularity || 0}%</span>
                                                            <div className="w-12 h-2 bg-gray-200 rounded-full">
                                                                <div
                                                                    className="h-2 bg-blue-500 rounded-full"
                                                                    style={{ width: `${sport.popularity || 0}%` }}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )) || []}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Performance Tab */}
                    <TabsContent value="performance" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Prestasi Keseluruhan</CardTitle>
                                <CardDescription>
                                    Trend prestasi dan penyertaan mengikut masa
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Tarikh</TableHead>
                                            <TableHead>Perlawanan</TableHead>
                                            <TableHead>Peserta</TableHead>
                                            <TableHead>Kadar Penyiapan</TableHead>
                                            <TableHead>Trend</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {reportData.monthly_progress?.map((data, index) => (
                                            <TableRow key={index}>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-4 w-4 text-gray-400" />
                                                        {data.date || data.month}
                                                    </div>
                                                </TableCell>
                                                <TableCell>{data.matches || data.total_matches || 0}</TableCell>
                                                <TableCell>{data.participants || data.total_participants || 0}</TableCell>
                                                <TableCell>
                                                    <Badge 
                                                        variant={(data.completion || 0) >= 80 ? 'default' : 'secondary'}
                                                        className={(data.completion || 0) >= 80 ? 'bg-green-100 text-green-800' : ''}
                                                    >
                                                        {data.completion || 0}%
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <TrendingUp className="h-4 w-4 text-green-600" />
                                                </TableCell>
                                            </TableRow>
                                        )) || []}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}
