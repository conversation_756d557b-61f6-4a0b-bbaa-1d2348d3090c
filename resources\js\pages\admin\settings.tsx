import { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Eye, Globe, Bell } from 'lucide-react';

// Import tab components
import GeneralSettingsTab from '@/components/admin/settings/GeneralSettingsTab';
import NotificationSettingsTab from '@/components/admin/settings/NotificationSettingsTab';

export default function Settings() {
    const [activeTab, setActiveTab] = useState('general');
    const [settings, setSettings] = useState({});
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            const response = await api.get('/settings');
            setSettings(response.data);
        } catch (error) {
            // Handle error silently
        } finally {
            setIsLoading(false);
        }
    };

    const previewHomepage = () => {
        window.open('/', '_blank');
    };

    return (
        <div className="space-y-6">
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Tetapan Sistem
                        </h1>
                        <p className="text-gray-600">
                            Konfigurasi dan pengurusan tetapan Portal Sukan Intra KKD
                        </p>
                    </div>

                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={previewHomepage}
                        >
                            <Eye className="h-4 w-4 mr-2" />
                            Preview Homepage
                        </Button>
                    </div>
                </div>

                {/* Settings Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="general" className="flex items-center gap-2">
                            <Globe className="h-4 w-4" />
                            Tetapan Umum
                        </TabsTrigger>
                        <TabsTrigger value="notification" className="flex items-center gap-2">
                            <Bell className="h-4 w-4" />
                            Notifikasi & Media Sosial
                        </TabsTrigger>
                    </TabsList>

                    {/* General Settings */}
                    <TabsContent value="general" className="space-y-4">
                        {isLoading ? (
                            <div className="flex justify-center p-8">
                                <div className="text-gray-500">Memuat tetapan...</div>
                            </div>
                        ) : (
                            <GeneralSettingsTab
                                settings={settings}
                                onSettingsUpdate={fetchSettings}
                            />
                        )}
                    </TabsContent>

                    {/* Notification Settings */}
                    <TabsContent value="notification" className="space-y-4">
                        {isLoading ? (
                            <div className="flex justify-center p-8">
                                <div className="text-gray-500">Memuat tetapan...</div>
                            </div>
                        ) : (
                            <NotificationSettingsTab
                                settings={settings}
                                onSettingsUpdate={fetchSettings}
                            />
                        )}
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}
