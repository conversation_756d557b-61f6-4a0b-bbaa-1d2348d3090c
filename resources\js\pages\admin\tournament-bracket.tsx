import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy, Target, Zap } from 'lucide-react';
import TournamentBracketManager from '@/components/admin/tournament-bracket-manager';

export default function TournamentBracketPage() {
    const handleMatchSelect = (match: any) => {

        // Handle match selection logic here
    };

    return (
        <div className="container mx-auto p-6 space-y-6">
            {/* Page Header */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-6 w-6 text-yellow-600" />
                        Tournament Bracket Management
                    </CardTitle>
                    <CardDescription>
                        Urus dan papar tournament bracket untuk semua sukan dalam Portal Sukan Intra Kor Kesihatan DiRaja 2025
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <Zap className="h-8 w-8 text-blue-600" />
                            <div>
                                <h3 className="font-semibold text-blue-900">Modern Bracket</h3>
                                <p className="text-sm text-blue-700">Interactive tournament bracket dengan @sportsgram/brackets</p>
                            </div>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                            <Trophy className="h-8 w-8 text-yellow-600" />
                            <div>
                                <h3 className="font-semibold text-yellow-900">Professional Bracket</h3>
                                <p className="text-sm text-yellow-700">Professional tournament display dengan advanced features</p>
                            </div>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <Target className="h-8 w-8 text-gray-600" />
                            <div>
                                <h3 className="font-semibold text-gray-900">Legacy Bracket</h3>
                                <p className="text-sm text-gray-700">Traditional bracket view untuk reference</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Tournament Bracket Manager */}
            <TournamentBracketManager onMatchSelect={handleMatchSelect} />
        </div>
    );
}
