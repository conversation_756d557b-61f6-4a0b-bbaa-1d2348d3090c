import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/axios';
import {
    Building2,
    Upload,
    Save,
    Loader2,
    Users,
    Edit,
    Camera,
    User
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { z } from 'zod';
import { useZone } from '@/contexts/zone-context';

// Validation schema
const zoneProfileSchema = z.object({
    name: z.string()
        .min(1, 'Nama zon diperlukan')
        .min(2, 'Nama zon mestilah sekurang-kurangnya 2 aksara')
        .max(100, 'Nama zon tidak boleh melebihi 100 aksara'),
    description: z.string().optional(),
    color_primary: z.string()
        .regex(/^#[0-9A-F]{6}$/i, 'Format warna tidak sah (gunakan format hex: #RRGGBB)'),
    color_secondary: z.string()
        .regex(/^#[0-9A-F]{6}$/i, 'Format warna tidak sah (gunakan format hex: #RRGGBB)'),
    leader_id: z.number().nullable().optional(),
    leader_name: z.string().optional(),
    leader_contact: z.string().optional(),
});

type ZoneProfileFormData = z.infer<typeof zoneProfileSchema>;

interface Zone {
    id: number;
    code: string;
    name: string;
    description?: string;
    logo_url?: string;
    color_primary: string;
    color_secondary: string;
    leader_id?: number;
    leader_name?: string;
    leader_contact?: string;
    leader?: {
        id: number;
        name: string;
        username: string;
    };
    is_active: boolean;
    display_order: number;
    created_at: string;
    updated_at: string;
}

interface User {
    id: number;
    name: string;
    username: string;
    role: string;
    zone?: string;
}

export default function ZoneProfile() {
    const { user } = useAuth();
    const { zone: contextZone, updateZone } = useZone();
    const [zone, setZone] = useState<Zone | null>(null);
    const [availableUsers, setAvailableUsers] = useState<User[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isUploadingLogo, setIsUploadingLogo] = useState(false);
    
    // Form data
    const [formData, setFormData] = useState<ZoneProfileFormData>({
        name: '',
        description: '',
        color_primary: '#3B82F6',
        color_secondary: '#1E40AF',
        leader_id: null,
        leader_name: '',
        leader_contact: '',
    });
    
    // Validation errors
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    useEffect(() => {
        const fetchData = async () => {
            try {
                if (user?.role === 'zone' && user.zone) {
                    // Fetch zone data for zone admin
                    const zoneResponse = await api.get(`/admin/zones/${user.zone}`);
                    const zoneData = zoneResponse.data;
                    setZone(zoneData);
                    
                    setFormData({
                        name: zoneData.name || '',
                        description: zoneData.description || '',
                        color_primary: zoneData.color_primary || '#3B82F6',
                        color_secondary: zoneData.color_secondary || '#1E40AF',
                        leader_id: zoneData.leader_id || null,
                        leader_name: zoneData.leader_name || '',
                        leader_contact: zoneData.leader_contact || '',
                    });
                    
                    // Fetch available users for leader assignment
                    const usersResponse = await api.get('/admin/users', {
                        params: { zone: user.zone, role: 'zone' }
                    });
                    setAvailableUsers(usersResponse.data || []);
                }
            } catch (error) {

                toast.error('Gagal memuat data zon');
            } finally {
                setIsLoading(false);
            }
        };

        if (user) {
            fetchData();
        }
    }, [user]);

    const handleInputChange = (field: keyof ZoneProfileFormData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear validation error when user starts typing
        if (validationErrors[field]) {
            setValidationErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            toast.error('Sila pilih fail imej yang sah');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            toast.error('Saiz fail tidak boleh melebihi 5MB');
            return;
        }

        setIsUploadingLogo(true);
        try {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('section', 'zone-logo');
            formData.append('width', '400');
            formData.append('height', '400');
            formData.append('quality', '90');

            const response = await api.post('/cms/upload-image', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.success && response.data.data.url) {
                const logoUrl = response.data.data.url;

                // Update zone logo only (not full form data)
                await api.patch(`/admin/zones/${zone?.code}/logo`, {
                    logo_url: logoUrl
                });

                // Update local state
                const updatedZone = zone ? { ...zone, logo_url: logoUrl } : null;
                setZone(updatedZone);

                // Update zone context to reflect changes in sidebar
                if (updatedZone) {
                    updateZone(updatedZone);
                }

                toast.success(`Logo zon berjaya dikemaskini! Size: ${response.data.data.file_size_kb}KB`);
            } else {
                throw new Error(response.data.message || 'Failed to upload logo');
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Gagal memuat naik logo');
        } finally {
            setIsUploadingLogo(false);
        }
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);
        setValidationErrors({});

        try {
            // Validate form data
            const validationResult = zoneProfileSchema.safeParse(formData);

            if (!validationResult.success) {
                const errors: Record<string, string> = {};
                validationResult.error.issues.forEach((issue) => {
                    if (issue.path[0]) {
                        errors[issue.path[0] as string] = issue.message;
                    }
                });
                setValidationErrors(errors);
                toast.error('Sila betulkan ralat dalam borang');
                return;
            }

            // Submit to API
            await api.put(`/admin/zones/${zone?.code}`, validationResult.data);
            toast.success('Profil zon berjaya dikemaskini');

            // Refresh zone data
            const zoneResponse = await api.get(`/admin/zones/${user?.zone}`);
            const updatedZone = zoneResponse.data;
            setZone(updatedZone);

            // Update zone context to reflect changes in sidebar and other components
            updateZone(updatedZone);
        } catch (error: any) {
            if (error.response?.status === 422) {
                setValidationErrors(error.response.data.errors || {});
            }
            toast.error(error.response?.data?.message || 'Ralat berlaku semasa menyimpan data');
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!user || user.role !== 'zone') {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <h2 className="text-xl font-semibold text-gray-900">Akses Ditolak</h2>
                    <p className="text-gray-600">Halaman ini hanya untuk pentadbir zon</p>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                            Profil {zone?.name || `Zon ${zone?.code}`}
                        </h1>
                        <p className="text-gray-600">
                            Urus maklumat dan tetapan zon anda
                        </p>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Zone Logo */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Camera className="h-5 w-5" />
                                Logo Zon
                            </CardTitle>
                            <CardDescription>
                                Muat naik logo untuk zon anda
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex flex-col items-center space-y-4">
                                {zone?.logo_url ? (
                                    <div className="relative">
                                        <img
                                            src={zone.logo_url}
                                            alt={`Logo ${zone.name}`}
                                            className="w-32 h-32 object-cover rounded-lg border-2 border-gray-200"
                                        />
                                    </div>
                                ) : (
                                    <div className="w-32 h-32 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                        <Building2 className="h-12 w-12 text-gray-400" />
                                    </div>
                                )}
                                
                                <div className="w-full">
                                    <Label htmlFor="logo-upload" className="cursor-pointer">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            disabled={isUploadingLogo}
                                            className="w-full"
                                            asChild
                                        >
                                            <span>
                                                {isUploadingLogo ? (
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                ) : (
                                                    <Upload className="w-4 h-4 mr-2" />
                                                )}
                                                {isUploadingLogo ? 'Memuat naik...' : 'Pilih Logo'}
                                            </span>
                                        </Button>
                                    </Label>
                                    <input
                                        id="logo-upload"
                                        type="file"
                                        accept="image/*"
                                        onChange={handleLogoUpload}
                                        className="hidden"
                                        disabled={isUploadingLogo}
                                    />
                                </div>
                                <p className="text-xs text-gray-500 text-center">
                                    Format: JPG, PNG, GIF. Maksimum: 5MB
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Zone Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Edit className="h-5 w-5" />
                                Maklumat Zon
                            </CardTitle>
                            <CardDescription>
                                Kemaskini maklumat asas zon
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="zone-code">Kod Zon</Label>
                                <Input
                                    id="zone-code"
                                    value={zone?.code || ''}
                                    disabled
                                    className="bg-gray-50"
                                />
                                <p className="text-xs text-gray-500">Kod zon tidak boleh diubah</p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="zone-name">Nama Zon *</Label>
                                <Input
                                    id="zone-name"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    placeholder="Masukkan nama zon"
                                    className={validationErrors.name ? 'border-red-500' : ''}
                                />
                                {validationErrors.name && (
                                    <p className="text-sm text-red-500">{validationErrors.name}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="zone-description">Penerangan</Label>
                                <Textarea
                                    id="zone-description"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    placeholder="Masukkan penerangan zon"
                                    rows={3}
                                />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Color Settings */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Tetapan Warna</CardTitle>
                            <CardDescription>
                                Pilih warna tema untuk zon anda
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="color-primary">Warna Utama *</Label>
                                <div className="flex gap-2">
                                    <Input
                                        id="color-primary"
                                        type="color"
                                        value={formData.color_primary}
                                        onChange={(e) => handleInputChange('color_primary', e.target.value)}
                                        className="w-16 h-10 p-1 border rounded"
                                    />
                                    <Input
                                        value={formData.color_primary}
                                        onChange={(e) => handleInputChange('color_primary', e.target.value)}
                                        placeholder="#3B82F6"
                                        className={`flex-1 ${validationErrors.color_primary ? 'border-red-500' : ''}`}
                                    />
                                </div>
                                {validationErrors.color_primary && (
                                    <p className="text-sm text-red-500">{validationErrors.color_primary}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="color-secondary">Warna Sekunder *</Label>
                                <div className="flex gap-2">
                                    <Input
                                        id="color-secondary"
                                        type="color"
                                        value={formData.color_secondary}
                                        onChange={(e) => handleInputChange('color_secondary', e.target.value)}
                                        className="w-16 h-10 p-1 border rounded"
                                    />
                                    <Input
                                        value={formData.color_secondary}
                                        onChange={(e) => handleInputChange('color_secondary', e.target.value)}
                                        placeholder="#1E40AF"
                                        className={`flex-1 ${validationErrors.color_secondary ? 'border-red-500' : ''}`}
                                    />
                                </div>
                                {validationErrors.color_secondary && (
                                    <p className="text-sm text-red-500">{validationErrors.color_secondary}</p>
                                )}
                            </div>

                            {/* Color Preview */}
                            <div className="space-y-2">
                                <Label>Pratonton Warna</Label>
                                <div className="flex gap-2">
                                    <div
                                        className="w-16 h-10 rounded border"
                                        style={{ backgroundColor: formData.color_primary }}
                                        title="Warna Utama"
                                    ></div>
                                    <div
                                        className="w-16 h-10 rounded border"
                                        style={{ backgroundColor: formData.color_secondary }}
                                        title="Warna Sekunder"
                                    ></div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Zone Leader Assignment */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Ketua Zon
                            </CardTitle>
                            <CardDescription>
                                Lantik ketua untuk zon ini
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="zone-leader">Pilih Ketua Zon</Label>
                                <Select
                                    value={formData.leader_id?.toString() || 'none'}
                                    onValueChange={(value) => handleInputChange('leader_id', value === 'none' ? null : parseInt(value))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Pilih ketua zon" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">Tiada ketua</SelectItem>
                                        {availableUsers.map((user) => (
                                            <SelectItem key={user.id} value={user.id.toString()}>
                                                {user.name} ({user.username})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-gray-500">
                                    Hanya pengguna dengan peranan 'zone' dalam zon ini yang boleh dipilih
                                </p>
                            </div>

                            {/* Leader Details */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="leader-name">Nama Penuh Ketua</Label>
                                    <Input
                                        id="leader-name"
                                        type="text"
                                        value={formData.leader_name || ''}
                                        onChange={(e) => handleInputChange('leader_name', e.target.value)}
                                        placeholder="Masukkan nama penuh ketua zon"
                                        className={validationErrors.leader_name ? 'border-red-500' : ''}
                                    />
                                    {validationErrors.leader_name && (
                                        <p className="text-sm text-red-600">{validationErrors.leader_name}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="leader-contact">Nombor Telefon Ketua</Label>
                                    <Input
                                        id="leader-contact"
                                        type="text"
                                        value={formData.leader_contact || ''}
                                        onChange={(e) => handleInputChange('leader_contact', e.target.value)}
                                        placeholder="Contoh: 013-1234567"
                                        className={validationErrors.leader_contact ? 'border-red-500' : ''}
                                    />
                                    {validationErrors.leader_contact && (
                                        <p className="text-sm text-red-600">{validationErrors.leader_contact}</p>
                                    )}
                                </div>
                            </div>

                            {zone?.leader && (
                                <div className="p-3 bg-blue-50 rounded-lg">
                                    <p className="text-sm font-medium text-blue-900">Ketua Semasa:</p>
                                    <p className="text-sm text-blue-700">
                                        {zone.leader.name} ({zone.leader.username})
                                    </p>
                                    {zone.leader_name && (
                                        <p className="text-sm text-blue-700">
                                            Nama Penuh: {zone.leader_name}
                                        </p>
                                    )}
                                    {zone.leader_contact && (
                                        <p className="text-sm text-blue-700">
                                            Telefon: {zone.leader_contact}
                                        </p>
                                    )}
                                </div>
                            )}

                            <div className="p-3 bg-yellow-50 rounded-lg">
                                <p className="text-xs text-yellow-800">
                                    <strong>Nota:</strong> Setelah ketua zon dilantik, hanya ketua zon yang boleh menambah peserta baru ke dalam zon ini.
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Save Button */}
                <Card>
                    <CardContent className="pt-6">
                        <div className="flex justify-end">
                            <Button
                                onClick={handleSubmit}
                                disabled={isSubmitting}
                                className="min-w-32"
                            >
                                {isSubmitting ? (
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                ) : (
                                    <Save className="w-4 h-4 mr-2" />
                                )}
                                {isSubmitting ? 'Menyimpan...' : 'Simpan Perubahan'}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
