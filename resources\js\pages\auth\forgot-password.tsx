// Components
import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function ForgotPassword({ status }: { status?: string }) {
    const { data, setData, post, processing, errors } = useForm<Required<{ email: string }>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('password.email'));
    };

    return (
        <AuthLayout title="Lupa Kata Laluan?" description="Masukkan email anda untuk menerima pautan reset kata laluan">
            <Head title="Lupa Kata Laluan" />

            {status && <div className="mb-6 text-center text-sm font-medium text-green-400 bg-green-400/10 border border-green-400/20 rounded-lg p-3">{status}</div>}

            <div className="space-y-4 sm:space-y-6 lg:space-y-6 xl:space-y-6">
                <form onSubmit={submit} className="space-y-3 sm:space-y-4 lg:space-y-4 xl:space-y-4">
                    <div className="space-y-1 sm:space-y-2 lg:space-y-2 xl:space-y-2">
                        <Label htmlFor="email" className="text-white font-medium text-sm sm:text-sm lg:text-base xl:text-base">Alamat Email</Label>
                        <Input
                            id="email"
                            type="email"
                            name="email"
                            autoComplete="off"
                            value={data.email}
                            autoFocus
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-12 xl:h-12 text-sm sm:text-sm lg:text-base xl:text-base"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <Button
                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2.5 sm:py-3 lg:py-3 xl:py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl mt-4 sm:mt-6 lg:mt-6 xl:mt-6 text-sm sm:text-sm lg:text-base xl:text-base"
                        disabled={processing}
                    >
                        {processing && <LoaderCircle className="h-3 w-3 sm:h-4 sm:w-4 lg:h-4 lg:w-4 xl:h-4 xl:w-4 animate-spin mr-2" />}
                        Hantar Pautan Reset
                    </Button>
                </form>

                <div className="text-center">
                    <span className="text-white/70 text-xs sm:text-sm lg:text-sm xl:text-sm">Atau, kembali ke </span>
                    <TextLink
                        href={route('login')}
                        className="text-blue-300 hover:text-blue-200 transition-colors font-medium text-xs sm:text-sm lg:text-sm xl:text-sm"
                    >
                        log masuk
                    </TextLink>
                </div>
            </div>
        </AuthLayout>
    );
}
