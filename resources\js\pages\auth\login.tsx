import { useState, FormEvent } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';
import { LoaderCircle } from 'lucide-react';
import { z } from 'zod';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/auth-context';
import toast from 'react-hot-toast';

// Zod validation schema
const loginSchema = z.object({
    username: z.string()
        .min(1, 'Username diperlukan')
        .max(50, 'Username tidak boleh melebihi 50 aksara'),
    password: z.string()
        .min(1, 'Kata laluan diperlukan'),
    remember: z.boolean().optional(),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function Login() {
    const navigate = useNavigate();
    const { login, isAuthenticated, user } = useAuth();
    const [isLoading, setIsLoading] = useState(false);
    const [formData, setFormData] = useState<LoginForm>({
        username: '',
        password: '',
        remember: false,
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    // Redirect if already authenticated
    if (isAuthenticated && user && !user.must_change_password) {
        return <Navigate to="/dashboard" replace />;
    }

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setValidationErrors({});

        try {
            // Validate with Zod
            loginSchema.parse(formData);
            setIsLoading(true);

            const response = await login(formData.username, formData.password, formData.remember);

            if (response.must_change_password) {
                toast.success('Login berjaya! Sila tukar kata laluan anda.');
                navigate('/auth/password/change');
            } else {
                toast.success('Login berjaya!');
                navigate('/dashboard');
            }
        } catch (error: any) {
            if (error instanceof z.ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.issues.forEach((err) => {
                    if (err.path[0]) {
                        fieldErrors[err.path[0] as string] = err.message;
                    }
                });
                setValidationErrors(fieldErrors);
            } else if (error.response?.data?.errors) {
                setValidationErrors(error.response.data.errors);
            } else {
                toast.error('Login gagal. Sila cuba lagi.');
            }
        } finally {
            setIsLoading(false);
        }
    };



    return (
                  <div className="space-y-4 sm:space-y-6 lg:space-y-4 xl:space-y-6">
                <form className="space-y-4 sm:space-y-6 lg:space-y-4 xl:space-y-6" onSubmit={handleSubmit}>
                    <div className="space-y-3 sm:space-y-4 lg:space-y-3 xl:space-y-4">
                        <div className="space-y-1 sm:space-y-2 lg:space-y-1 xl:space-y-2">
                            <Label htmlFor="username" className="text-white font-medium text-sm sm:text-sm lg:text-sm xl:text-base">Username</Label>
                            <Input
                                id="username"
                                type="text"
                                autoFocus
                                tabIndex={1}
                                value={formData.username}
                                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                                placeholder="Masukkan username"
                                className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                                disabled={isLoading}
                            />
                            <InputError message={validationErrors.username} />
                        </div>

                        <div className="space-y-1 sm:space-y-2 lg:space-y-1 xl:space-y-2">
                            <Label htmlFor="password" className="text-white font-medium text-sm sm:text-sm lg:text-sm xl:text-base">Kata Laluan</Label>
                            <Input
                                id="password"
                                type="password"
                                tabIndex={2}
                                value={formData.password}
                                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                                placeholder="Masukkan kata laluan"
                                className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                                disabled={isLoading}
                            />
                            <InputError message={validationErrors.password} />
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="remember"
                                checked={formData.remember}
                                onCheckedChange={(checked) => setFormData({ ...formData, remember: !!checked })}
                                className="border-white/30 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                            />
                            <Label htmlFor="remember" className="text-white/80 text-sm sm:text-sm lg:text-sm xl:text-base cursor-pointer">
                                Ingat saya
                            </Label>
                        </div>
                    </div>

                    <div className="space-y-3 sm:space-y-4 lg:space-y-3 xl:space-y-4">
                        <Button
                            type="submit"
                            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                            disabled={isLoading}
                        >
                            {isLoading && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                            {isLoading ? 'Sedang log masuk...' : 'Log Masuk'}
                        </Button>
                    </div>
                </form>
            </div>
    );
}


