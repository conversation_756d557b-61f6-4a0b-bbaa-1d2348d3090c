import { LoaderCircle } from 'lucide-react';
import { FormEventHandler, useState } from 'react';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/auth-context';

// Zod validation schema
const passwordChangeSchema = z.object({
    current_password: z.string()
        .min(1, 'Kata laluan semasa diperlukan'),
    password: z.string()
        .min(8, 'Kata laluan baru mesti sekurang-kurangnya 8 aksara')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
               'Kata laluan mesti mengandungi huruf besar, huruf kecil, nombor dan simbol'),
    password_confirmation: z.string()
        .min(1, 'Pengesahan kata laluan diperlukan')
}).refine((data) => data.password === data.password_confirmation, {
    message: "Kata laluan tidak sepadan",
    path: ["password_confirmation"],
});

type PasswordChangeForm = {
    current_password: string;
    password: string;
    password_confirmation: string;
};

export default function PasswordChange() {
    const { user, updatePassword } = useAuth();
    const navigate = useNavigate();
    const [processing, setProcessing] = useState(false);
    const [data, setData] = useState<PasswordChangeForm>({
        current_password: '',
        password: '',
        password_confirmation: '',
    });
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

    const submit: FormEventHandler = async (e) => {
        e.preventDefault();

        // Clear previous validation errors
        setValidationErrors({});

        // Validate with Zod
        try {
            passwordChangeSchema.parse(data);

            setProcessing(true);

            // If validation passes, submit the form
            await updatePassword(data.current_password, data.password);

            toast.success('Kata laluan berjaya dikemaskini!');
            navigate('/dashboard');
        } catch (error) {
            if (error instanceof z.ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.issues.forEach((err) => {
                    if (err.path[0]) {
                        fieldErrors[err.path[0] as string] = err.message;
                    }
                });
                setValidationErrors(fieldErrors);
            } else {
                toast.error('Ralat berlaku semasa mengemas kini kata laluan');
            }
        } finally {
            setProcessing(false);
        }
    };

    if (!user) {
        return null;
    }

    return (
        <>
            <div className="mb-6 text-center">
                <div className="text-yellow-400 text-sm font-medium bg-yellow-400/10 border border-yellow-400/20 rounded-lg p-4">
                    <p className="mb-2">Selamat datang, <strong>{user.name}</strong>!</p>
                    <p>Untuk keselamatan, sila tukar kata laluan anda sebelum meneruskan.</p>
                </div>
            </div>

            <form className="space-y-4 sm:space-y-6 lg:space-y-4 xl:space-y-6" onSubmit={submit}>
                <div className="space-y-3 sm:space-y-4 lg:space-y-3 xl:space-y-4">
                    <div className="space-y-1 sm:space-y-2 lg:space-y-1 xl:space-y-2">
                        <Label htmlFor="current_password" className="text-white font-medium text-sm sm:text-sm lg:text-sm xl:text-base">
                            Kata Laluan Semasa
                        </Label>
                        <Input
                            id="current_password"
                            type="password"
                            autoFocus
                            tabIndex={1}
                            value={data.current_password}
                            onChange={(e) => setData(prev => ({ ...prev, current_password: e.target.value }))}
                            placeholder="Masukkan kata laluan semasa"
                            className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                        />
                        <InputError message={validationErrors.current_password} />
                    </div>

                    <div className="space-y-1 sm:space-y-2 lg:space-y-1 xl:space-y-2">
                        <Label htmlFor="password" className="text-white font-medium text-sm sm:text-sm lg:text-sm xl:text-base">
                            Kata Laluan Baru
                        </Label>
                        <Input
                            id="password"
                            type="password"
                            tabIndex={2}
                            value={data.password}
                            onChange={(e) => setData(prev => ({ ...prev, password: e.target.value }))}
                            placeholder="Masukkan kata laluan baru"
                            className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                        />
                        <InputError message={validationErrors.password} />
                        <p className="text-xs text-white/60">
                            Kata laluan mesti mengandungi huruf besar, huruf kecil, nombor dan simbol
                        </p>
                    </div>

                    <div className="space-y-1 sm:space-y-2 lg:space-y-1 xl:space-y-2">
                        <Label htmlFor="password_confirmation" className="text-white font-medium text-sm sm:text-sm lg:text-sm xl:text-base">
                            Sahkan Kata Laluan Baru
                        </Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            tabIndex={3}
                            value={data.password_confirmation}
                            onChange={(e) => setData(prev => ({ ...prev, password_confirmation: e.target.value }))}
                            placeholder="Sahkan kata laluan baru"
                            className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 focus:ring-blue-400/20 h-10 sm:h-10 lg:h-10 xl:h-12 text-sm sm:text-sm lg:text-sm xl:text-base"
                        />
                        <InputError message={validationErrors.password_confirmation} />
                    </div>
                </div>

                <Button
                    type="submit"
                    disabled={processing}
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 sm:py-3 lg:py-2 xl:py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl text-sm sm:text-base lg:text-sm xl:text-base h-10 sm:h-11 lg:h-10 xl:h-12"
                    tabIndex={4}
                >
                    {processing ? (
                        <>
                            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                            Mengemas kini...
                        </>
                    ) : (
                        'Tukar Kata Laluan'
                    )}
                </Button>
            </form>
        </>
    );
}
