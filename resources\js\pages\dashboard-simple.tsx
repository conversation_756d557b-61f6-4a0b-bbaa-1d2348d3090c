export default function DashboardSimple() {
    return (
        <div className="min-h-screen bg-gray-100 p-4">
            <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h1 className="text-2xl font-bold text-gray-800 mb-4">
                        Simple Dashboard Test
                    </h1>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                        <div className="bg-blue-100 p-4 rounded-lg">
                            <h2 className="font-semibold text-blue-800">✅ React Working</h2>
                            <p className="text-blue-700 text-sm">Dashboard component rendered</p>
                        </div>
                        
                        <div className="bg-green-100 p-4 rounded-lg">
                            <h2 className="font-semibold text-green-800">✅ Tailwind Working</h2>
                            <p className="text-green-700 text-sm">Responsive grid layout</p>
                        </div>
                        
                        <div className="bg-purple-100 p-4 rounded-lg">
                            <h2 className="font-semibold text-purple-800">✅ No Layout</h2>
                            <p className="text-purple-700 text-sm">Bypassing dashboard layout</p>
                        </div>
                    </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-lg p-6">
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">
                        Debug Information
                    </h2>
                    
                    <div className="space-y-2 text-sm">
                        <p><strong>Route:</strong> /dashboard-simple</p>
                        <p><strong>Layout:</strong> None (direct component)</p>
                        <p><strong>Providers:</strong> None (all disabled)</p>
                        <p><strong>Hooks:</strong> None (no complex state)</p>
                    </div>
                    
                    <div className="mt-4 p-4 bg-yellow-100 rounded-lg">
                        <h3 className="font-semibold text-yellow-800">🔍 Next Steps</h3>
                        <p className="text-yellow-700 text-sm">
                            If this works, the issue is in DashboardLayout component or its dependencies.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
