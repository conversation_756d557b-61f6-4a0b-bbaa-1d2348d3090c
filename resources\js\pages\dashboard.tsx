import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/axios';
import {
    Users,
    Trophy,
    Calendar,
    BarChart3,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface DashboardStats {
    totalPlayers: number;
    totalMatches: number;
    totalSports: number;
    activeZones: number;
    upcomingMatches: number;
    completedMatches: number;
}

export default function Dashboard() {
    const { user } = useAuth();
    const [stats, setStats] = useState<DashboardStats>({
        totalPlayers: 0,
        totalMatches: 0,
        totalSports: 0,
        activeZones: 0,
        upcomingMatches: 0,
        completedMatches: 0,
    });
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchStats = async () => {
            try {
                const response = await api.get('/dashboard/stats');
                setStats(response.data);
            } catch (error) {
                // Handle error silently
            } finally {
                setIsLoading(false);
            }
        };

        fetchStats();
    }, []);



    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    // Add safety checks for stats
    const safeStats = {
        totalPlayers: stats?.totalPlayers || 0,
        totalMatches: stats?.totalMatches || 0,
        totalSports: stats?.totalSports || 0,
        activeZones: stats?.activeZones || 0,
        upcomingMatches: stats?.upcomingMatches || 0,
        completedMatches: stats?.completedMatches || 0,
    };

    // Update dashboard cards to use safe stats
    const safeDashboardCards = [
        {
            title: 'Jumlah Peserta',
            value: safeStats.totalPlayers,
            description: 'Peserta berdaftar',
            icon: Users,
            color: 'text-blue-600'
        },
        {
            title: 'Jumlah Perlawanan',
            value: safeStats.totalMatches,
            description: 'Perlawanan dijadualkan',
            icon: Trophy,
            color: 'text-green-600'
        },
        {
            title: 'Perlawanan Akan Datang',
            value: safeStats.upcomingMatches,
            description: 'Dalam minggu ini',
            icon: Calendar,
            color: 'text-yellow-600'
        },
        {
            title: 'Perlawanan Selesai',
            value: safeStats.completedMatches,
            description: 'Telah selesai',
            icon: BarChart3,
            color: 'text-purple-600'
        }
    ];

    return (
        <div className="space-y-6">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold mb-2">
                    Selamat Datang, {user?.name}!
                </h1>
                <p className="text-blue-100">
                    Portal Sukan Intra Kor Kesihatan DiRaja 2025
                </p>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {safeDashboardCards.map((card, index) => {
                    const IconComponent = card.icon;
                    return (
                        <Card key={index} className="hover:shadow-lg transition-shadow">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    {card.title}
                                </CardTitle>
                                <IconComponent className={`h-4 w-4 ${card.color}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{card.value}</div>
                                <p className="text-xs text-muted-foreground">
                                    {card.description}
                                </p>
                            </CardContent>
                        </Card>
                    );
                })}
            </div>
        </div>
    );
}
