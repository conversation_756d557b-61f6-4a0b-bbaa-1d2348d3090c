import { Calendar, Clock, MapPin, Users, Trophy, Star, Home, Coffee, Utensils } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';

export default function ChampionshipSchedule() {
    const { data: content = {}, isLoading: loading } = usePageContent('championship-schedule');

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'ATURCARA KEJOHANAN';
    const heroSubtitle = content?.hero?.subtitle || 'Jadual lengkap aktiviti dan acara Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Lihat jadual lengkap semua aktiviti, upacara, dan acara sepanjang kejohanan dari 21 hingga 25 Julai 2025.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    // Complete championship schedule data based on official document
    const scheduleData = [
        {
            date: '21 Julai 2025',
            day: 'Isnin',
            events: [
                {
                    time: '08:00 - 12:00',
                    title: '<PERSON>apor dan <PERSON>n',
                    location: 'Wisma Perwira, Wisma Bintara, Annex RKAT, Blok Bujang MPSTU',
                    type: 'registration',
                    description: 'Pendaftaran dan check-in ke penginapan'
                },
                {
                    time: '12:00 - 14:00',
                    title: 'Makan Tengahari dan Pentadbiran',
                    location: 'Dewan Makan',
                    type: 'break',
                    description: 'Rehat tengahari dan urusan pentadbiran'
                },
                {
                    time: '14:00 - 17:00',
                    title: 'Raptai Upacara Perasmian dan Penutup',
                    location: 'Stadium PU Sendayan',
                    type: 'ceremony',
                    description: 'Latihan upacara perasmian dan penutup'
                },
                {
                    time: '20:00 - 21:00',
                    title: 'Upacara Perasmian Sukan Intra Kor KKD 2025',
                    location: 'Stadium PU Sendayan',
                    type: 'ceremony',
                    description: 'Upacara perasmian rasmi kejohanan'
                },
                {
                    time: '21:00 - 21:30',
                    title: 'Lari Berganti-ganti 10 x 400 meter',
                    location: 'Stadium PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan lari berganti-ganti'
                }
            ]
        },
        {
            date: '22 Julai 2025',
            day: 'Selasa',
            events: [
                {
                    time: '07:30 - 08:30',
                    title: 'Walkathon Wanita KKD',
                    location: 'Kawasan Walkathon',
                    type: 'match',
                    description: 'Walkathon khusus wanita KKD'
                },
                {
                    time: '09:00 - 16:30',
                    title: 'Bola Tampar (Lelaki)',
                    location: 'Kompleks Sukan PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan bola tampar lelaki'
                },
                {
                    time: '09:00 - 16:30',
                    title: 'Bola Jaring',
                    location: 'Kompleks Sukan PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan bola jaring'
                },
                {
                    time: '09:00 - 17:00',
                    title: 'Ping Pong',
                    location: 'Dewan Angkasaraya',
                    type: 'match',
                    description: 'Pertandingan ping pong'
                },
                {
                    time: '20:00 - 21:45',
                    title: 'Bola Sepak Perlawanan 2',
                    location: 'Stadium PU Sendayan',
                    type: 'match',
                    description: 'Perlawanan bola sepak kedua'
                },
                {
                    time: '08:00 - 09:45',
                    title: 'Bola Sepak Perlawanan 3',
                    location: 'Dewan Angkasaraya',
                    type: 'match',
                    description: 'Perlawanan bola sepak ketiga'
                }
            ]
        },
        {
            date: '23 Julai 2025',
            day: 'Rabu',
            events: [
                {
                    time: '09:00 - 17:00',
                    title: 'Badminton Zon',
                    location: 'Dewan Angkasaraya',
                    type: 'match',
                    description: 'Pertandingan badminton zon'
                },
                {
                    time: '09:00 - 17:00',
                    title: 'Badminton Serikandi',
                    location: 'Dewan Angkasaraya',
                    type: 'match',
                    description: 'Pertandingan badminton serikandi'
                },
                {
                    time: '09:00 - 16:00',
                    title: 'Bola Tampar (Wanita)',
                    location: 'Kompleks Sukan PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan bola tampar wanita'
                },
                {
                    time: '20:00 - 22:00',
                    title: 'Malam Akrab',
                    location: 'Dewan Bankuet Wisma Perwira',
                    type: 'social',
                    description: 'Malam akrab dan hiburan'
                }
            ]
        },
        {
            date: '24 Julai 2025',
            day: 'Khamis',
            events: [
                {
                    time: '08:00 - 10:00',
                    title: 'Bola Sepak Perlawanan Akhir',
                    location: 'Stadium PU Sendayan',
                    type: 'match',
                    description: 'Perlawanan akhir bola sepak'
                },
                {
                    time: '15:30 - 16:00',
                    title: 'Sukan Rakyat BAKAT',
                    location: 'Stadium PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan sukan rakyat BAKAT'
                },
                {
                    time: '16:00 - 18:30',
                    title: 'Acara Penutup',
                    location: 'Stadium PU Sendayan',
                    type: 'ceremony',
                    description: 'Upacara penutup dan penyampaian hadiah'
                }
            ]
        },
        {
            date: '25 Julai 2025',
            day: 'Jumaat',
            events: [
                {
                    time: '08:00 - 12:00',
                    title: 'Pembubaran dan Daftar Keluar Penginapan',
                    location: 'Penginapan Masing-masing',
                    type: 'registration',
                    description: 'Check-out dan pembubaran'
                },
                {
                    time: '08:30 - 12:00',
                    title: 'Golf Tertutup KKD',
                    location: 'Kelab Rekreasi AT PU Sendayan',
                    type: 'match',
                    description: 'Pertandingan golf tertutup KKD'
                }
            ]
        }
    ];

    // Venue information
    const venues = [
        {
            name: 'Stadium PU Sendayan',
            sports: 'Bola Sepak, Lari Berganti-ganti, Upacara',
            icon: <Trophy className="w-6 h-6 text-yellow-400" />
        },
        {
            name: 'Kompleks Sukan PU Sendayan',
            sports: 'Bola Tampar, Bola Jaring',
            icon: <Users className="w-6 h-6 text-blue-400" />
        },
        {
            name: 'Dewan Angkasaraya',
            sports: 'Ping Pong, Badminton, Bola Sepak (1 perlawanan)',
            icon: <Star className="w-6 h-6 text-green-400" />
        },
        {
            name: 'Kelab Rekreasi AT PU Sendayan',
            sports: 'Golf Tertutup',
            icon: <MapPin className="w-6 h-6 text-purple-400" />
        }
    ];

    // Accommodation information
    const accommodations = [
        {
            name: 'Wisma Perwira',
            for: 'Pegawai Kanan',
            icon: <Home className="w-5 h-5 text-yellow-400" />
        },
        {
            name: 'Wisma Bintara',
            for: 'Pegawai Rendah & Bintara',
            icon: <Home className="w-5 h-5 text-blue-400" />
        },
        {
            name: 'Annex RKAT',
            for: 'Anggota Tambahan',
            icon: <Home className="w-5 h-5 text-green-400" />
        },
        {
            name: 'Blok Bujang MPSTU',
            for: 'Anggota Bujang',
            icon: <Home className="w-5 h-5 text-purple-400" />
        }
    ];

    // Sports list
    const sportsList = [
        'Lari Berganti-ganti 10 x 400m',
        'Bola Sepak (3 Perlawanan)',
        'Bola Tampar (Lelaki & Wanita)',
        'Bola Jaring',
        'Ping Pong',
        'Badminton (Zon & Serikandi)',
        'Sukan Rakyat BAKAT',
        'Golf Tertutup KKD'
    ];

    // Important notes
    const importantNotes = [
        'Daftar masuk penginapan: 21 Julai (0800-1200)',
        'Daftar keluar penginapan: 25 Julai (0800-1200)',
        'Tempoh kejohanan: 5 hari 4 malam',
        'Walkathon khusus wanita KKD: 23 Julai pagi',
        'Malam akrab wajib: 23 Julai malam',
        'Golf tertutup KKD sahaja: 25 Julai pagi'
    ];

    const getEventIcon = (type: string) => {
        switch (type) {
            case 'ceremony':
                return <Trophy className="w-5 h-5 text-yellow-400" />;
            case 'match':
                return <Users className="w-5 h-5 text-blue-400" />;
            case 'registration':
                return <Home className="w-5 h-5 text-green-400" />;
            case 'break':
                return <Utensils className="w-5 h-5 text-orange-400" />;
            case 'social':
                return <Star className="w-5 h-5 text-purple-400" />;
            default:
                return <Calendar className="w-5 h-5 text-gray-400" />;
        }
    };

    const getEventColor = (type: string) => {
        switch (type) {
            case 'ceremony':
                return 'bg-yellow-500/20 border-yellow-500/30';
            case 'match':
                return 'bg-blue-500/20 border-blue-500/30';
            case 'registration':
                return 'bg-green-500/20 border-green-500/30';
            case 'break':
                return 'bg-orange-500/20 border-orange-500/30';
            case 'social':
                return 'bg-purple-500/20 border-purple-500/30';
            default:
                return 'bg-gray-500/20 border-gray-500/30';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-white text-xl">Loading...</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-14 sm:h-14 bg-blue-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Calendar className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-7 sm:h-7 text-blue-400" />
                        </div>
                        <h1 className="text-4xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-3 drop-shadow-lg">
                            <span className="text-blue-400">{heroTitle}</span>
                        </h1>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-white/90 mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-3 leading-relaxed 2xl:max-w-4xl xl:max-w-4xl lg:max-w-3xl md:max-w-3xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroSubtitle}
                        </p>
                        <p className="text-base 2xl:text-lg xl:text-base lg:text-xs md:text-sm sm:text-xs text-white/80 mb-6 2xl:mb-10 xl:mb-9 lg:mb-6 md:mb-7 sm:mb-4 leading-relaxed 2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroDescription}
                        </p>
                    </div>
                </div>
            </section>

            {/* Schedule Section */}
            <section className="py-16 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-6 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-4 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-4">
                        <h2 className="text-4xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-2">
                            <span className="text-blue-400">JADUAL</span> LENGKAP
                        </h2>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-3xl mx-auto px-4">
                            Aturcara harian kejohanan dari 21-25 Julai 2025
                        </p>
                    </div>

                    <div className="space-y-8 2xl:space-y-12 xl:space-y-10 lg:space-y-6 md:space-y-8 sm:space-y-4">
                        {scheduleData.map((day, dayIndex) => (
                            <div key={dayIndex} className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-blue-400/30 overflow-hidden shadow-2xl">
                                <div className="bg-gradient-to-r from-blue-600/80 to-purple-600/80 backdrop-blur-sm p-6 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-4 border-b border-blue-400/30">
                                    <div className="flex items-center space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-3 md:space-x-4 sm:space-x-3">
                                        <Calendar className="w-8 h-8 2xl:w-12 2xl:h-12 xl:w-10 xl:h-10 lg:w-7 lg:h-7 md:w-8 md:h-8 sm:w-6 sm:h-6 text-blue-400" />
                                        <div>
                                            <h3 className="text-2xl 2xl:text-4xl xl:text-3xl lg:text-xl md:text-2xl sm:text-lg font-black text-white">
                                                {day.date}
                                            </h3>
                                            <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm text-white/80">
                                                {day.day}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="p-6 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-4">
                                    <div className="space-y-4 2xl:space-y-6 xl:space-y-5 lg:space-y-3 md:space-y-4 sm:space-y-3">
                                        {day.events.map((event, eventIndex) => (
                                            <div key={eventIndex} className={`flex items-start space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-3 md:space-x-4 sm:space-x-3 p-4 2xl:p-6 xl:p-5 lg:p-3 md:p-4 sm:p-3 ${getEventColor(event.type)} backdrop-blur-sm rounded-xl 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-lg sm:rounded-lg border hover:border-opacity-50 transition-all duration-300`}>
                                                <div className="flex items-center justify-center w-12 h-12 2xl:w-16 2xl:h-16 xl:w-14 xl:h-14 lg:w-10 lg:h-10 md:w-12 md:h-12 sm:w-8 sm:h-8 bg-white/10 rounded-full flex-shrink-0">
                                                    {getEventIcon(event.type)}
                                                </div>
                                                <div className="flex-1">
                                                    <div className="flex items-start justify-between flex-col sm:flex-col lg:flex-row gap-2 sm:gap-2 lg:gap-4">
                                                        <div className="flex-1">
                                                            <div className="flex items-center space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-2 mb-2">
                                                                <Clock className="w-4 h-4 2xl:w-5 2xl:h-5 xl:w-4 xl:h-4 lg:w-3 lg:h-3 md:w-4 md:h-4 sm:w-3 sm:h-3 text-white/70" />
                                                                <span className="text-sm 2xl:text-lg xl:text-base lg:text-xs md:text-sm sm:text-xs font-bold text-white/90">{event.time}</span>
                                                            </div>
                                                            <h4 className="text-lg 2xl:text-2xl xl:text-xl lg:text-base md:text-lg sm:text-sm font-bold text-white mb-1 2xl:mb-2 xl:mb-1 lg:mb-1 md:mb-1 sm:mb-1">
                                                                {event.title}
                                                            </h4>
                                                            <p className="text-sm 2xl:text-lg xl:text-base lg:text-xs md:text-sm sm:text-xs text-white/80 mb-2">
                                                                {event.description}
                                                            </p>
                                                        </div>
                                                        <div className="flex items-center space-x-2 2xl:space-x-3 xl:space-x-2 lg:space-x-1 md:space-x-2 sm:space-x-1 text-white/70">
                                                            <MapPin className="w-4 h-4 2xl:w-5 2xl:h-5 xl:w-4 xl:h-4 lg:w-3 lg:h-3 md:w-4 md:h-4 sm:w-3 sm:h-3" />
                                                            <span className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-medium">{event.location}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Venues Section */}
            <section className="py-16 2xl:py-24 xl:py-20 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-6">
                        <h2 className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                            <span className="text-blue-400">LOKASI</span> VENUE
                        </h2>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-2xl mx-auto">
                            Venue utama untuk semua aktiviti kejohanan
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 2xl:gap-8 xl:gap-7 lg:gap-5 md:gap-6 sm:gap-4">
                        {venues.map((venue, index) => (
                            <div key={index} className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-2xl sm:rounded-lg border-2 border-blue-400/30 p-6 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-4 hover:border-blue-400/50 transition-all duration-300">
                                <div className="flex items-start space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-3 md:space-x-4 sm:space-x-3">
                                    <div className="flex items-center justify-center w-12 h-12 2xl:w-16 2xl:h-16 xl:w-14 xl:h-14 lg:w-10 lg:h-10 md:w-12 md:h-12 sm:w-8 sm:h-8 bg-blue-500/20 rounded-full flex-shrink-0">
                                        {venue.icon}
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-xl sm:text-base font-bold text-white mb-2 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1">
                                            {venue.name}
                                        </h3>
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs text-gray-300">
                                            {venue.sports}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Accommodations Section */}
            <section className="py-16 2xl:py-24 xl:py-20 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-6">
                        <h2 className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                            <span className="text-green-400">PENGINAPAN</span> PESERTA
                        </h2>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-2xl mx-auto">
                            Kemudahan penginapan untuk semua peserta
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 2xl:gap-8 xl:gap-7 lg:gap-5 md:gap-6 sm:gap-4">
                        {accommodations.map((accommodation, index) => (
                            <div key={index} className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-2xl sm:rounded-lg border-2 border-green-400/30 p-6 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-4 hover:border-green-400/50 transition-all duration-300">
                                <div className="flex items-start space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-3 md:space-x-4 sm:space-x-3">
                                    <div className="flex items-center justify-center w-12 h-12 2xl:w-16 2xl:h-16 xl:w-14 xl:h-14 lg:w-10 lg:h-10 md:w-12 md:h-12 sm:w-8 sm:h-8 bg-green-500/20 rounded-full flex-shrink-0">
                                        {accommodation.icon}
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-xl sm:text-base font-bold text-white mb-2 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1">
                                            {accommodation.name}
                                        </h3>
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs text-gray-300">
                                            {accommodation.for}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Sports List Section */}
            <section className="py-16 2xl:py-24 xl:py-20 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-6">
                        <h2 className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                            <span className="text-yellow-400">SUKAN</span> DIPERTANDINGKAN
                        </h2>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-2xl mx-auto">
                            8 kategori sukan yang akan dipertandingkan
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 2xl:gap-6 xl:gap-5 lg:gap-3 md:gap-4 sm:gap-3">
                        {sportsList.map((sport, index) => (
                            <div key={index} className="bg-black/60 backdrop-blur-xl rounded-xl 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-xl sm:rounded-lg border-2 border-yellow-400/30 p-4 2xl:p-6 xl:p-5 lg:p-3 md:p-4 sm:p-3 hover:border-yellow-400/50 transition-all duration-300 text-center">
                                <div className="flex items-center justify-center w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-10 md:h-10 sm:w-8 sm:h-8 bg-yellow-500/20 rounded-full mx-auto mb-3 2xl:mb-4 xl:mb-3 lg:mb-2 md:mb-3 sm:mb-2">
                                    <Trophy className="w-5 h-5 2xl:w-6 2xl:h-6 xl:w-5 xl:h-5 lg:w-4 lg:h-4 md:w-5 md:h-5 sm:w-4 sm:h-4 text-yellow-400" />
                                </div>
                                <h3 className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs font-bold text-white">
                                    {sport}
                                </h3>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Important Notes Section */}
            <section className="py-16 2xl:py-24 xl:py-20 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-6">
                        <h2 className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                            <span className="text-red-400">NOTA</span> PENTING
                        </h2>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-2xl mx-auto">
                            Maklumat penting yang perlu diketahui semua peserta
                        </p>
                    </div>

                    <div className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-2xl sm:rounded-lg border-2 border-red-400/30 p-8 2xl:p-10 xl:p-9 lg:p-6 md:p-8 sm:p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 2xl:gap-8 xl:gap-7 lg:gap-4 md:gap-6 sm:gap-4">
                            {importantNotes.map((note, index) => (
                                <div key={index} className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-2">
                                    <div className="flex items-center justify-center w-6 h-6 2xl:w-8 2xl:h-8 xl:w-7 xl:h-7 lg:w-5 lg:h-5 md:w-6 md:h-6 sm:w-5 sm:h-5 bg-red-500/20 rounded-full flex-shrink-0 mt-1">
                                        <Star className="w-3 h-3 2xl:w-4 2xl:h-4 xl:w-3 xl:h-3 lg:w-2 lg:h-2 md:w-3 md:h-3 sm:w-2 sm:h-2 text-red-400" />
                                    </div>
                                    <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs text-white leading-relaxed">
                                        {note}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Map Section */}
            <section className="py-16 2xl:py-24 xl:py-20 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-6">
                        <h2 className="text-4xl 2xl:text-6xl xl:text-5xl lg:text-3xl md:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                            <span className="text-purple-400">PETA</span> LOKASI
                        </h2>
                        <p className="text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-2xl mx-auto">
                            Peta kawasan kejohanan dan lokasi venue utama
                        </p>
                    </div>

                    <div className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-xl md:rounded-2xl sm:rounded-lg border-2 border-purple-400/30 overflow-hidden">
                        <div className="p-6 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-4 border-b border-purple-400/30">
                            <div className="flex items-center space-x-4 2xl:space-x-6 xl:space-x-5 lg:space-x-3 md:space-x-4 sm:space-x-3">
                                <div className="flex items-center justify-center w-12 h-12 2xl:w-16 2xl:h-16 xl:w-14 xl:h-14 lg:w-10 lg:h-10 md:w-12 md:h-12 sm:w-8 sm:h-8 bg-purple-500/20 rounded-full">
                                    <MapPin className="w-6 h-6 2xl:w-8 2xl:h-8 xl:w-7 xl:h-7 lg:w-5 lg:h-5 md:w-6 md:h-6 sm:w-4 sm:h-4 text-purple-400" />
                                </div>
                                <div>
                                    <h3 className="text-xl 2xl:text-2xl xl:text-xl lg:text-lg md:text-xl sm:text-base font-bold text-white">
                                        Kawasan Kejohanan
                                    </h3>
                                    <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs text-gray-300">
                                        PU Sendayan, Negeri Sembilan
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="relative">
                            <img
                                src="/images/map.jpg"
                                alt="Peta Lokasi Kejohanan"
                                className="w-full h-80 2xl:h-[600px] xl:h-[500px] lg:h-64 md:h-80 sm:h-60 object-contain bg-gray-900"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    const parent = target.parentElement;
                                    if (parent) {
                                        parent.innerHTML = `
                                            <div class="w-full h-80 2xl:h-[600px] xl:h-[500px] lg:h-64 md:h-80 sm:h-60 bg-gray-800 flex items-center justify-center">
                                                <div class="text-center">
                                                    <div class="w-16 h-16 2xl:w-20 2xl:h-20 xl:w-18 xl:h-18 lg:w-12 lg:h-12 md:w-16 md:h-16 sm:w-12 sm:h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                                        <svg class="w-8 h-8 2xl:w-10 2xl:h-10 xl:w-9 xl:h-9 lg:w-6 lg:h-6 md:w-8 md:h-8 sm:w-6 sm:h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                    </div>
                                                    <p class="text-white text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-sm font-medium">Peta Lokasi</p>
                                                    <p class="text-gray-400 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs">Akan dikemaskini</p>
                                                </div>
                                            </div>
                                        `;
                                    }
                                }}
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent pointer-events-none"></div>
                            <div className="absolute bottom-4 left-4 right-4 pointer-events-none">
                                <div className="bg-black/90 backdrop-blur-sm rounded-lg p-4 2xl:p-6 xl:p-5 lg:p-3 md:p-4 sm:p-3">
                                    <h4 className="text-lg 2xl:text-xl xl:text-lg lg:text-base md:text-lg sm:text-sm font-bold text-white mb-2">
                                        Lokasi Utama
                                    </h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-sm sm:text-xs text-gray-300">
                                        <div>• Stadium PU Sendayan</div>
                                        <div>• Kompleks Sukan PU Sendayan</div>
                                        <div>• Dewan Angkasaraya</div>
                                        <div>• Kelab Rekreasi AT PU Sendayan</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}
