import { useEffect, useState } from 'react';
import { usePageContent } from '@/hooks/usePageContent';
import { useSettings } from '@/hooks/useSettings';
import JadualPertandinganPublic from '@/components/public/jadual-pertandingan-public';

export default function JadualPertandingan() {
    const { settings } = useSettings();
    const { pageContent } = usePageContent('homepage');
    const [backgroundImage, setBackgroundImage] = useState<string>('');

    useEffect(() => {
        // Set background image from homepage content
        if (pageContent?.hero_background_image) {
            setBackgroundImage(pageContent.hero_background_image);
        }
    }, [pageContent]);

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section 
                className="relative bg-cover bg-center bg-no-repeat py-20"
                style={{
                    backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                }}
            >
                <div className="absolute inset-0 bg-black/50"></div>
                <div className="relative z-10 container mx-auto px-4 text-center text-white">
                    <h1 className="text-4xl md:text-6xl font-bold mb-4">
                        Jadual Pertandingan
                    </h1>
                    <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                        Jadual lengkap pertandingan Sukan Intra Kor Kesihatan DiRaja 2025
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                            <div className="text-sm opacity-90">Tarikh Pertandingan</div>
                            <div className="text-lg font-semibold">22 - 24 Julai 2025</div>
                        </div>
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                            <div className="text-sm opacity-90">Venue Utama</div>
                            <div className="text-lg font-semibold">Stadium PU Sendayan</div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <section className="py-16 bg-gray-50">
                <div className="container mx-auto px-4">
                    <JadualPertandinganPublic />
                </div>
            </section>

            {/* Additional Info Section */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8">
                            Maklumat Penting
                        </h2>
                        
                        <div className="grid gap-8 md:grid-cols-3">
                            <div className="bg-blue-50 rounded-lg p-6">
                                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Masa Pertandingan
                                </h3>
                                <p className="text-gray-600">
                                    Pertandingan bermula pada 8:00 pagi dan berakhir pada 6:00 petang setiap hari.
                                </p>
                            </div>

                            <div className="bg-green-50 rounded-lg p-6">
                                <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Lokasi Venue
                                </h3>
                                <p className="text-gray-600">
                                    Stadium PU Sendayan, Dewan Angkasaraya, dan Kompleks Sukan PU Sendayan.
                                </p>
                            </div>

                            <div className="bg-purple-50 rounded-lg p-6">
                                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Maklumat Lanjut
                                </h3>
                                <p className="text-gray-600">
                                    Untuk maklumat lanjut, sila hubungi pihak penganjur atau rujuk laman web rasmi.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Live Updates Section */}
            <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">
                        Kemaskini Langsung
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto">
                        Dapatkan kemaskini terkini mengenai keputusan pertandingan dan jadual yang dikemaskini.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-4">
                            <div className="text-sm opacity-90">Status Sistem</div>
                            <div className="text-lg font-semibold flex items-center justify-center gap-2">
                                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                Aktif
                            </div>
                        </div>
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-4">
                            <div className="text-sm opacity-90">Kemaskini Terakhir</div>
                            <div className="text-lg font-semibold">
                                {new Date().toLocaleString('ms-MY')}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}
