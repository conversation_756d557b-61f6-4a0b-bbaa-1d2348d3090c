import { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, Users, Trophy, Target, Filter, ChevronDown } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';
import OptimizedImage from '@/components/OptimizedImage';
import SportCard from '@/components/SportCard';
import { SportLogo, ZoneLogo } from '@/components/ui/logo-components';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import publicApi from '@/lib/public-api';

// Interface for database matches
interface SportMatch {
    id: number;
    sport_id: number;
    bil: number;
    title: string;
    match_type: string;
    zone_a: string;
    zone_b: string;
    special_teams: string;
    match_date: string;
    match_time: string;
    end_time: string;
    venue: string;
    court_field: string;
    category: string;
    gender_type: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    is_highlighted: boolean;
    highlight_color: string;
    bracket_round: string;
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    sports_schedule: {
        id: number;
        schedule_name: string;
    };
}



// Function to check if match is a final
const isFinalMatch = (match: SportMatch) => {
    const title = match.title?.toLowerCase() || '';
    const matchType = match.match_type?.toLowerCase() || '';
    const bracketRound = match.bracket_round?.toLowerCase() || '';

    return title.includes('final') ||
           matchType.includes('final') ||
           bracketRound.includes('final') ||
           title.includes('tempat ke 3') ||
           matchType.includes('tempat ke 3');
};

// Component for final match placeholder
const FinalPlaceholder = ({ className, label }: { className: string; label: 'X' | 'Y' }) => (
    <div className={`${className} bg-gradient-to-br from-gray-700/60 to-gray-800/60 rounded-full border-2 border-dashed border-yellow-400/50 flex items-center justify-center relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-full"></div>
        <div className="text-center relative z-10">
            <div className="text-yellow-400 font-bold text-2xl sm:text-3xl md:text-4xl lg:text-3xl xl:text-5xl 2xl:text-6xl">{label}</div>
        </div>
    </div>
);

const getStatusColor = (status: string) => {
    switch (status) {
        case 'completed':
            return 'bg-green-500/20 text-green-300 border-green-500/30';
        case 'ongoing':
            return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
        case 'scheduled':
        case 'upcoming':
            return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
        case 'cancelled':
            return 'bg-red-500/20 text-red-300 border-red-500/30';
        default:
            return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case 'completed':
            return 'Selesai';
        case 'ongoing':
            return 'Sedang Berlangsung';
        case 'scheduled':
        case 'upcoming':
            return 'Akan Datang';
        case 'cancelled':
            return 'Dibatalkan';
        default:
            return status;
    }
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
};

export default function MatchSchedule() {
    // State for matches data
    const [matches, setMatches] = useState<SportMatch[]>([]);
    const [filteredMatches, setFilteredMatches] = useState<SportMatch[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Filter states
    const [selectedDate, setSelectedDate] = useState<string>('all');
    const [selectedSport, setSelectedSport] = useState<string>('all');
    const [availableDates, setAvailableDates] = useState<{value: string, label: string}[]>([]);
    const [availableSports, setAvailableSports] = useState<any[]>([]);

    // Get page content from CMS
    const { data: pageContent = {}, isLoading: contentLoading } = usePageContent('match-schedule');

    // Fetch matches data from API
    useEffect(() => {
        const fetchMatches = async () => {
            try {
                setIsLoading(true);




                const response = await publicApi.get('/public/jadual/matches');






                const matchesData = response.data.data || [];
                setMatches(matchesData);
                setFilteredMatches(matchesData);

                // Extract unique dates and sports for filters
                const dates = [...new Set(matchesData.map(match => match.match_date))].sort();
                const formattedDates = dates.map(date => {
                    const dateObj = new Date(date);
                    const day = dateObj.getDate();
                    const month = dateObj.toLocaleDateString('ms-MY', { month: 'long' });
                    const year = dateObj.getFullYear();
                    return {
                        value: date,
                        label: `${day} ${month} ${year}`
                    };
                });

                // Extract unique sports properly
                const sportsMap = new Map();
                matchesData.forEach(match => {
                    if (match.sport?.id && match.sport?.name) {
                        sportsMap.set(match.sport.id, {
                            id: match.sport.id,
                            name: match.sport.name,
                            logo_url: match.sport.logo_url
                        });
                    }
                });
                const sports = Array.from(sportsMap.values());

                setAvailableDates(formattedDates);
                setAvailableSports(sports);

            } catch (error) {






            } finally {
                setIsLoading(false);

            }
        };

        fetchMatches();
    }, []);

    // Filter matches based on selected date and sport
    useEffect(() => {
        let filtered = matches;

        if (selectedDate !== 'all') {
            filtered = filtered.filter(match => match.match_date === selectedDate);
        }

        if (selectedSport !== 'all') {
            filtered = filtered.filter(match => match.sport?.name === selectedSport);
        }

        setFilteredMatches(filtered);
    }, [matches, selectedDate, selectedSport]);

    // Group matches by sport
    const groupedMatches = filteredMatches.reduce((groups, match) => {
        const sportName = match.sport?.name || 'Unknown Sport';
        if (!groups[sportName]) {
            groups[sportName] = {
                sport: match.sport,
                matches: []
            };
        }
        groups[sportName].matches.push(match);
        return groups;
    }, {} as Record<string, { sport: any; matches: SportMatch[] }>);

    // Get content from CMS or use defaults
    const heroContent = pageContent.hero || {};
    const baseBackgroundImage = heroContent.background_image || "/images/default.jpg";
    const title = heroContent.title || "JADUAL PERLAWANAN";
    // Use background image directly
    const backgroundImage = baseBackgroundImage;
    const subtitle = heroContent.subtitle || "Jadual lengkap semua perlawanan Sukan Intra Kor Kesihatan DiRaja 2025";
    const description = heroContent.description || "21 - 25 Julai 2025 | PU Sendayan";

    // Calculate stats from database data
    const totalMatches = matches.length;
    const upcomingMatches = matches.filter(match => match.status === 'scheduled').length;
    const totalSports = [...new Set(matches.map(match => match.sport?.name))].filter(Boolean).length;
    const totalVenues = [...new Set(matches.map(match => match.venue))].filter(Boolean).length;

    if (contentLoading || isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${backgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-16 sm:h-16 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Calendar className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-8 sm:h-8 text-yellow-400" />
                        </div>
                        <h1 className="text-5xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-4xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4 drop-shadow-lg">
                            <span className="text-yellow-400">{title.split(' ')[0] || 'JADUAL'}</span> {title.split(' ').slice(1).join(' ') || 'PERLAWANAN'}
                        </h1>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-base md:text-base sm:text-sm text-white/90 mb-12 2xl:mb-16 xl:mb-14 lg:mb-8 md:mb-10 sm:mb-8 leading-relaxed max-w-3xl mx-auto">
                            {subtitle}
                            <br className="hidden md:block" />
                            <span className="text-yellow-300 font-semibold">{description}</span>
                        </p>

                     
                    </div>
                </div>
            </section>

            {/* Match Schedule Section */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-12 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="text-center mb-16 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-8">
                        <h2 className="text-5xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-3xl font-black text-white mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <span className="text-yellow-400">JADUAL</span> PERLAWANAN
                        </h2>
                        <p className="text-xl 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-sm text-gray-300 max-w-3xl mx-auto mb-8">
                            {filteredMatches.length} daripada {totalMatches} perlawanan dijadualkan
                        </p>

                        {/* Filters */}
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-2xl mx-auto">
                            <div className="flex items-center gap-2">
                                <Filter className="w-5 h-5 text-yellow-400" />
                                <span className="text-white font-semibold">Filter:</span>
                            </div>

                            <Select value={selectedDate} onValueChange={setSelectedDate}>
                                <SelectTrigger className="w-48 bg-white/10 border-white/20 text-white">
                                    <SelectValue placeholder="Pilih Tarikh" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Tarikh</SelectItem>
                                    {availableDates.map(date => (
                                        <SelectItem key={date.value} value={date.value}>
                                            {date.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={selectedSport} onValueChange={setSelectedSport}>
                                <SelectTrigger className="w-48 bg-white/10 border-white/20 text-white">
                                    <SelectValue placeholder="Pilih Sukan" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Sukan</SelectItem>
                                    {availableSports.map(sport => (
                                        <SelectItem key={sport.id} value={sport.name}>
                                            {sport.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Match Cards Grouped by Sport */}
                    <div className="space-y-12">
                        {Object.keys(groupedMatches).length === 0 && (
                            <div className="text-center py-12">
                                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                                    <div className="text-yellow-400 text-6xl mb-4">⚠️</div>
                                    <p className="text-white text-xl mb-2">Tiada perlawanan dijadualkan buat masa ini.</p>
                                    <p className="text-gray-300 text-sm">
                                        Sila semak semula atau hubungi pentadbir sistem.
                                    </p>
                                </div>
                            </div>
                        )}

                        {Object.entries(groupedMatches).map(([sportName, { sport, matches: sportMatches }]) => (
                            <div key={sportName} className="space-y-6">
                                {/* Sport Header */}
                                <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-lg sm:rounded-xl flex items-center justify-center">
                                        <SportLogo
                                            sportName={sport?.name || ''}
                                            className="w-8 h-8 sm:w-10 sm:h-10 object-contain"
                                        />
                                    </div>
                                    <div>
                                        <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-white">{sportName}</h3>
                                        <p className="text-sm sm:text-base text-gray-400">{sportMatches.length} perlawanan</p>
                                    </div>
                                </div>

                                {/* Matches Grid for this sport */}
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 md:gap-6 lg:gap-6 xl:gap-8 2xl:gap-10">
                                    {sportMatches.map(match => (
                            <div key={match.id} className="bg-white/5 backdrop-blur-sm rounded-lg sm:rounded-xl md:rounded-xl lg:rounded-xl xl:rounded-2xl 2xl:rounded-3xl p-3 sm:p-4 md:p-4 lg:p-4 xl:p-5 2xl:p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10">
                                {/* Sport Header */}
                                <div className="flex items-center justify-between mb-2 sm:mb-3 md:mb-3 lg:mb-3 xl:mb-4 2xl:mb-4">
                                    <div className="flex items-center space-x-2 sm:space-x-2 md:space-x-3 lg:space-x-3 xl:space-x-4 2xl:space-x-5">
                                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-12 lg:h-12 xl:w-18 xl:h-18 2xl:w-20 2xl:h-20 rounded-lg sm:rounded-lg md:rounded-lg lg:rounded-lg xl:rounded-xl 2xl:rounded-2xl bg-gradient-to-br from-yellow-400/20 to-orange-500/20 flex items-center justify-center overflow-hidden">
                                            <SportLogo
                                                sportName={match.sport?.name || ''}
                                                className="w-6 h-6 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-8 lg:h-8 xl:w-11 xl:h-11 2xl:w-12 2xl:h-12 object-contain"
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="text-sm sm:text-sm md:text-base lg:text-base xl:text-lg 2xl:text-xl font-bold text-white leading-tight">
                                                {match.event_type === 'group_event' ? (
                                                    <span>
                                                        <span className="block">{match.sport?.name}</span>
                                                        <span className="block text-xs sm:text-xs md:text-xs lg:text-xs xl:text-sm 2xl:text-base text-yellow-400 font-medium">
                                                            {match.title?.replace(match.sport?.name || '', '').trim()}
                                                        </span>
                                                    </span>
                                                ) : (
                                                    match.sport?.name || match.title
                                                )}
                                            </h3>
                                            <p className="text-xs sm:text-xs md:text-xs lg:text-xs xl:text-sm 2xl:text-base text-gray-400 mt-1">{match.category || match.gender_type}</p>
                                        </div>
                                    </div>
                                    <div className={`px-2 py-1 sm:px-2 sm:py-1 md:px-3 md:py-2 lg:px-3 lg:py-1 xl:px-4 xl:py-2 2xl:px-5 2xl:py-3 rounded-full border text-xs sm:text-xs md:text-xs lg:text-xs xl:text-sm 2xl:text-base font-semibold ${getStatusColor(match.status)}`}>
                                        {getStatusText(match.status)}
                                    </div>
                                </div>

                                {/* Teams */}
                                {match.event_type === 'group_event' && match.participating_zones ? (
                                    // Group Event - Show all participating zones
                                    <div className="mb-4 sm:mb-4 md:mb-5 lg:mb-4 xl:mb-7 2xl:mb-8">
                                        <div className="text-center mb-3 sm:mb-4">
                                            <span className="text-sm sm:text-sm md:text-base lg:text-base xl:text-lg 2xl:text-xl font-semibold text-yellow-400">Penyertaan Semua Zon</span>
                                        </div>
                                        <div className="flex items-center justify-center space-x-2 sm:space-x-3 md:space-x-4 lg:space-x-3 xl:space-x-5 2xl:space-x-6">
                                            {match.participating_zones.map((zone, index) => (
                                                <div key={zone} className="text-center">
                                                    <div className="mx-auto mb-1 sm:mb-1 md:mb-2 lg:mb-1 xl:mb-2 2xl:mb-3 flex items-center justify-center">
                                                        <ZoneLogo
                                                            zoneCode={zone === 'BAKAT' ? 'BAKAT' : zone}
                                                            className="w-16 h-16 sm:w-18 sm:h-18 md:w-22 md:h-22 lg:w-20 lg:h-20 xl:w-28 xl:h-28 2xl:w-32 2xl:h-32 object-contain"
                                                        />
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ) : (
                                    // Regular Match - Zone vs Zone
                                    <div className="flex items-center justify-between mb-2 sm:mb-3 md:mb-3 lg:mb-3 xl:mb-4 2xl:mb-4">
                                        <div className="text-center flex-1">
                                            <div className="mx-auto mb-1 sm:mb-1 md:mb-2 lg:mb-1 xl:mb-2 2xl:mb-3 flex items-center justify-center">
                                                {isFinalMatch(match) ? (
                                                    <FinalPlaceholder className="w-28 h-28 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-32 lg:h-32 xl:w-40 xl:h-40 2xl:w-44 2xl:h-44" label="X" />
                                                ) : (
                                                    <ZoneLogo
                                                        zoneCode={match.zone_a === 'BAKAT' ? 'BAKAT' : match.zone_a}
                                                        className="w-40 h-40 sm:w-48 sm:h-48 md:w-60 md:h-60 lg:w-56 lg:h-56 xl:w-72 xl:h-72 2xl:w-80 2xl:h-80 object-contain"
                                                    />
                                                )}
                                            </div>
                                        </div>

                                        <div className="px-2 sm:px-3 md:px-4 lg:px-3 xl:px-5 2xl:px-6">
                                            <span className="text-lg sm:text-lg md:text-xl lg:text-xl xl:text-2xl 2xl:text-3xl font-black text-yellow-400">VS</span>
                                        </div>

                                        <div className="text-center flex-1">
                                            <div className="mx-auto mb-1 sm:mb-1 md:mb-2 lg:mb-1 xl:mb-2 2xl:mb-3 flex items-center justify-center">
                                                {isFinalMatch(match) ? (
                                                    <FinalPlaceholder className="w-28 h-28 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-32 lg:h-32 xl:w-40 xl:h-40 2xl:w-44 2xl:h-44" label="Y" />
                                                ) : (
                                                    <ZoneLogo
                                                        zoneCode={match.zone_b === 'BAKAT' ? 'BAKAT' : match.zone_b}
                                                        className="w-40 h-40 sm:w-48 sm:h-48 md:w-60 md:h-60 lg:w-56 lg:h-56 xl:w-72 xl:h-72 2xl:w-80 2xl:h-80 object-contain"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Match Details */}
                                <div className="grid grid-cols-3 gap-4 2xl:gap-6 xl:gap-5 lg:gap-3 md:gap-4 sm:gap-3 pt-6 2xl:pt-8 xl:pt-7 lg:pt-4 md:pt-5 sm:pt-4 border-t border-white/10">
                                    <div className="text-center">
                                        <Calendar className="w-5 h-5 2xl:w-6 2xl:h-6 xl:w-5 xl:h-5 lg:w-4 lg:h-4 md:w-4 md:h-4 sm:w-4 sm:h-4 text-gray-400 mx-auto mb-2 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1" />
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-1">Tarikh</p>
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold text-white">{formatDate(match.match_date)}</p>
                                    </div>
                                    <div className="text-center">
                                        <Clock className="w-5 h-5 2xl:w-6 2xl:h-6 xl:w-5 xl:h-5 lg:w-4 lg:h-4 md:w-4 md:h-4 sm:w-4 sm:h-4 text-gray-400 mx-auto mb-2 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1" />
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-1">Masa</p>
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold text-white">{match.match_time?.substring(0, 5)}</p>
                                    </div>
                                    <div className="text-center">
                                        <MapPin className="w-5 h-5 2xl:w-6 2xl:h-6 xl:w-5 xl:h-5 lg:w-4 lg:h-4 md:w-4 md:h-4 sm:w-4 sm:h-4 text-gray-400 mx-auto mb-2 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1" />
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs text-gray-400 mb-1">Venue</p>
                                        <p className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold text-white">{match.venue}</p>
                                    </div>
                                </div>
                            </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        </div>
    );
}
