import { Book<PERSON><PERSON>, Alert<PERSON>riangle, CheckCircle, Users, Trophy, Shield } from 'lucide-react';
import { usePageContent } from '@/hooks/usePageContent';

const generalRules = [
    'Semua peserta mesti mendaftar melalui Portal Sukan Intra Kor Kesihatan DiRaja sebelum tarikh tutup pendaftaran',
    '<PERSON><PERSON> anggota Kor Kesihatan DiRaja yang layak untuk menyertai pertandingan ini',
    '<PERSON>iap zon boleh menyertai semua 6 kategori sukan: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan <PERSON>',
    'Ma<PERSON> pendaftaran berbeza mengikut sukan: 1 jam (<PERSON><PERSON>), 30 minit (<PERSON><PERSON>, <PERSON><PERSON>), 15 minit (<PERSON><PERSON>, <PERSON>, <PERSON>)',
    'Pakaian seragam zon adalah wajib untuk semua peserta, ketua pasukan mesti memakai arm band',
    '<PERSON><PERSON><PERSON> pemain mengikut kategori: <PERSON><PERSON><PERSON><PERSON>, PTT Kanan, PTT Rendah, dan pemain terbuka (KKD/Kor Tumpangan/Anggota Awam)',
    'Bantahan mesti dibuat secara bertulis dalam masa yang ditetapkan dengan wang pertaruhan (RM50-RM500)',
    'Keputusan pengadil bertauliah adalah muktamad dan tidak boleh dipertikaikan',
    'Peserta yang tidak hadir atau lewat mengikut masa yang ditetapkan akan menghadapi penalti atau diskualifikasi',
    'Semua peserta mesti menghormati lawan, pengadil, dan pegawai pertandingan serta mematuhi kod etika Kor Kesihatan DiRaja',
    'Penggunaan bahan terlarang atau doping adalah dilarang sama sekali dan akan mengakibatkan diskualifikasi segera',
    'Sebarang bentuk kecurangan, kelakuan tidak sportif, atau tidak menghormati lawan akan mengakibatkan amaran atau diskualifikasi'
];

const sportsRules = [
    {
        sport: 'Lari Berganti-Ganti',
        icon: '/images/10x400mpsd.png',
        color: 'bg-teal-500/20 text-teal-300 border-teal-500/30',
        category: 'Campuran (5 Lelaki + 5 Wanita)',
        rules: [
            'Pendaftaran: 10 pelari per zon (5 lelaki + 5 wanita) - 30 minit sebelum perlawanan',
            'Susunan: 1 Pengurus + 2 Pegawai + 2 PTT Kanan + 1 Waran + 2 PTT Rendah + 1 Terbuka',
            'Format: Sistem knockout - pusingan saringan, separuh akhir, akhir',
            'Jarak: Setiap pelari berlari 400 meter (1 pusingan penuh trek)',
            'Tongkat: Mesti diserahkan dalam zon pertukaran 20 meter, jatuh boleh diambil',
            'Susunan Larian: Pegawai Kanan → PTT Kanan → PTT Rendah → Waran → PTT Rendah → Terbuka → PTT Rendah → PTT Kanan → PTT Rendah → Pegawai Kanan',
            'Hadiah: Johan (10 Emas), Naib Johan (10 Perak), Ke-3 (10 Gangsa) + 1 Hadiah Pengurus',
            'Bantahan: RM50 dalam masa 15 minit selepas perlawanan tamat'
        ]
    },
    {
        sport: 'Bola Sepak',
        icon: '/images/bolasepakpsd.png',
        color: 'bg-red-500/20 text-red-300 border-red-500/30',
        category: 'Lelaki',
        rules: [
            'Pendaftaran: 18 pemain per zon - 1 jam sebelum perlawanan',
            'Susunan: 1 Pegawai + 1 PTT Kanan + 2 PTT Rendah + 2 Terbuka (6 pemain utama + 12 simpanan)',
            'Masa: 2 x 30 minit (saringan), 2 x 45 minit (akhir) dengan rehat 15 minit',
            'Pertukaran: Maksimum 5 pemain, gagal hadir 10 minit = kalah automatik',
            'Penalti: Seri diselesaikan dengan sepakan penalti (5 sepakan), sudden death jika masih seri',
            'Peraturan: Mengikut undang-undang FIFA yang diubahsuai untuk format pendek',
            'Kad: Kad kuning (amaran), kad merah (dikeluarkan), 2 kad kuning = 1 kad merah',
            'Bantahan: RM500 dalam masa 10 minit selepas perlawanan tamat'
        ]
    },
    {
        sport: 'Bola Jaring',
        icon: '/images/netballpsd.png',
        color: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
        category: 'Wanita',
        rules: [
            'Pendaftaran: 12 pemain per zon - 30 minit sebelum perlawanan',
            'Susunan: 1 Pegawai + 1 PTT Kanan + 2 PTT Rendah + 3 Terbuka (7 pemain utama + 5 simpanan)',
            'Masa: 4 suku x 7 minit dengan rehat 1-3 minit antara suku',
            'Posisi: GS, GA, WA, C, WD, GD, GK - pemain mesti berada dalam posisi yang ditetapkan',
            'Pertukaran: Hanya pada masa rehat atau kecederaan, maksimum 2 pertukaran per suku',
            'Peraturan: Bola mesti dihantar dalam 3 saat, tidak boleh berlari sambil pegang bola',
            'Penalti: Gagal hadir 5 minit = kalah 10-0, time-out 2 kali per babak (1 minit)',
            'Bantahan: RM500 dalam masa 10 minit selepas perlawanan tamat'
        ]
    },
    {
        sport: 'Bola Tampar',
        icon: '/images/bolatamparpsd.png',
        color: 'bg-orange-500/20 text-orange-300 border-orange-500/30',
        category: 'Lelaki & Wanita',
        rules: [
            'Pendaftaran: 15 pemain per zon - 15 minit sebelum perlawanan',
            'Susunan: 1 Pegawai + 1 PTT Kanan + 2 PTT Rendah + 2 Terbuka (6 pemain utama + 9 simpanan)',
            'Format: Best of 3 set (25 mata), set ketiga 15 mata, rehat 3 minit antara set',
            'Pemain Libero: Dibenarkan dengan jersi berlainan warna, tidak boleh spike atau block',
            'Peraturan: Maksimum 3 sentuhan, tidak boleh sentuh net, rotasi mengikut arah jam',
            'Tagging: Semua pemain mesti pakai tagging, tidak pakai = tidak boleh bermain',
            'Walk Over: Kurang dari 6 pemain = kalah 3-0, gagal hadir = kalah automatik',
            'Bantahan: RM500 dalam masa 10 minit selepas perlawanan tamat'
        ]
    },
    {
        sport: 'Badminton',
        icon: '/images/badmintonpsd.png',
        color: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
        category: '5 Kategori (Lelaki, Wanita, Campuran)',
        rules: [
            'Pendaftaran: 13 pemain per zon - 15 minit sebelum perlawanan',
            'Kategori: Perseorangan Lelaki, Beregu Lelaki A & B, Beregu Wanita, Beregu Campuran (5 kategori)',
            'Format: Best of 3 set (21 mata), selisih minimum 2 mata, maksimum 30 mata',
            'Rehat: 2 minit antara set, 60 saat selepas mata 11 dalam setiap set',
            'Peraturan: Mengikut undang-undang BAM, servis dari bawah secara menyerong',
            'PTT: 2 PTT per kategori, boleh main dalam kategori yang berbeza',
            'Juara: Kiraan mata tertinggi dari semua 5 kategori menentukan juara keseluruhan',
            'Hadiah: Johan (11 Emas), Naib Johan (11 Perak), Ke-3 (11 Gangsa)',
            'Bantahan: RM500 dalam masa 10 minit selepas perlawanan tamat'
        ]
    },
    {
        sport: 'Ping Pong',
        icon: '/images/pingpongpsd.png',
        color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
        category: '5 Kategori (Lelaki, Wanita, Campuran)',
        rules: [
            'Pendaftaran: 9 pemain per zon - 15 minit sebelum perlawanan',
            'Kategori: Perseorangan Lelaki, Beregu Lelaki A & B, Beregu Wanita, Beregu Campuran (5 kategori)',
            'Format: Best of 5 set (11 poin), selisih minimum 2 poin, rehat 1 minit antara set',
            'Servis: Bertukar setiap 2 poin, mesti dari belakang garisan meja',
            'Time-out: 1 kali per pertandingan (1 minit), boleh dipanggil bila-bila masa',
            'Peraturan: Mengikut undang-undang ITTF, bola mesti melantun sekali di setiap sisi',
            'PTT: 5 PTT per kategori, boleh main dalam kategori yang berbeza',
            'Juara: Kiraan mata tertinggi dari semua 5 kategori menentukan juara keseluruhan',
            'Hadiah: Johan (11 Emas), Naib Johan (11 Perak), Ke-3 (11 Gangsa)',
            'Bantahan: RM500 dalam masa 10 minit selepas perlawanan tamat'
        ]
    }
];

const penalties = [
    {
        offense: 'Tidak hadir tanpa notis awal',
        penalty: 'Diskualifikasi dari pertandingan dan zon kehilangan mata',
        severity: 'high'
    },
    {
        offense: 'Lewat hadir (Bola Sepak: >10 minit)',
        penalty: 'Kalah secara automatik (Walk Over)',
        severity: 'high'
    },
    {
        offense: 'Lewat hadir (Bola Jaring: >5 minit)',
        penalty: 'Kalah dengan skor 10-0',
        severity: 'high'
    },
    {
        offense: 'Kurang pemain (Bola Tampar: <6 pemain)',
        penalty: 'Walk Over dengan skor 3-0 untuk lawan',
        severity: 'high'
    },
    {
        offense: 'Bantahan tidak sah atau lewat masa',
        penalty: 'Wang pertaruhan RM50-RM500 tidak dikembalikan',
        severity: 'medium'
    },
    {
        offense: 'Tidak memakai pakaian seragam zon',
        penalty: 'Tidak dibenarkan bertanding sehingga memakai pakaian yang betul',
        severity: 'medium'
    },
    {
        offense: 'Kelakuan tidak sportif atau tidak menghormati lawan',
        penalty: 'Amaran pertama, diskualifikasi jika berulang',
        severity: 'medium'
    },
    {
        offense: 'Kecurangan atau penggunaan bahan terlarang',
        penalty: 'Diskualifikasi segera dan laporan kepada pihak atasan',
        severity: 'high'
    },
    {
        offense: 'Mempertikaikan keputusan pengadil secara tidak sopan',
        penalty: 'Amaran atau diskualifikasi bergantung kepada tahap kesalahan',
        severity: 'medium'
    },
    {
        offense: 'Tidak memakai tagging (Bola Tampar)',
        penalty: 'Tidak dibenarkan bermain sepanjang perlawanan',
        severity: 'medium'
    }
];

const getSeverityColor = (severity: string) => {
    switch (severity) {
        case 'high':
            return 'bg-red-500/20 text-red-300 border-red-500/30';
        case 'medium':
            return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
        case 'low':
            return 'bg-green-500/20 text-green-300 border-green-500/30';
        default:
            return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
};

export default function Rules() {
    const { data: content = {} } = usePageContent('rules');

    // Get CMS content with fallbacks
    const heroTitle = content?.hero?.title || 'PERATURAN PERTANDINGAN';
    const heroSubtitle = content?.hero?.subtitle || 'Panduan lengkap peraturan dan syarat penyertaan untuk Sukan Intra Kor Kesihatan DiRaja 2025';
    const heroDescription = content?.hero?.description || 'Sila baca dan fahami dengan teliti semua peraturan dan syarat penyertaan sebelum menyertai pertandingan.';
    const heroBackgroundImage = content?.hero?.background_image || '/images/default.jpg';

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <section
                className="relative min-h-screen bg-cover bg-center bg-no-repeat pt-32 2xl:pt-40 xl:pt-36 lg:pt-28 md:pt-28 sm:pt-24"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('${heroBackgroundImage}')`
                }}
            >
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 py-16 2xl:py-20 xl:py-18 lg:py-12 md:py-12 sm:py-8">
                    <div className="2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full mx-auto text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 2xl:w-24 2xl:h-24 xl:w-22 xl:h-22 lg:w-16 lg:h-16 md:w-18 md:h-18 sm:w-14 sm:h-14 bg-yellow-500/20 rounded-full mb-6 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-4">
                            <Shield className="w-10 h-10 2xl:w-12 2xl:h-12 xl:w-11 xl:h-11 lg:w-8 lg:h-8 md:w-9 md:h-9 sm:w-7 sm:h-7 text-yellow-400" />
                        </div>
                        <h1 className="text-4xl md:text-6xl 2xl:text-8xl xl:text-7xl lg:text-4xl sm:text-2xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-3 drop-shadow-lg">
                            <span className="text-yellow-400">{heroTitle}</span>
                        </h1>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-white/90 mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-3 leading-relaxed 2xl:max-w-4xl xl:max-w-4xl lg:max-w-3xl md:max-w-3xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroSubtitle}
                        </p>
                        <p className="text-base 2xl:text-lg xl:text-base lg:text-xs md:text-sm sm:text-xs text-white/80 mb-6 2xl:mb-10 xl:mb-9 lg:mb-6 md:mb-7 sm:mb-4 leading-relaxed 2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto px-4 sm:px-6">
                            {heroDescription}
                        </p>

                      
                    </div>
                </div>
            </section>

            {/* General Rules Section */}
            <section className="py-16 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-6 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-4 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-4">
                        <h2 className="text-4xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-2">
                            <span className="text-green-400">PERATURAN</span> AM
                        </h2>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-3xl mx-auto px-4">
                            Peraturan asas yang terpakai untuk semua kategori sukan
                        </p>
                    </div>

                    <div className="2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto">
                        <div className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-green-400/50 overflow-hidden shadow-2xl">
                            <div className="bg-gradient-to-r from-green-600/80 to-blue-600/80 backdrop-blur-sm p-4 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-3 border-b border-green-400/30">
                                <div className="flex items-center space-x-2 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-2">
                                    <BookOpen className="w-6 h-6 2xl:w-10 2xl:h-10 xl:w-9 xl:h-9 lg:w-7 lg:h-7 md:w-8 md:h-8 sm:w-4 sm:h-4 text-green-400" />
                                    <h3 className="text-2xl 2xl:text-4xl xl:text-3xl lg:text-2xl md:text-2xl sm:text-sm font-black text-white drop-shadow-lg">
                                        PERATURAN ASAS
                                    </h3>
                                </div>
                                <p className="text-base 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-white/90 mt-2 2xl:mt-3 xl:mt-2 lg:mt-1 md:mt-2 sm:mt-1">
                                    Wajib dipatuhi oleh semua peserta tanpa pengecualian
                                </p>
                            </div>

                            <div className="p-6 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-3">
                                <div className="space-y-4 2xl:space-y-8 xl:space-y-7 lg:space-y-4 md:space-y-5 sm:space-y-2">
                                    {generalRules.map((rule, index) => (
                                        <div key={index} className="flex items-start space-x-3 2xl:space-x-5 xl:space-x-4 lg:space-x-3 md:space-x-4 sm:space-x-2 p-4 2xl:p-8 xl:p-7 lg:p-4 md:p-5 sm:p-2 bg-green-500/10 backdrop-blur-sm rounded-lg 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-lg sm:rounded-md border border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                                            <div className="flex items-center justify-center w-6 h-6 2xl:w-10 2xl:h-10 xl:w-9 xl:h-9 lg:w-7 lg:h-7 md:w-8 md:h-8 sm:w-4 sm:h-4 bg-green-500/20 rounded-full flex-shrink-0">
                                                <CheckCircle className="w-4 h-4 2xl:w-6 2xl:h-6 xl:w-5 xl:h-5 lg:w-4 lg:h-4 md:w-4 md:w-4 sm:w-2 sm:h-2 text-green-400" />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex items-center space-x-2 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1 mb-1 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1">
                                                    <span className="text-xs 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-bold text-green-400">PERATURAN {index + 1}</span>
                                                </div>
                                                <p className="text-white text-sm 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs leading-relaxed">{rule}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {/* Sports Specific Rules Section */}
            <section className="py-16 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-6 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto px-4 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-4">
                        <h2 className="text-4xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-2">
                            <span className="text-yellow-400">PERATURAN</span> MENGIKUT SUKAN
                        </h2>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-3xl mx-auto px-4">
                            Peraturan khusus untuk setiap kategori sukan
                        </p>
                    </div>

                    <div className="space-y-6 2xl:space-y-10 xl:space-y-9 lg:space-y-6 md:space-y-7 sm:space-y-3">
                        {sportsRules.map((sport, index) => (
                            <div key={index} className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-yellow-400/30 overflow-hidden shadow-2xl hover:border-yellow-400/50 transition-all duration-300">
                                <div className="bg-gradient-to-r from-blue-600/80 to-green-600/80 backdrop-blur-sm p-4 2xl:p-8 xl:p-7 lg:p-5 md:p-6 sm:p-3 border-b border-yellow-400/30">
                                    <div className="flex items-center justify-between flex-col sm:flex-col lg:flex-row gap-2 sm:gap-2 lg:gap-0">
                                        <div className="flex items-center space-x-3 2xl:space-x-5 xl:space-x-4 lg:space-x-3 md:space-x-4 sm:space-x-2">
                                            <img
                                                src={sport.icon}
                                                alt={sport.sport}
                                                className="w-10 h-10 2xl:w-16 2xl:h-16 xl:w-14 xl:h-14 lg:w-10 lg:h-10 md:w-12 md:h-12 sm:w-6 sm:h-6 object-contain"
                                            />
                                            <div>
                                                <h3 className="text-2xl 2xl:text-4xl xl:text-3xl lg:text-2xl md:text-2xl sm:text-sm font-black text-white">
                                                    {sport.sport}
                                                </h3>
                                                <p className="text-white/80 text-sm 2xl:text-lg xl:text-base lg:text-sm md:text-sm sm:text-xs">
                                                    Kategori: {sport.category}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-center sm:text-center lg:text-right">
                                            <span className={`px-4 py-2 2xl:px-5 2xl:py-3 xl:px-4 xl:py-2 lg:px-3 lg:py-2 md:px-4 md:py-2 sm:px-2 sm:py-1 rounded-full text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${sport.color}`}>
                                                {sport.rules.length} Peraturan
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div className="p-8 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-3">
                                    <div className="space-y-4 2xl:space-y-6 xl:space-y-5 lg:space-y-3 md:space-y-4 sm:space-y-2">
                                        {sport.rules.map((rule, ruleIndex) => (
                                            <div key={ruleIndex} className="flex items-start space-x-4 2xl:space-x-5 xl:space-x-4 lg:space-x-3 md:space-x-4 sm:space-x-2 p-4 2xl:p-6 xl:p-5 lg:p-3 md:p-4 sm:p-2 bg-white/5 backdrop-blur-sm rounded-xl 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-lg sm:rounded-lg border border-white/10 hover:border-yellow-400/30 transition-all duration-300">
                                                <div className="flex items-center justify-center w-8 h-8 2xl:w-10 2xl:h-10 xl:w-9 xl:h-9 lg:w-7 lg:h-7 md:w-8 md:h-8 sm:w-5 sm:h-5 bg-yellow-500/20 rounded-full flex-shrink-0">
                                                    <span className="text-sm 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-bold text-yellow-400">{ruleIndex + 1}</span>
                                                </div>
                                                <p className="text-white text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs leading-relaxed">{rule}</p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Penalties Section */}
            <section className="py-16 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-6 bg-gradient-to-br from-gray-900 to-black">
                <div className="container mx-auto px-4 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-full max-w-full">
                    <div className="text-center mb-12 2xl:mb-20 xl:mb-18 lg:mb-8 md:mb-12 sm:mb-4">
                        <h2 className="text-4xl 2xl:text-7xl xl:text-6xl lg:text-3xl md:text-4xl sm:text-xl font-black text-white mb-4 2xl:mb-8 xl:mb-7 lg:mb-4 md:mb-5 sm:mb-2">
                            <span className="text-red-400">PENALTI</span> DAN HUKUMAN
                        </h2>
                        <p className="text-lg 2xl:text-2xl xl:text-xl lg:text-sm md:text-base sm:text-xs text-gray-300 max-w-3xl mx-auto px-4">
                            Senarai kesalahan dan hukuman yang akan dikenakan
                        </p>
                    </div>

                    <div className="2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-full max-w-full mx-auto">
                        <div className="bg-black/60 backdrop-blur-xl rounded-2xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-red-400/50 overflow-hidden shadow-2xl">
                            <div className="bg-gradient-to-r from-red-600/80 to-orange-600/80 backdrop-blur-sm p-4 2xl:p-8 xl:p-7 lg:p-6 md:p-5 sm:p-3 border-b border-red-400/30">
                                <div className="flex items-center space-x-2 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-2">
                                    <AlertTriangle className="w-6 h-6 2xl:w-10 2xl:h-10 xl:w-9 xl:h-9 lg:w-7 lg:h-7 md:w-8 md:h-8 sm:w-4 sm:h-4 text-red-400" />
                                    <h3 className="text-2xl 2xl:text-4xl xl:text-3xl lg:text-2xl md:text-2xl sm:text-sm font-black text-white drop-shadow-lg">
                                        SISTEM PENALTI
                                    </h3>
                                </div>
                                <p className="text-base 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs text-white/90 mt-2 2xl:mt-3 xl:mt-2 lg:mt-1 md:mt-2 sm:mt-1">
                                    Hukuman mengikut tahap keseriusan kesalahan
                                </p>
                            </div>

                            <div className="p-6 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-3">
                                <div className="space-y-4 2xl:space-y-8 xl:space-y-7 lg:space-y-4 md:space-y-5 sm:space-y-2">
                                    {penalties.map((penalty, index) => (
                                        <div key={index} className="flex items-start justify-between flex-col sm:flex-col lg:flex-row gap-2 sm:gap-2 lg:gap-6 p-4 2xl:p-8 xl:p-7 lg:p-4 md:p-5 sm:p-2 bg-red-500/10 backdrop-blur-sm rounded-lg 2xl:rounded-2xl xl:rounded-xl lg:rounded-lg md:rounded-lg sm:rounded-md border border-red-500/20 hover:border-red-500/40 transition-all duration-300">
                                            <div className="flex-1">
                                                <h4 className="font-bold text-lg 2xl:text-2xl xl:text-xl lg:text-lg md:text-lg sm:text-xs text-white mb-1 2xl:mb-3 xl:mb-2 lg:mb-1 md:mb-2 sm:mb-1">{penalty.offense}</h4>
                                                <p className="text-gray-300 text-sm 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs leading-relaxed">{penalty.penalty}</p>
                                            </div>
                                            <div className="sm:self-start lg:ml-6 lg:self-center">
                                                <span className={`px-3 py-1 2xl:px-5 2xl:py-3 xl:px-4 xl:py-2 lg:px-3 lg:py-2 md:px-4 md:py-2 sm:px-2 sm:py-1 rounded-full text-xs 2xl:text-base xl:text-sm lg:text-xs md:text-xs sm:text-xs font-semibold border ${getSeverityColor(penalty.severity)}`}>
                                                    {penalty.severity === 'high' ? 'BERAT' :
                                                     penalty.severity === 'medium' ? 'SEDERHANA' : 'RINGAN'}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Important Notes Section */}
            <section className="py-20 2xl:py-28 xl:py-24 lg:py-12 md:py-16 sm:py-8 bg-gradient-to-br from-black to-gray-900">
                <div className="container mx-auto px-6 2xl:px-8 xl:px-6 lg:px-4 md:px-4 sm:px-4 2xl:max-w-7xl xl:max-w-7xl lg:max-w-5xl md:max-w-5xl sm:max-w-4xl max-w-full">
                    <div className="2xl:max-w-5xl xl:max-w-5xl lg:max-w-4xl md:max-w-4xl sm:max-w-3xl max-w-full mx-auto">
                        <div className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 backdrop-blur-xl rounded-3xl 2xl:rounded-3xl xl:rounded-2xl lg:rounded-2xl md:rounded-xl sm:rounded-lg border-2 border-yellow-400/50 p-8 2xl:p-10 xl:p-9 lg:p-6 md:p-7 sm:p-4">
                            <div className="sm:block md:flex items-start space-x-0 sm:space-x-0 md:space-x-4 2xl:space-x-5 xl:space-x-4 lg:space-x-3">
                                <div className="sm:bg-transparent md:bg-yellow-500/20 rounded-full p-3 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-0 sm:mb-2 md:mb-0 sm:w-fit">
                                    <AlertTriangle className="h-8 w-8 2xl:h-10 2xl:w-10 xl:h-9 xl:w-9 lg:h-7 lg:w-7 md:h-8 md:w-8 sm:h-4 sm:w-4 text-yellow-400" />
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-2xl 2xl:text-3xl xl:text-2xl lg:text-xl md:text-xl sm:text-base font-bold text-yellow-400 mb-4 2xl:mb-6 xl:mb-5 lg:mb-3 md:mb-4 sm:mb-2">
                                        PENTING UNTUK DIINGAT
                                    </h3>
                                    <div className="space-y-3 2xl:space-y-4 xl:space-y-3 lg:space-y-2 md:space-y-3 sm:space-y-2 text-white/90 text-lg 2xl:text-xl xl:text-lg lg:text-sm md:text-base sm:text-xs">
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Semua peserta mesti membaca dan memahami peraturan sebelum menyertai pertandingan</p>
                                        </div>
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Ketidaktahuan tentang peraturan bukan alasan untuk melanggar peraturan</p>
                                        </div>
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Pengadil dan jawatankuasa teknikal berhak membuat keputusan berdasarkan peraturan yang ditetapkan</p>
                                        </div>
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Sebarang pertikaian mesti dibuat secara bertulis kepada jawatankuasa penganjur dalam masa 24 jam</p>
                                        </div>
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Peraturan ini boleh dikemaskini dari semasa ke semasa mengikut keperluan</p>
                                        </div>
                                        <div className="flex items-start space-x-3 2xl:space-x-4 xl:space-x-3 lg:space-x-2 md:space-x-3 sm:space-x-1">
                                            <CheckCircle className="h-5 w-5 2xl:h-6 2xl:w-6 xl:h-5 xl:w-5 lg:h-4 lg:w-4 md:h-4 md:w-4 sm:h-3 sm:w-3 text-yellow-400 mt-1 flex-shrink-0" />
                                            <p>Semua peserta bertanggungjawab untuk mematuhi kod etika dan nilai-nilai Kor Kesihatan DiRaja</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    );
}

