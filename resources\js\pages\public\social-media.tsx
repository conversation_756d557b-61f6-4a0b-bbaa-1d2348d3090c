import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Share2, ExternalLink, Users, Heart, MessageCircle, Eye, Clock } from 'lucide-react';

const socialMediaLinks = [
    {
        platform: 'Facebook',
        handle: '@PortalSukanIntraKKD',
        url: 'https://facebook.com/portalsukankd',
        icon: '📘',
        color: 'bg-blue-600 hover:bg-blue-700',
        description: '<PERSON><PERSON><PERSON> kemaskini terkini, foto, dan video pertandingan',
        followers: '2.5K',
        posts: 45
    },
    {
        platform: 'Instagram',
        handle: '@portalsukan_kkd',
        url: 'https://instagram.com/portalsukan_kkd',
        icon: '📷',
        color: 'bg-pink-600 hover:bg-pink-700',
        description: 'Foto dan stories eksklusif dari set<PERSON>p pertandingan',
        followers: '1.8K',
        posts: 67
    },
    {
        platform: 'Twitter',
        handle: '@PortalSukanKKD',
        url: 'https://twitter.com/portalsukankd',
        icon: '🐦',
        color: 'bg-sky-500 hover:bg-sky-600',
        description: 'Live updates dan keputusan pertandingan real-time',
        followers: '1.2K',
        posts: 89
    },
    {
        platform: 'YouTube',
        handle: 'Portal Sukan Intra KKD',
        url: 'https://youtube.com/@portalsukankd',
        icon: '📺',
        color: 'bg-red-600 hover:bg-red-700',
        description: 'Video highlight dan live streaming pertandingan',
        followers: '856',
        posts: 23
    },
    {
        platform: 'TikTok',
        handle: '@portalsukan.kkd',
        url: 'https://tiktok.com/@portalsukan.kkd',
        icon: '🎵',
        color: 'bg-black hover:bg-gray-800',
        description: 'Video pendek dan momen viral dari pertandingan',
        followers: '3.1K',
        posts: 34
    },
    {
        platform: 'WhatsApp',
        handle: 'Broadcast Channel',
        url: 'https://whatsapp.com/channel/portalsukan',
        icon: '💬',
        color: 'bg-green-600 hover:bg-green-700',
        description: 'Notifikasi segera untuk pengumuman penting',
        followers: '500+',
        posts: 12
    }
];

const liveUpdates = [
    {
        id: 1,
        platform: 'Instagram',
        content: 'Pertandingan bola sepak final sedang berlangsung! Kolej Pertama vs Kolej Kedua 1-0 (HT)',
        timestamp: '2025-07-04T14:30:00Z',
        likes: 45,
        comments: 12,
        type: 'live'
    },
    {
        id: 2,
        platform: 'Twitter',
        content: 'GOAL! Kolej Pertama menjaringkan gol kedua! Skor kini 2-0. #PortalSukan2025',
        timestamp: '2025-07-04T14:25:00Z',
        likes: 67,
        comments: 23,
        type: 'update'
    },
    {
        id: 3,
        platform: 'Facebook',
        content: 'Foto-foto terbaik dari pertandingan badminton hari ini telah dimuatnaik! Lihat aksi-aksi hebat para pemain.',
        timestamp: '2025-07-04T13:45:00Z',
        likes: 89,
        comments: 34,
        type: 'media'
    },
    {
        id: 4,
        platform: 'YouTube',
        content: 'Video highlight larian 10x400m kini tersedia! Saksikan detik-detik menegangkan perlumbaan.',
        timestamp: '2025-07-04T12:15:00Z',
        likes: 156,
        comments: 45,
        type: 'video'
    }
];

const hashtags = [
    '#PortalSukan2025',
    '#IntraKKD',
    '#SukanKolej',
    '#KKDSports',
    '#TeamSpirit',
    '#CollegeSports',
    '#SukanIntra',
    '#KKDChampions'
];

const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Baru sahaja';
    if (diffInMinutes < 60) return `${diffInMinutes} minit yang lalu`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} jam yang lalu`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} hari yang lalu`;
};

const getUpdateTypeColor = (type: string) => {
    switch (type) {
        case 'live':
            return 'bg-red-100 text-red-800';
        case 'update':
            return 'bg-blue-100 text-blue-800';
        case 'media':
            return 'bg-green-100 text-green-800';
        case 'video':
            return 'bg-purple-100 text-purple-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

export default function SocialMedia() {
    const totalFollowers = socialMediaLinks.reduce((sum, link) => {
        const followers = parseFloat(link.followers.replace(/[K+]/g, '')) * (link.followers.includes('K') ? 1000 : 1);
        return sum + followers;
    }, 0);

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container mx-auto px-6">
                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-2xl font-bold">{socialMediaLinks.length}</CardTitle>
                                    <CardDescription className="text-blue-100">
                                        Platform Media
                                    </CardDescription>
                                </div>
                                <Share2 className="h-10 w-10 text-blue-200" />
                            </div>
                        </CardHeader>
                    </Card>

                    <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-2xl font-bold">{(totalFollowers / 1000).toFixed(1)}K</CardTitle>
                                    <CardDescription className="text-green-100">
                                        Jumlah Pengikut
                                    </CardDescription>
                                </div>
                                <Users className="h-10 w-10 text-green-200" />
                            </div>
                        </CardHeader>
                    </Card>

                    <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-2xl font-bold">{liveUpdates.length}</CardTitle>
                                    <CardDescription className="text-purple-100">
                                        Update Terkini
                                    </CardDescription>
                                </div>
                                <Clock className="h-10 w-10 text-purple-200" />
                            </div>
                        </CardHeader>
                    </Card>

                    <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-2xl font-bold">{hashtags.length}</CardTitle>
                                    <CardDescription className="text-orange-100">
                                        Hashtag Rasmi
                                    </CardDescription>
                                </div>
                                <MessageCircle className="h-10 w-10 text-orange-200" />
                            </div>
                        </CardHeader>
                    </Card>
                </div>

                {/* Social Media Links */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Share2 className="h-6 w-6 text-blue-600" />
                            <span>Ikuti Kami di Media Sosial</span>
                        </CardTitle>
                        <CardDescription>
                            Dapatkan kemaskini terkini, foto, video, dan live updates dari semua platform kami
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {socialMediaLinks.map((link, index) => (
                                <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                                    <CardContent className="p-6">
                                        <div className="flex items-center space-x-4 mb-4">
                                            <div className="text-3xl">{link.icon}</div>
                                            <div>
                                                <h3 className="font-bold text-lg text-gray-900">{link.platform}</h3>
                                                <p className="text-sm text-gray-600">{link.handle}</p>
                                            </div>
                                        </div>
                                        
                                        <p className="text-gray-700 text-sm mb-4">
                                            {link.description}
                                        </p>
                                        
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="flex space-x-4 text-sm text-gray-600">
                                                <div className="flex items-center space-x-1">
                                                    <Users className="h-4 w-4" />
                                                    <span>{link.followers}</span>
                                                </div>
                                                <div className="flex items-center space-x-1">
                                                    <Eye className="h-4 w-4" />
                                                    <span>{link.posts} posts</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <Button 
                                            className={`w-full ${link.color} text-white`}
                                            onClick={() => window.open(link.url, '_blank')}
                                        >
                                            <ExternalLink className="h-4 w-4 mr-2" />
                                            Ikuti di {link.platform}
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Live Updates */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Clock className="h-6 w-6 text-green-600" />
                            <span>Live Updates Terkini</span>
                        </CardTitle>
                        <CardDescription>
                            Kemaskini terbaru dari semua platform media sosial kami
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {liveUpdates.map((update) => (
                                <div key={update.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors">
                                    <div className="flex items-start justify-between mb-3">
                                        <div className="flex items-center space-x-3">
                                            <Badge className="bg-blue-100 text-blue-800">
                                                {update.platform}
                                            </Badge>
                                            <Badge className={getUpdateTypeColor(update.type)}>
                                                {update.type === 'live' ? 'LIVE' :
                                                 update.type === 'update' ? 'UPDATE' :
                                                 update.type === 'media' ? 'MEDIA' : 'VIDEO'}
                                            </Badge>
                                        </div>
                                        <span className="text-sm text-gray-500">
                                            {getTimeAgo(update.timestamp)}
                                        </span>
                                    </div>
                                    
                                    <p className="text-gray-900 mb-4">
                                        {update.content}
                                    </p>
                                    
                                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                                        <div className="flex items-center space-x-1">
                                            <Heart className="h-4 w-4" />
                                            <span>{update.likes} suka</span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <MessageCircle className="h-4 w-4" />
                                            <span>{update.comments} komen</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Official Hashtags */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <MessageCircle className="h-6 w-6 text-purple-600" />
                            <span>Hashtag Rasmi</span>
                        </CardTitle>
                        <CardDescription>
                            Gunakan hashtag ini dalam post anda untuk menyertai perbualan
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-3">
                            {hashtags.map((hashtag, index) => (
                                <Badge 
                                    key={index} 
                                    variant="outline" 
                                    className="text-lg px-4 py-2 cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                    onClick={() => navigator.clipboard.writeText(hashtag)}
                                >
                                    {hashtag}
                                </Badge>
                            ))}
                        </div>
                        <p className="text-sm text-gray-600 mt-4">
                            💡 Tip: Klik pada hashtag untuk menyalin ke clipboard
                        </p>
                    </CardContent>
                </Card>

                {/* Call to Action */}
                <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <CardContent className="pt-6">
                        <div className="text-center">
                            <Share2 className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">
                                Jangan Ketinggalan Sebarang Update!
                            </h3>
                            <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
                                Ikuti semua platform media sosial kami untuk mendapat kemaskini terkini, 
                                foto eksklusif, video highlight, dan live updates dari Portal Sukan Intra KKD 2025.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                                    <Share2 className="h-5 w-5 mr-2" />
                                    Ikuti Semua Platform
                                </Button>
                                <Button size="lg" variant="outline">
                                    <MessageCircle className="h-5 w-5 mr-2" />
                                    Join WhatsApp Channel
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
