export default function SimpleTest() {
    return (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
                <h1 className="text-2xl font-bold text-center mb-6 text-gray-800">
                    Simple Test Page
                </h1>
                
                <div className="space-y-4">
                    <div className="p-4 bg-green-100 border border-green-300 rounded-lg">
                        <h2 className="font-semibold text-green-800">✅ React Working</h2>
                        <p className="text-green-700 text-sm">Component rendered successfully</p>
                    </div>
                    
                    <div className="p-4 bg-blue-100 border border-blue-300 rounded-lg">
                        <h2 className="font-semibold text-blue-800">✅ Tailwind CSS Working</h2>
                        <p className="text-blue-700 text-sm">Styles applied correctly</p>
                    </div>
                    
                    <div className="p-4 bg-purple-100 border border-purple-300 rounded-lg">
                        <h2 className="font-semibold text-purple-800">✅ No Complex Hooks</h2>
                        <p className="text-purple-700 text-sm">Simple component without providers</p>
                    </div>
                    
                    <div className="p-4 bg-yellow-100 border border-yellow-300 rounded-lg">
                        <h2 className="font-semibold text-yellow-800">🔍 Debug Info</h2>
                        <p className="text-yellow-700 text-sm">
                            User Agent: {navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}
                        </p>
                        <p className="text-yellow-700 text-sm">
                            Screen: {window.innerWidth}x{window.innerHeight}
                        </p>
                    </div>
                </div>
                
                <div className="mt-6 text-center">
                    <p className="text-gray-600 text-sm">
                        If you see this page without errors, React is working fine.
                    </p>
                </div>
            </div>
        </div>
    );
}
