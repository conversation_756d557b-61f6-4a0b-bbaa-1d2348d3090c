import { createHashRouter } from 'react-router-dom';

// Import layouts
import AuthLayout from '@/layouts/auth-layout';
import DashboardLayout from '@/layouts/dashboard-layout-new';
import PublicLayout from '@/layouts/public-layout';
import ProtectedRoute from '@/components/protected-route';

// Import pages
import Login from '@/pages/auth/login';
import PasswordChange from '@/pages/auth/password-change';
import Dashboard from '@/pages/dashboard';

// Import public pages
import Home from '@/pages/public/home';
import MatchSchedule from '@/pages/public/match-schedule';
import TournamentResults from '@/pages/public/tournament-results';
import OverallStandings from '@/pages/public/overall-standings';
import ChampionshipSchedule from '@/pages/public/championship-schedule';
import Gallery from '@/pages/public/gallery';
import News from '@/pages/public/news';
import Rules from '@/pages/public/rules';
import SocialMedia from '@/pages/public/social-media';
import Jadual<PERSON><PERSON>andingan from '@/pages/public/jadual-pertandingan';

// Import admin pages
import Players from '@/pages/admin/players';
import Matches from '@/pages/admin/matches';
import TournamentBracket from '@/pages/admin/tournament-bracket';

import Reports from '@/pages/admin/reports';
import Settings from '@/pages/admin/settings';
import PageContent from '@/pages/admin/page-content';
import ZoneProfile from '@/pages/admin/zone-profile';

// Import test pages
import SimpleTest from '@/pages/simple-test';
// import DashboardSimple from '@/pages/dashboard-simple';

// Import test page
import TestPage from '@/pages/test';
import TournamentBracketTest from '@/pages/admin/tournament-bracket-test';

// Simple test component
const TestComponent = () => (
    <div style={{ padding: '20px', textAlign: 'center' }}>
        <h1>React App is Working!</h1>
        <p>Portal Sukan Intra KKD 2025</p>
    </div>
);

export const router = createHashRouter([
  // Test routes (no layout)
  {
    path: '/test',
    element: <SimpleTest />,
  },
  {
    path: '/dashboard-simple',
    element: (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Inline Dashboard Test
            </h1>
            <div className="bg-green-100 p-4 rounded-lg">
              <h2 className="font-semibold text-green-800">✅ Inline Component Working</h2>
              <p className="text-green-700 text-sm">No import issues, direct JSX in router</p>
            </div>
          </div>
        </div>
      </div>
    ),
  },

  // Public routes
  {
    path: '/',
    element: <PublicLayout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: 'match-schedule',
        element: <MatchSchedule />,
      },
      {
        path: 'tournament-results',
        element: <TournamentResults />,
      },
      {
        path: 'overall-standings',
        element: <OverallStandings />,
      },
      {
        path: 'championship-schedule',
        element: <ChampionshipSchedule />,
      },
      {
        path: 'jadual-pertandingan',
        element: <JadualPertandingan />,
      },
      {
        path: 'gallery',
        element: <Gallery />,
      },
      {
        path: 'news',
        element: <News />,
      },
      {
        path: 'rules',
        element: <Rules />,
      },
      {
        path: 'social-media',
        element: <SocialMedia />,
      },
    ],
  },
  
  // Auth routes
  {
    path: '/auth/login',
    element: <AuthLayout title="Log Masuk" description="Sila log masuk untuk mengakses sistem" />,
    children: [
      {
        index: true,
        element: <Login />,
      },
    ],
  },
  {
    path: '/auth/password/change',
    element: <AuthLayout title="Tukar Kata Laluan" description="Anda perlu menukar kata laluan untuk keselamatan akaun" />,
    children: [
      {
        index: true,
        element: <PasswordChange />,
      },
    ],
  },
  
  // Protected dashboard routes
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <DashboardLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Dashboard />,
      },

      // Admin routes
      {
        path: 'admin/players',
        element: <Players />,
      },
      {
        path: 'admin/matches',
        element: <Matches />,
      },
      {
        path: 'admin/tournament-bracket',
        element: <TournamentBracket />,
      },

      {
        path: 'admin/zone-profile',
        element: <ZoneProfile />,
      },
      {
        path: 'admin/reports',
        element: <Reports />,
      },
      {
        path: 'admin/settings',
        element: <Settings />,
      },
      {
        path: 'admin/cms/page-content',
        element: <PageContent />,
      },
      {
        path: 'admin/tournament-bracket-test',
        element: <TournamentBracketTest />,
      },
    ],
  },
]);
