export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface Match {
    id: number;
    title: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    score_zone_a?: number;
    score_zone_b?: number;
    winner_zone?: string;
    notes?: string;
    bracket_round?: string;
    is_highlighted?: boolean;
    event_type?: 'head_to_head' | 'group_event' | 'knockout_group' | 'category_based';
    participating_zones?: string | string[];
    special_teams?: string;
    detailed_scores?: string;
    zone_rankings?: string | any[];
    points_zone_a?: number;
    points_zone_b?: number;
    court_field?: string;
    sport: {
        id: number;
        name: string;
        logo_url?: string;
    };
    sports_schedule?: {
        id: number;
        schedule_name: string;
    };
}

export interface Sport {
    id: number;
    name: string;
}

export interface Zone {
    code: string;
    name: string;
}

export interface MatchesProps {
    matches: {
        data: Match[];
        links: any;
        meta: any;
    };
    sports: Sport[];
    zones: Zone[];
    filters: {
        sport?: string;
        status?: string;
        date?: string;
    };
}

export interface MatchFilters {
    searchTerm: string;
    statusFilter: string;
    sportFilter: string;
    dateFilter: string;
    venueFilter: string;
    zoneFilter: string;
}

export interface MatchStats {
    totalMatches: number;
    upcomingCount: number;
    ongoingCount: number;
    completedCount: number;
}
