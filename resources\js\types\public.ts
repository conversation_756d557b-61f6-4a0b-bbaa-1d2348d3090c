// Site Settings Interface
export interface SiteSettings {
    siteName: string;
    tagline: string;
    logoUrl?: string;
    faviconUrl?: string;
    copyrightText: string;
    contactEmail?: string;
    contactPhone?: string;
    address?: string;
    socialMedia?: {
        facebook?: string;
        instagram?: string;
        twitter?: string;
        youtube?: string;
        tiktok?: string;
    };
}

// Hero Section Interface
export interface HeroContent {
    title: string;
    subtitle: string;
    description: string;
    backgroundImage?: string;
    backgroundVideo?: string;
    showCountdown: boolean;
    countdownText?: string;
    countdownTargetDate?: string;
    primaryButtonText?: string;
    primaryButtonHref?: string;
    secondaryButtonText?: string;
    secondaryButtonHref?: string;
}

// Feature Interface
export interface Feature {
    id: string;
    title: string;
    description: string;
    icon: string;
    color: string;
    href?: string;
    isActive: boolean;
    sortOrder: number;
}

// Features Section Interface
export interface FeaturesContent {
    title: string;
    subtitle: string;
    features: Feature[];
}

// Sport Interface
export interface Sport {
    id: string;
    name: string;
    icon: string;
    description?: string;
    isActive: boolean;
    sortOrder: number;
    maxParticipants?: number;
    registrationDeadline?: string;
}

// Sports Categories Interface
export interface SportsContent {
    title: string;
    subtitle: string;
    sports: Sport[];
}

// Statistic Interface
export interface Statistic {
    id: string;
    label: string;
    value: string;
    icon: string;
    description?: string;
    isActive: boolean;
    sortOrder: number;
}

// Statistics Section Interface
export interface StatsContent {
    title: string;
    subtitle: string;
    backgroundImage?: string;
    stats: Statistic[];
}

// Call to Action Interface
export interface CTAContent {
    title: string;
    subtitle: string;
    description: string;
    backgroundImage?: string;
    primaryButtonText: string;
    primaryButtonHref: string;
    secondaryButtonText?: string;
    secondaryButtonHref?: string;
    isActive: boolean;
}

// News Article Interface
export interface NewsArticle {
    id: string;
    title: string;
    excerpt: string;
    content: string;
    featuredImage?: string;
    author: string;
    publishedAt: string;
    isPublished: boolean;
    tags?: string[];
    slug: string;
}

// News Section Interface
export interface NewsContent {
    title: string;
    subtitle: string;
    articles: NewsArticle[];
    showMoreLink?: string;
}

// Gallery Item Interface
export interface GalleryItem {
    id: string;
    title: string;
    description?: string;
    imageUrl: string;
    thumbnailUrl?: string;
    type: 'image' | 'video';
    category?: string;
    uploadedAt: string;
    isActive: boolean;
}

// Gallery Section Interface
export interface GalleryContent {
    title: string;
    subtitle: string;
    items: GalleryItem[];
    categories?: string[];
}

// Tournament Interface
export interface Tournament {
    id: string;
    name: string;
    sport: string;
    startDate: string;
    endDate: string;
    venue: string;
    status: 'upcoming' | 'ongoing' | 'completed';
    maxTeams?: number;
    registeredTeams?: number;
    description?: string;
    rules?: string;
    prizes?: string[];
}

// Tournaments Section Interface
export interface TournamentsContent {
    title: string;
    subtitle: string;
    tournaments: Tournament[];
}

// Page Content Interface (Main container)
export interface PageContent {
    siteSettings: SiteSettings;
    hero: HeroContent;
    features: FeaturesContent;
    sports: SportsContent;
    stats: StatsContent;
    cta: CTAContent;
    news?: NewsContent;
    gallery?: GalleryContent;
    tournaments?: TournamentsContent;
}

// Component Props Interfaces
export interface PublicHeaderProps {
    user?: any;
    siteSettings: SiteSettings;
}

export interface HeroSectionProps {
    user?: any;
    content: HeroContent;
}

export interface FeaturesGridProps {
    content: FeaturesContent;
}

export interface SportsCategoriesProps {
    content: SportsContent;
}

export interface StatsSectionProps {
    content: StatsContent;
}

export interface CTASectionProps {
    user?: any;
    content: CTAContent;
}

export interface PublicFooterProps {
    siteSettings: SiteSettings;
}

// API Response Interfaces
export interface PublicPageData {
    pageContent: PageContent;
    user?: any;
}

// Form Interfaces for Admin
export interface SiteSettingsForm {
    siteName: string;
    tagline: string;
    logoFile?: File;
    faviconFile?: File;
    copyrightText: string;
    contactEmail?: string;
    contactPhone?: string;
    address?: string;
    facebookUrl?: string;
    instagramUrl?: string;
    twitterUrl?: string;
    youtubeUrl?: string;
    tiktokUrl?: string;
}

export interface HeroContentForm {
    title: string;
    subtitle: string;
    description: string;
    backgroundImageFile?: File;
    backgroundVideoFile?: File;
    showCountdown: boolean;
    countdownText?: string;
    countdownTargetDate?: string;
    primaryButtonText?: string;
    primaryButtonHref?: string;
    secondaryButtonText?: string;
    secondaryButtonHref?: string;
}

export interface FeatureForm {
    title: string;
    description: string;
    icon: string;
    color: string;
    href?: string;
    isActive: boolean;
    sortOrder: number;
}

export interface SportForm {
    name: string;
    icon: string;
    description?: string;
    isActive: boolean;
    sortOrder: number;
    maxParticipants?: number;
    registrationDeadline?: string;
}

export interface StatisticForm {
    label: string;
    value: string;
    icon: string;
    description?: string;
    isActive: boolean;
    sortOrder: number;
}

// Validation Interfaces
export interface ValidationErrors {
    [key: string]: string[];
}

export interface FormResponse<T> {
    success: boolean;
    data?: T;
    errors?: ValidationErrors;
    message?: string;
}
