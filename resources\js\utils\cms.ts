/**
 * CMS Content Management Utilities
 * Helper functions for fetching dynamic content from CMS
 */

interface ContentResponse {
    content: string | null;
    page: string;
    section: string | null;
    key: string;
}



interface GeneralRule {
    id: number;
    rule_text: string;
    sort_order: number;
    is_active: boolean;
}

interface SportsRule {
    id: number;
    sport_name: string;
    category: string;
    icon_path?: string;
    color_class?: string;
    rules: string[];
    sort_order: number;
    is_active: boolean;
}

interface Penalty {
    id: number;
    offense: string;
    penalty_description: string;
    severity: 'low' | 'medium' | 'high';
    sort_order: number;
    is_active: boolean;
}

/**
 * Get content value by page, section, and key
 */
export async function getContent(page: string, section: string | null, key: string, fallback: string = ''): Promise<string> {
    try {
        const url = section 
            ? `/api/content/${page}/${section}/${key}`
            : `/api/content/${page}/null/${key}`;
            
        const response = await fetch(url);
        
        if (!response.ok) {
            return fallback;
        }

        const data: ContentResponse = await response.json();
        return data.content || fallback;
    } catch (error) {
        return fallback;
    }
}

/**
 * Get all content for a specific page
 */
export async function getPageContent(page: string): Promise<Record<string, any>> {
    try {
        const response = await fetch(`/api/content/${page}`);
        
        if (!response.ok) {
            return {};
        }

        return await response.json();
    } catch (error) {
        return {};
    }
}



/**
 * Get all general rules
 */
export async function getGeneralRules(): Promise<GeneralRule[]> {
    try {
        const response = await fetch('/api/rules/general');
        
        if (!response.ok) {
            return [];
        }

        return await response.json();
    } catch (error) {
        return [];
    }
}

/**
 * Get all sports rules
 */
export async function getSportsRules(): Promise<SportsRule[]> {
    try {
        const response = await fetch('/api/rules/sports');
        
        if (!response.ok) {
            return [];
        }

        return await response.json();
    } catch (error) {
        return [];
    }
}

/**
 * Get all penalties
 */
export async function getPenalties(): Promise<Penalty[]> {
    try {
        const response = await fetch('/api/penalties');
        
        if (!response.ok) {
            return [];
        }

        return await response.json();
    } catch (error) {
        return [];
    }
}

/**
 * React hook for fetching content with loading state
 */
import { useState, useEffect } from 'react';

export function useContent(page: string, section: string | null, key: string, fallback: string = '') {
    const [content, setContent] = useState<string>(fallback);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        let mounted = true;

        const fetchContent = async () => {
            try {
                setLoading(true);
                setError(null);
                const result = await getContent(page, section, key, fallback);
                
                if (mounted) {
                    setContent(result);
                }
            } catch (err) {
                if (mounted) {
                    setError(err instanceof Error ? err.message : 'Failed to fetch content');
                    setContent(fallback);
                }
            } finally {
                if (mounted) {
                    setLoading(false);
                }
            }
        };

        fetchContent();

        return () => {
            mounted = false;
        };
    }, [page, section, key, fallback]);

    return { content, loading, error };
}



/**
 * React hook for fetching rules with loading state
 */
export function useRules() {
    const [generalRules, setGeneralRules] = useState<GeneralRule[]>([]);
    const [sportsRules, setSportsRules] = useState<SportsRule[]>([]);
    const [penalties, setPenalties] = useState<Penalty[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        let mounted = true;

        const fetchRules = async () => {
            try {
                setLoading(true);
                setError(null);
                
                const [generalData, sportsData, penaltiesData] = await Promise.all([
                    getGeneralRules(),
                    getSportsRules(),
                    getPenalties()
                ]);
                
                if (mounted) {
                    setGeneralRules(generalData);
                    setSportsRules(sportsData);
                    setPenalties(penaltiesData);
                }
            } catch (err) {
                if (mounted) {
                    setError(err instanceof Error ? err.message : 'Failed to fetch rules');
                }
            } finally {
                if (mounted) {
                    setLoading(false);
                }
            }
        };

        fetchRules();

        return () => {
            mounted = false;
        };
    }, []);

    return { generalRules, sportsRules, penalties, loading, error };
}

/**
 * Utility function to get content with caching
 */
const contentCache = new Map<string, { data: string; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function getCachedContent(page: string, section: string | null, key: string, fallback: string = ''): Promise<string> {
    const cacheKey = `${page}/${section}/${key}`;
    const cached = contentCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.data;
    }
    
    const content = await getContent(page, section, key, fallback);
    contentCache.set(cacheKey, { data: content, timestamp: Date.now() });
    
    return content;
}

/**
 * Clear content cache
 */
export function clearContentCache(): void {
    contentCache.clear();
}
