/**
 * Image optimization utilities
 */

export interface ImageOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpg' | 'png';
}

/**
 * Get optimized image URL with proper cache strategy
 */
export function getOptimizedImageUrl(src: string, options: ImageOptions = {}): string {
  if (!src) return '';

  // If it's a storage URL (uploaded image), return as is - already optimized
  if (src.includes('/storage/')) {
    return src;
  }

  // If it's a public image, return as is
  if (src.startsWith('/images/') || src.startsWith('/public/')) {
    return src;
  }

  // For external URLs, return as is (could be enhanced with image proxy service)
  return src;
}

/**
 * Preload an image
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Preload multiple images
 */
export async function preloadImages(srcs: string[]): Promise<void> {
  try {
    await Promise.all(srcs.map(preloadImage));
  } catch (error) {

  }
}

/**
 * Get image dimensions
 */
export function getImageDimensions(src: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Check if image exists
 */
export function imageExists(src: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = src;
  });
}

/**
 * Get fallback image based on context
 */
export function getFallbackImage(context: 'logo' | 'hero' | 'sport' | 'zone' | 'general' = 'general'): string {
  const fallbacks = {
    logo: '/images/logo.png',
    hero: '/images/default.jpg',
    sport: '/images/logo.png',
    zone: '/images/zones/zona-logo.png',
    general: '/images/default.jpg'
  };

  return fallbacks[context] || fallbacks.general;
}

/**
 * Generate srcset for responsive images
 */
export function generateSrcSet(baseSrc: string, sizes: number[] = [400, 800, 1200, 1600]): string {
  if (!baseSrc.includes('/storage/')) {
    return baseSrc; // Can't generate srcset for non-storage images
  }

  // For now, return the base src. Could be enhanced with image resizing service
  return baseSrc;
}
