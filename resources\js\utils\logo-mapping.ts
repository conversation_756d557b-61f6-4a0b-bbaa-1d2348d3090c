/**
 * Utility functions for mapping sport names and zone codes to their respective logo images
 * Located in public/images/ directory
 */

// Sport logo mapping
export const getSportLogo = (sportName: string | undefined | null): string => {
    // Handle undefined, null, or empty values
    if (!sportName || typeof sportName !== 'string') {
        return '/images/default.jpg';
    }

    const sportMap: { [key: string]: string } = {
        // Badminton
        'badminton': '/images/badmintonpsd.png',

        // <PERSON><PERSON>
        'bola sepak': '/images/bolasepakpsd.png',
        'football': '/images/bolasepakpsd.png',

        // Bola Tampar
        'bola tampar': '/images/bolatamparpsd.png',
        'volleyball': '/images/bolatamparpsd.png',

        // Bola Jaring / Netball
        'bola jaring': '/images/netballpsd.png',
        'netball': '/images/netballpsd.png',

        // Ping Pong / Table Tennis
        'ping pong': '/images/pingpongpsd.png',
        'table tennis': '/images/pingpongpsd.png',

        // Larian / Running
        'lari': '/images/10x400mpsd.png',
        'larian': '/images/10x400mpsd.png',
        'lari-lari berganti-ganti': '/images/10x400mpsd.png',
        'lari berganti-ganti': '/images/10x400mpsd.png',
        '10x400m': '/images/10x400mpsd.png',
        'lari 10x400m': '/images/10x400mpsd.png',

        // Sukan Rakyat / Traditional Sports
        'sukan rakyat': '/images/sukanrakyatpsd.png',
        'traditional': '/images/sukanrakyatpsd.png',

        // Add more sport mappings as needed
    };

    const key = sportName.toLowerCase().trim();
    return sportMap[key] || '/images/default.jpg';
};

// Zone logo mapping
export const getZoneLogo = (zoneCode: string | undefined | null): string => {
    // Handle undefined, null, or empty values
    if (!zoneCode || typeof zoneCode !== 'string') {
        return '/images/zones/zona-logo.png';
    }

    const zoneMap: { [key: string]: string } = {
        // Zone 1
        '1': '/images/zones/zona-logo.png',
        'zone 1': '/images/zones/zona-logo.png',
        'zon 1': '/images/zones/zona-logo.png',

        // Zone 2
        '2': '/images/zones/zone-b-logo.png',
        'zone 2': '/images/zones/zone-b-logo.png',
        'zon 2': '/images/zones/zone-b-logo.png',

        // Zone 3
        '3': '/images/zones/zonec-logo.png',
        'zone 3': '/images/zones/zonec-logo.png',
        'zon 3': '/images/zones/zonec-logo.png',

        // Bakat KKD
        'bakat-kkd': '/images/zones/bakat-kkd-logo.png',
        'bakat kkd': '/images/zones/bakat-kkd-logo.png',
        'bakat': '/images/zones/bakat-kkd-logo.png',

        // Legacy support
        'a': '/images/zones/zona-logo.png',
        'b': '/images/zones/zone-b-logo.png',
        'c': '/images/zones/zonec-logo.png',
        // Add more zone mappings as needed
    };

    const key = zoneCode.toLowerCase().trim();
    return zoneMap[key] || '/images/zones/zona-logo.png';
};



// Available sports list (for reference)
export const AVAILABLE_SPORTS = [
    'Badminton',
    'Bola Sepak', 
    'Bola Tampar',
    'Netball',
    'Ping Pong',
    'Lari 10x400m',
    'Sukan Rakyat'
];

// Available zones list (for reference)
export const AVAILABLE_ZONES = [
    'A',
    'B', 
    'C',
    'Bakat KKD'
];
