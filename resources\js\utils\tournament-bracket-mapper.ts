/**
 * Tournament Bracket Data Mapper
 * Maps database SportMatch data to @g-loot/react-tournament-brackets format
 */

export interface DatabaseMatch {
    id: number;
    title: string;
    match_type: string;
    zone_a: string;
    zone_b: string;
    match_date: string;
    match_time: string;
    venue: string;
    status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
    bracket_round: string;
    bracket_position: number;
    is_highlighted?: boolean;
    score_zone_a?: number;
    score_zone_b?: number;
}

export interface BracketParticipant {
    id: string;
    resultText: string;
    isWinner: boolean;
    status: 'PLAYED' | 'NO_SHOW' | 'WALK_OVER' | 'NO_PARTY' | null;
    name: string;
}

export interface BracketMatch {
    id: number;
    name: string;
    nextMatchId: number | null;
    tournamentRoundText: string;
    startTime: string;
    state: 'NO_SHOW' | 'WALK_OVER' | 'NO_PARTY' | 'DONE' | 'SCORE_DONE';
    participants: BracketParticipant[];
}

/**
 * Map database status to library state
 */
export const mapMatchState = (status: string): BracketMatch['state'] => {
    switch (status) {
        case 'completed':
            return 'DONE';
        case 'ongoing':
            return 'SCORE_DONE';
        case 'cancelled':
            return 'NO_SHOW';
        case 'scheduled':
        default:
            return 'DONE'; // Default for display purposes
    }
};

/**
 * Get zone display name with proper formatting
 */
export const getZoneDisplayName = (zoneCode: string): string => {
    if (zoneCode === 'BAKAT') return 'BAKAT';
    if (zoneCode.startsWith('W')) return zoneCode; // Winner placeholders
    if (zoneCode === 'Zon X' || zoneCode === 'Zon Y') return zoneCode; // Placeholder zones
    return `Zon ${zoneCode}`;
};

/**
 * Create participant from zone data
 */
export const createParticipant = (
    zoneCode: string, 
    score?: number, 
    isWinner: boolean = false
): BracketParticipant => {
    return {
        id: `zone-${zoneCode}`,
        resultText: score !== undefined ? score.toString() : '',
        isWinner,
        status: score !== undefined ? 'PLAYED' : null,
        name: getZoneDisplayName(zoneCode)
    };
};

/**
 * Determine next match ID based on bracket structure
 */
export const determineNextMatchId = (
    currentMatch: DatabaseMatch,
    allMatches: DatabaseMatch[]
): number | null => {
    // For final matches, there's no next match
    if (currentMatch.bracket_round === 'Final') {
        return null;
    }

    // Find the next round match that this match feeds into
    const nextRoundMatches = allMatches.filter(match => {
        // Simple logic: next round has higher bracket_position or different round name
        return match.bracket_round !== currentMatch.bracket_round &&
               match.bracket_position > currentMatch.bracket_position;
    });

    // Return the first next round match (can be improved with more complex logic)
    return nextRoundMatches.length > 0 ? nextRoundMatches[0].id : null;
};

/**
 * Map single database match to bracket format
 */
export const mapDatabaseMatchToBracket = (
    dbMatch: DatabaseMatch,
    allMatches: DatabaseMatch[]
): BracketMatch => {
    const hasScores = dbMatch.score_zone_a !== undefined && dbMatch.score_zone_b !== undefined;
    const zoneAWins = hasScores && dbMatch.score_zone_a! > dbMatch.score_zone_b!;
    const zoneBWins = hasScores && dbMatch.score_zone_b! > dbMatch.score_zone_a!;

    return {
        id: dbMatch.id,
        name: dbMatch.title,
        nextMatchId: determineNextMatchId(dbMatch, allMatches),
        tournamentRoundText: dbMatch.bracket_round,
        startTime: `${dbMatch.match_date} ${dbMatch.match_time}`,
        state: mapMatchState(dbMatch.status),
        participants: [
            createParticipant(dbMatch.zone_a, dbMatch.score_zone_a, zoneAWins),
            createParticipant(dbMatch.zone_b, dbMatch.score_zone_b, zoneBWins)
        ]
    };
};

/**
 * Map array of database matches to bracket format
 */
export const mapDatabaseMatchesToBracket = (dbMatches: DatabaseMatch[]): BracketMatch[] => {
    return dbMatches.map(match => mapDatabaseMatchToBracket(match, dbMatches));
};

/**
 * Group matches by round for better organization
 */
export const groupMatchesByRound = (matches: BracketMatch[]): Record<string, BracketMatch[]> => {
    return matches.reduce((groups, match) => {
        const round = match.tournamentRoundText;
        if (!groups[round]) {
            groups[round] = [];
        }
        groups[round].push(match);
        return groups;
    }, {} as Record<string, BracketMatch[]>);
};

/**
 * Sort matches by bracket position within each round
 */
export const sortMatchesByPosition = (matches: BracketMatch[]): BracketMatch[] => {
    return matches.sort((a, b) => {
        // First sort by round (assuming certain order)
        const roundOrder = ['Round Robin', 'Quarter Final', 'Semi-Final', 'Final'];
        const aRoundIndex = roundOrder.indexOf(a.tournamentRoundText);
        const bRoundIndex = roundOrder.indexOf(b.tournamentRoundText);
        
        if (aRoundIndex !== bRoundIndex) {
            return aRoundIndex - bRoundIndex;
        }
        
        // Then sort by ID (which should correspond to bracket_position)
        return a.id - b.id;
    });
};

/**
 * Main function to convert database matches to tournament bracket format
 */
export const convertToTournamentBracket = (dbMatches: DatabaseMatch[]): BracketMatch[] => {
    const bracketMatches = mapDatabaseMatchesToBracket(dbMatches);
    return sortMatchesByPosition(bracketMatches);
};
