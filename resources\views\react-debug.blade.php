<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Debug - <PERSON>vel Blade</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script type="text/babel">
        function DebugApp() {
            const [tests, setTests] = React.useState([
                { name: 'React 18', status: 'success', message: 'React loaded successfully' },
                { name: 'Tailwind CSS', status: 'success', message: 'Styles applied via CDN' },
                { name: 'Laravel Blade', status: 'success', message: 'Served via Laravel route' },
                { name: 'Browser Compatibility', status: 'testing', message: 'Testing browser support...' }
            ]);

            React.useEffect(() => {
                // Test browser compatibility
                setTimeout(() => {
                    setTests(prev => prev.map(test => 
                        test.name === 'Browser Compatibility' 
                            ? { ...test, status: 'success', message: 'Browser supports React' }
                            : test
                    ));
                }, 1000);
            }, []);

            const getStatusColor = (status) => {
                switch(status) {
                    case 'success': return 'bg-green-100 text-green-800 border-green-300';
                    case 'error': return 'bg-red-100 text-red-800 border-red-300';
                    case 'testing': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
                    default: return 'bg-gray-100 text-gray-800 border-gray-300';
                }
            };

            return (
                <div className="min-h-screen p-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                            <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
                                🔍 React Debug - Laravel Blade
                            </h1>
                            
                            <div className="grid gap-4">
                                {tests.map((test, index) => (
                                    <div key={index} className={`p-4 rounded-lg border ${getStatusColor(test.status)}`}>
                                        <div className="flex items-center justify-between">
                                            <h3 className="font-semibold">{test.name}</h3>
                                            <span className="text-sm">
                                                {test.status === 'success' && '✅'}
                                                {test.status === 'error' && '❌'}
                                                {test.status === 'testing' && '🔄'}
                                            </span>
                                        </div>
                                        <p className="text-sm mt-1">{test.message}</p>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-lg p-6">
                            <h2 className="text-xl font-semibold text-gray-800 mb-4">
                                🔧 System Information
                            </h2>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div className="space-y-2">
                                    <p><strong>React Version:</strong> {React.version}</p>
                                    <p><strong>User Agent:</strong> {navigator.userAgent.slice(0, 50)}...</p>
                                    <p><strong>Screen:</strong> {screen.width}x{screen.height}</p>
                                    <p><strong>Viewport:</strong> {window.innerWidth}x{window.innerHeight}</p>
                                </div>
                                <div className="space-y-2">
                                    <p><strong>Platform:</strong> {navigator.platform}</p>
                                    <p><strong>Language:</strong> {navigator.language}</p>
                                    <p><strong>Online:</strong> {navigator.onLine ? 'Yes' : 'No'}</p>
                                    <p><strong>Touch Support:</strong> {'ontouchstart' in window ? 'Yes' : 'No'}</p>
                                </div>
                            </div>
                        </div>

                        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 className="font-semibold text-blue-800 mb-2">📋 Next Steps</h3>
                            <ul className="text-blue-700 text-sm space-y-1">
                                <li>• If this page works: Issue is with Vite/build configuration</li>
                                <li>• If this page crashes: Issue is with browser/system compatibility</li>
                                <li>• Compare this with Vite-built React app behavior</li>
                            </ul>
                        </div>
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<DebugApp />);
    </script>
</body>
</html>
