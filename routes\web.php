<?php

use Illuminate\Support\Facades\Route;

// CSRF token route for SPA
Route::get('/sanctum/csrf-cookie', function () {
    return response()->json(['message' => 'CSRF cookie set']);
});

// Storage route for file access
Route::get('/storage/{path}', function ($path) {
    $file = storage_path('app/public/' . $path);

    if (!file_exists($file)) {
        abort(404);
    }

    return response()->file($file);
})->where('path', '.*');

// Serve React SPA for all routes (except API routes)
Route::get('/{any}', function () {
    return view('app');
})->where('any', '^(?!api|storage).*$');


