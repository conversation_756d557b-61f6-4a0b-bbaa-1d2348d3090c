const fs = require('fs');
const path = require('path');

// Files that need to be updated
const filesToUpdate = [
    'resources/js/components/admin/page-content/sections/RulesSectionEditor.tsx',
    'resources/js/components/admin/page-content/sections/SportsSectionEditor.tsx',
    'resources/js/components/admin/page-content/sections/StatsSectionEditor.tsx',
    'resources/js/components/admin/page-content/sections/TournamentResultsSectionEditor.tsx'
];

function updateFile(filePath) {
    console.log(`📝 Updating ${filePath}...`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Check if already has isUploading
        if (content.includes('isUploading')) {
            console.log(`   ✅ Already has loading state`);
            return;
        }
        
        // 1. Add isUploading state after other useState declarations
        content = content.replace(
            /(\s+const \[validationErrors, setValidationErrors\] = useState<Record<string, string>>\(\{\}\);)/,
            '$1\n    const [isUploading, setIsUploading] = useState(false);'
        );
        
        // 2. Add setIsUploading(true) at start of handleImageSave
        content = content.replace(
            /(const handleImageSave = async \(\) => \{\s*try \{)/,
            'const handleImageSave = async () => {\n        setIsUploading(true);\n        try {'
        );
        
        // 3. Add finally block with setIsUploading(false)
        content = content.replace(
            /(\s+} catch \(error: any\) \{\s+toast\.error\(error\.response\?\.data\?\.message \|\| 'Failed to upload image'\);\s+}\s+};)/,
            '$1'.replace('};', '} finally {\n            setIsUploading(false);\n        }\n    };')
        );
        
        // 4. Update button to show loading state
        content = content.replace(
            /disabled=\{!imageFile\}/g,
            'disabled={!imageFile || isUploading}'
        );
        
        // 5. Update button content to show loading
        content = content.replace(
            /(<Button onClick={handleImageSave} disabled=\{!imageFile \|\| isUploading\}>\s*<Upload className="w-4 h-4 mr-2" \/>\s*Save Image\s*<\/Button>)/,
            `<Button onClick={handleImageSave} disabled={!imageFile || isUploading}>
                                        {isUploading ? (
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        ) : (
                                            <Upload className="w-4 h-4 mr-2" />
                                        )}
                                        {isUploading ? 'Uploading...' : 'Save Image'}
                                    </Button>`
        );
        
        fs.writeFileSync(filePath, content);
        console.log(`   ✅ Updated successfully`);
        
    } catch (error) {
        console.log(`   ❌ Error updating ${filePath}:`, error.message);
    }
}

// Update all files
filesToUpdate.forEach(updateFile);

console.log('\n✅ All files updated!');
